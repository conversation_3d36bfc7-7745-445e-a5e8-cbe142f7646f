{"root": true, "env": {"browser": true, "node": true, "es6": true}, "parser": "vue-eslint-parser", "extends": ["plugin:@typescript-eslint/recommended", "plugin:vue/vue3-recommended", "plugin:prettier/recommended"], "parserOptions": {"parser": "@typescript-eslint/parser", "ecmaVersion": "latest", "sourceType": "module", "jsxPragma": "React", "ecmaFeatures": {"jsx": true}}, "rules": {"@typescript-eslint/ban-types": "off", "@typescript-eslint/ban-ts-ignore": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-use-before-define": "off", "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "vue/attributes-order": "off", "vue/attribute-hyphenation": "off", "vue/v-on-event-hyphenation": "off", "vue/custom-event-name-casing": "off", "vue/multi-word-component-names": "off", "vue/no-setup-props-destructure": "off", "vue/script-setup-uses-vars": "error", "vue/one-component-per-file": "off", "vue/max-attributes-per-line": "off", "vue/html-closing-bracket-newline": "off", "vue/multiline-html-element-content-newline": "off", "vue/singleline-html-element-content-newline": "off", "vue/require-default-prop": "off", "vue/html-self-closing": ["error", {"html": {"void": "always", "normal": "never", "component": "always"}, "svg": "always", "math": "always"}], "no-use-before-define": "off", "space-before-function-paren": "off", "no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}]}}