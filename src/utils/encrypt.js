import CryptoJS from 'crypto-js';

const KEY = CryptoJS.enc.Utf8.parse('1234567890abcdef'); // 16字节密钥
const IV = CryptoJS.enc.Utf8.parse('abcdef1234567890');  // 16字节IV

// base64url 工具函数
function toBase64Url(str) {
  return str.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
}

function fromBase64Url(str) {
  let base64 = str.replace(/-/g, '+').replace(/_/g, '/');
  while (base64.length % 4) {
    base64 += '=';
  }
  return base64;
}

export function encryptParam(data) {
  const encrypted = CryptoJS.AES.encrypt(
    String(data),
    KEY,
    {
      iv: IV,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    }
  );
  // base64url 编码，保证结果只有数字、字母、-、_
  return toBase64Url(encrypted.toString());
}

export function decryptParam(encrypted) {
  // base64url 还原为标准base64
  const base64 = fromBase64Url(encrypted);
  const decrypted = CryptoJS.AES.decrypt(
    base64,
    KEY,
    {
      iv: IV,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    }
  );
  return decrypted.toString(CryptoJS.enc.Utf8);
}

