import request from '@/utils/request/server';

// 接口调用日志 VO
export interface ServerRequestLogVO {
  id: number; // 编号
  baseId: number; // 接口配置表id
  code: string; // 接口代码
  requestUrl: string; // 接口地址
  respData: string; // 接口描述
  deptId: number; // 部门id
}

// 接口调用日志 API
// 查询接口调用日志分页
export const getServerRequestLogPage = async (params: any) => {
  return await request.get({ url: `/wg/server-request-log/page`, params });
};

// 查询接口调用日志详情
export const getServerRequestLog = async (id: number) => {
  return await request.get({ url: `/wg/server-request-log/get?id=` + id });
};

// 新增接口调用日志
export const createServerRequestLog = async (data: ServerRequestLogVO) => {
  return await request.post({ url: `/wg/server-request-log/create`, data });
};

// 修改接口调用日志
export const updateServerRequestLog = async (data: ServerRequestLogVO) => {
  return await request.put({ url: `/wg/server-request-log/update`, data });
};

// 删除接口调用日志
export const deleteServerRequestLog = async (id: number) => {
  return await request.delete({
    url: `/wg/server-request-log/delete?id=` + id
  });
};

// 导出接口调用日志 Excel
export const exportServerRequestLog = async (params) => {
  return await request.download({
    url: `/wg/server-request-log/export-excel`,
    params
  });
};
