import request from '@/utils/request/server';

// 接口配置 VO
export interface ServerBaseVO {
  id: number; // 编号
  code: string; // 接口代码
  url: string; // 接口地址
  desc: string; // 接口描述
  msgCode: string; // 消息模版编码
  deptId: number; // 部门id
}

// 接口配置 API
// 查询接口配置分页
export const getServerBasePage = async (params: any) => {
  return await request.get({ url: `/wg/server-base/page`, params });
};

// 查询接口配置详情
export const getServerBase = async (id: number) => {
  return await request.get({ url: `/wg/server-base/get?id=` + id });
};

// 新增接口配置
export const createServerBase = async (data: ServerBaseVO) => {
  return await request.post({ url: `/wg/server-base/create`, data });
};

// 修改接口配置
export const updateServerBase = async (data: ServerBaseVO) => {
  return await request.put({ url: `/wg/server-base/update`, data });
};

// 删除接口配置
export const deleteServerBase = async (id: number) => {
  return await request.delete({ url: `/wg/server-base/delete?id=` + id });
};

// 导出接口配置 Excel
export const exportServerBase = async (params) => {
  return await request.download({
    url: `/wg/server-base/export-excel`,
    params
  });
};
