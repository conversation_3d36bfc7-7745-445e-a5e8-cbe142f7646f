import request from '@/utils/request';
import { mapTree } from 'sirius-platform-pro';
import { toFormData } from '@/utils/common';
import { API_BASE_URL } from '@/config/setting';

/**
 * 获取当前登录用户的菜单
 */
export async function getUserMenu(res) {
  const temp = res;
  // 一级菜单去掉父级
  temp.forEach((item, i) => {
    if (item.path === '/') {
      temp[i] = item.children[0];
    }
  });
  // 增加首页
  temp.unshift({
    path: '/index',
    component: 'index',
    meta: { title: '首页', icon: 'IconProHomeOutlined' }
  });
  // 增加个人中心
  temp.push({
    path: '/profile',
    component: 'profile',
    meta: {
      title: '个人中心',
      icon: 'UserOutlined',
      active: '/index',
      hide: true
    }
  });
  temp.push({
    path: '/notify-message',
    component: 'system/notify/my',
    meta: {
      title: '我的站内信',
      icon: 'UserOutlined',
      active: '/index',
      hide: true
    }
  });
  // 修改图标
  return mapTree(temp, (item) => {
    return {
      ...item,
      meta: {
        ...item.meta,
        icon: item.meta.icon
      }
    };
  });
}

/**
 * 修改当前登录用户的密码
 */
export async function updatePassword(data) {
  const res = await request.put(
    '/system/user/profile/updatePwd',
    toFormData(data)
  );
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}
