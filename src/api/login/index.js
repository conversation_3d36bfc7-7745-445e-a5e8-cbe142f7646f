import request from '@/utils/request/server';
/**
 * 登录
 */
export const login = (data) => {
  return request.post({ url: '/system/auth/login', data });
};
// 登出
export const loginOut = () => {
  return request.post({ url: '/system/auth/logout' });
};
// 刷新访问令牌
export const refreshToken = () => {
  return request.post({
    url: '/system/auth/refresh-token?refreshToken=' + getRefreshToken()
  });
};
// 使用租户名，获得租户编号
export const getTenantIdByName = (name) => {
  return request.get({ url: '/system/tenant/get-id-by-name?name=' + name });
};
// 使用租户域名，获得租户信息
export const getTenantByWebsite = (website) => {
  return request.get({
    url: '/system/tenant/get-by-website?website=' + website
  });
};
// 获取验证图片以及 token
export const getCode = (data) => {
  return request.postOriginal({ url: 'system/captcha/get', data });
};

// 滑动或者点选验证
export const reqCheck = (data) => {
  return request.postOriginal({ url: 'system/captcha/check', data });
};

// 获取用户权限信息
export const getInfo = () => {
  return request.get({ url: '/system/auth/get-permission-info' });
};
//获取登录验证码
export const sendSmsCode = (data) => {
  return request.post({ url: '/system/auth/send-sms-code', data });
};

// 短信验证码登录
export const smsLogin = (data) => {
  return request.post({ url: '/system/auth/sms-login', data });
};
// 通过短信重置密码
export const smsResetPassword = (data) => {
  return request.post({ url: '/system/auth/reset-password', data });
};
