import request from '@/utils/request/server';

// 消息发送配置 VO
export interface MsgSendServerVO {
  id: number; // id
  serverCode: string; // 消息代码
  serverName: string; // 消息名称
  sendType: number; // 发送类型 0实时 1定时
  sendCron: string; // cron表达式
  databaseId: number; // 数据集id
  status: number; // 开启状态（0正常 1停用）
  remark: string; // 描述
}

// 消息发送配置 API
// 查询消息发送配置分页
export const getMsgSendServerPage = async (params: any) => {
  return await request.get({ url: `/system/msg-send-server/page`, params });
};

// 查询消息发送配置详情
export const getMsgSendServer = async (id: number) => {
  return await request.get({ url: `/system/msg-send-server/get?id=` + id });
};

// 新增消息发送配置
export const createMsgSendServer = async (data: MsgSendServerVO) => {
  return await request.post({ url: `/system/msg-send-server/create`, data });
};

// 修改消息发送配置
export const updateMsgSendServer = async (data: MsgSendServerVO) => {
  return await request.put({ url: `/system/msg-send-server/update`, data });
};

// 删除消息发送配置
export const deleteMsgSendServer = async (id: number) => {
  return await request.delete({
    url: `/system/msg-send-server/delete?id=` + id
  });
};

// 导出消息发送配置 Excel
export const exportMsgSendServer = async (params) => {
  return await request.download({
    url: `/system/msg-send-server/export-excel`,
    params
  });
};
// 消息发送配置明细 VO
export interface MsgSendServerLineVO {
  id: number; // id
  serverId: number; // system_msg_send_server.id
  serverCode: string; // 消息代码
  typeCode: number; // 模版类型,0短信 1邮件 2企微
  templateCode: string; // 消息模板编码
  sendType: number; // 发送类型 0实时 1定时
  sendCron: string; // cron表达式
  databaseId: number; // 数据集id
  status: number; // 开启状态（0正常 1停用）
  remark: string; // 描述
}

// 消息发送配置明细 API
// 查询消息发送配置明细分页
export const getMsgSendServerLinePage = async (params: any) => {
  return await request.get({
    url: `/system/msg-send-server-line/page`,
    params
  });
};

// 查询消息发送配置明细详情
export const getMsgSendServerLine = async (id: number) => {
  return await request.get({
    url: `/system/msg-send-server-line/get?id=` + id
  });
};

// 新增消息发送配置明细
export const createMsgSendServerLine = async (data: MsgSendServerLineVO) => {
  return await request.post({
    url: `/system/msg-send-server-line/create`,
    data
  });
};

// 修改消息发送配置明细
export const updateMsgSendServerLine = async (data: MsgSendServerLineVO) => {
  return await request.put({
    url: `/system/msg-send-server-line/update`,
    data
  });
};

// 删除消息发送配置明细
export const deleteMsgSendServerLine = async (id: number) => {
  return await request.delete({
    url: `/system/msg-send-server-line/delete?id=` + id
  });
};

// 导出消息发送配置明细 Excel
export const exportMsgSendServerLine = async (params) => {
  return await request.download({
    url: `/system/msg-send-server-line/export-excel`,
    params
  });
};

// 查询消息发送配置明细详情
export const getMsgTmpMailList = async () => {
  return await request.get({
    url: `/system/mail-template/list`
  });
};

// 查询消息发送配置明细详情
export const getMsgTmpSmsList = async () => {
  return await request.get({
    url: `/system/sms-template/list`
  });
};

// 查询消息发送配置明细详情
export const getMsgTmpWxList = async () => {
  return await request.get({
    url: `/system/wx-msg-template/list`
  });
};
