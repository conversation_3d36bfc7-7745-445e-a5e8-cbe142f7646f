import request from '@/utils/request/server';

// 消息数据集头 VO
export interface MsgDatabaseVO {
  id: number; // 编号
  code: string; // 数据集编码
  name: string; // 数据集名称
  type: number; // 类型（0表 1数据库 2api）
  status: number; // 开启状态（0正常 1停用）
}

// 消息数据集头 API
// 查询消息数据集头分页
export const getMsgDatabasePage = async (params: any) => {
  return await request.get({ url: `/system/msg-database/page`, params });
};
export const getMsgDatabaseList = async (params: any) => {
  return await request.get({ url: `/system/msg-database/list`, params });
};
// 查询消息数据集头详情
export const getMsgDatabase = async (id: number) => {
  return await request.get({ url: `/system/msg-database/get?id=` + id });
};

// 新增消息数据集头
export const createMsgDatabase = async (data: MsgDatabaseVO) => {
  return await request.post({ url: `/system/msg-database/create`, data });
};

// 修改消息数据集头
export const updateMsgDatabase = async (data: MsgDatabaseVO) => {
  return await request.put({ url: `/system/msg-database/update`, data });
};

// 删除消息数据集头
export const deleteMsgDatabase = async (id: number) => {
  return await request.delete({ url: `/system/msg-database/delete?id=` + id });
};

// 导出消息数据集头 Excel
export const exportMsgDatabase = async (params) => {
  return await request.download({
    url: `/system/msg-database/export-excel`,
    params
  });
};
