import request from '@/utils/request/server';

// 数据集详细信息 VO
export interface MsgDatabaseDetailVO {
  id: number; // id
  databaseId: number; // 数据集头id
  username: string; // 用户账号，第三方账号
  realname: string; // 用户姓名
  qwAccount: string; // 企微账户
  wxAccount: string; // 微信账户
  email: string; // 用户邮箱
  mobile: string; // 手机号码
  sex: number; // 用户性别
  birthday: Date; // 生日
}

// 数据集详细信息 API
// 查询数据集详细信息分页
export const getMsgDatabaseDetailPage = async (params: any) => {
  return await request.get({ url: `/system/msg-database-detail/page`, params });
};

// 查询数据集详细信息详情
export const getMsgDatabaseDetail = async (id: number) => {
  return await request.get({ url: `/system/msg-database-detail/get?id=` + id });
};

// 新增数据集详细信息
export const createMsgDatabaseDetail = async (data: MsgDatabaseDetailVO) => {
  return await request.post({
    url: `/system/msg-database-detail/create`,
    data
  });
};

// 修改数据集详细信息
export const updateMsgDatabaseDetail = async (data: MsgDatabaseDetailVO) => {
  return await request.put({ url: `/system/msg-database-detail/update`, data });
};

// 删除数据集详细信息
export const deleteMsgDatabaseDetail = async (id: number) => {
  return await request.delete({
    url: `/system/msg-database-detail/delete?id=` + id
  });
};

// 导出数据集详细信息 Excel
export const exportMsgDatabaseDetail = async (params) => {
  return await request.download({
    url: `/system/msg-database-detail/export-excel`,
    params
  });
};
/**
 * 下载导入模板
 */
export const getImportemplate = () => {
  return request.download({
    url: '/system/msg-database-detail/get-import-template'
  });
};
