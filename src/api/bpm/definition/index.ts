import request from '@/utils/request/server';

export const getProcessDefinition = async (id?: string, key?: string) => {
  return await request.get({
    url: '/bpm/process-definition/get',
    params: { id, key }
  });
};

export const getProcessDefinitionPage = async (params) => {
  return await request.get({
    url: '/bpm/process-definition/page',
    params
  });
};

export const getProcessDefinitionList = async (params) => {
  return await request.get({
    url: '/bpm/process-definition/list',
    params
  });
};

export const getSimpleProcessDefinitionList = async () => {
  return await request.get({
    url: '/bpm/process-definition/simple-list'
  });
};
