import request from '@/utils/request/server';

// 播放列 VO
export interface TaiplaylistInfoVO {
  id: number // 地址ID
  code: string // 播放列表uuid
  name: string // 名称
  orientation: string // 方向
  model: string // 模型
  sort: string // 排序
  version: string // 版本
  contentVersion: string // 内容版本
  status: string // 组状态（10新建 20启用 30停用）
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
  atributeText1: string // 备用text字段1
  atributeText2: string // 备用text字段2
  atributeText3: string // 备用text字段3
  atributeText4: string // 备用text字段4
  atributeText5: string // 备用text字段5
}

// 播放列 API
  // 查询播放列分页
export const getTaiplaylistInfoPage = async (params: any) => {
    return await request.get({ url: `/information/taiplaylist-info/page`, params })
};

  // 查询播放列详情
export const getTaiplaylistInfo = async (id: number) => {
    return await request.get({ url: `/information/taiplaylist-info/get?id=` + id })
};

  // 新增播放列
export const createTaiplaylistInfo = async (data: TaiplaylistInfoVO) => {
    return await request.post({ url: `/information/taiplaylist-info/create`, data })
};

  // 修改播放列
export const updateTaiplaylistInfo = async (data: TaiplaylistInfoVO) => {
    return await request.put({ url: `/information/taiplaylist-info/update`, data })
};

  // 删除播放列
export const deleteTaiplaylistInfo = async (id: number) => {
    return await request.delete({ url: `/information/taiplaylist-info/delete?id=` + id })
};

  // 导出播放列 Excel
export const exportTaiplaylistInfo = async (params) => {
    return await request.download({ url: `/information/taiplaylist-info/export-excel`, params })
};
export const refreshPlaylistInfo = async () => {
  return await request.get({ url: `/external/information-playlists/interface_get_playlist` })
};

export const simplePlaylistInfo = async () => {
  return await request.get({ url: `/information/taiplaylist-info/simple-list` })
};

