import request from '@/utils/request/server';

// 设备管理 VO
export interface DeviceInfoVO {
  id: number; // 设备ID
  deviceCode: string; // 设备编码
  deviceName: string; // 设备描述
  spaceCode: string; // 空间编号
  spaceType: string; // 空间类型
  sort: number; // 显示顺序
  status: string; // 设备状态（10新建 20启用 30停用）
  atributeVarchar1: string; // 备用字段1
  atributeVarchar2: string; // 备用字段2
  atributeVarchar3: string; // 备用字段3
  atributeVarchar4: string; // 备用字段4
  atributeVarchar5: string; // 备用字段5
  atributeText1: string; // 备用text字段1
  atributeText2: string; // 备用text字段2
  atributeText3: string; // 备用text字段3
  atributeText4: string; // 备用text字段4
  atributeText5: string; // 备用text字段5
}

// 设备管理 API
export const DeviceAssignApi = {
  // 查询设备管理分页
  getDeviceInfoPage: async (params: any) => {
    return await request.get({
      url: `/information/device-assign/page`,
      params
    });
  },

  // 导出设备管理 Excel
  exportDeviceInfo: async (params) => {
    return await request.download({
      url: `/information/device-assign/export-excel`,
      params
    });
  },
  // 设备上线
  onlineDeviceInfo: async (id: number) => {
    return await request.put({
      url: `/information/device-assign/online?ids=` + id
    });
  },
  // 设备下线
  offlineDeviceInfo: async (id: number) => {
    return await request.put({
      url: `/information/device-assign/offline?ids=` + id
    });
  },

  // 获得设备分配行表-模板列表 - 分页
  getDeviceAssignTemplatePageByDeviceId: async (params: any) => {
    return await request.get({
      url: `/information/device-assign/template/page-by-device-id`,
      params
    });
  },

  // 分配设备分配行表-模板列表 - 更新
  setDeviceAssignTemplateListByDeviceId: async (data) => {
    return await request.post({
      url: `/information/device-assign/template/assign-template-id`,
      data
    });
  },

  // 删除设备分配行表-模板
  deleteDeviceAssignTemplate: async (data) => {
    return await request.post({
      url: `/information/device-assign/template/delete-assign-template`,
      data
    });
  },

  // 新增设备分配行表-模板
  addDeviceAssignTemplate: async (data) => {
    return await request.post({
      url: `/information/device-assign/template/add-assign-template`,
      data
    });
  },

  // 获取简易模板列表
  getSimpleTemplateList: async (params: any) => {
    return await request.get({
      url: `/information/device-assign/template/simple-page`,
      params
    });
  }
};
