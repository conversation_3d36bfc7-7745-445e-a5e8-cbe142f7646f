import request from '@/utils/request/server';

// 设备管理 VO
export interface DeviceInfoVO {
  id: number // 设备ID
  deviceCode: string // 设备编码
  deviceName: string // 设备描述
  spaceCode: string // 空间编号
  spaceType: string // 空间类型
  sort: number // 显示顺序
  status: string // 设备状态（10新建 20启用 30停用）
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
  atributeText1: string // 备用text字段1
  atributeText2: string // 备用text字段2
  atributeText3: string // 备用text字段3
  atributeText4: string // 备用text字段4
  atributeText5: string // 备用text字段5
}

// 设备管理 API
  // 查询设备管理分页
export const getDeviceInfoPage = async (params: any) => {
    return await request.get({ url: `/information/device-info/page`, params })
};

  // 查询设备管理详情
export const getDeviceInfo = async (id: number) => {
    return await request.get({ url: `/information/device-info/get?id=` + id })
};

  // 新增设备管理
export const createDeviceInfo = async (data: DeviceInfoVO) => {
    return await request.post({ url: `/information/device-info/create`, data })
};

  // 修改设备管理
export const updateDeviceInfo = async (data: DeviceInfoVO) => {
    return await request.put({ url: `/information/device-info/update`, data })
};

  // 删除设备管理
export const deleteDeviceInfo = async (id: number) => {
    return await request.delete({ url: `/information/device-info/delete?id=` + id })
};

  // 导出设备管理 Excel
export const exportDeviceInfo = async (params) => {
    return await request.download({ url: `/information/device-info/export-excel`, params })
};

// 查询空间精简信息
export const getSpaceInfo = async () => {
  return await request.get({ url: `/external/system-space/simple-list`})
};

// 启用设备
export const enabledDeviceInfo = async (id: number) => {
  return await request.put({ url: `/information/device-info/enabled?ids=` + id })
};
// 停用设备
export const disabledDeviceInfo = async (id: number) => {
  return await request.put({ url: `/information/device-info/disabled?ids=` + id })
};