import request from '@/utils/request/server';

// 模板管理 VO
export interface TemplateInfoVO {
  id: number // 模板ID
  templateCode: string // 模板编码
  templateName: string // 模板描述
  canvas: string // 画布
  content: string // 内容
  sort: number // 显示顺序
  status: string // 模板状态（10新建 20启用 30停用）
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
  atributeText1: string // 备用text字段1
  atributeText2: string // 备用text字段2
  atributeText3: string // 备用text字段3
  atributeText4: string // 备用text字段4
  atributeText5: string // 备用text字段5
}

// 模板管理 API
  // 查询模板管理分页
export const getTemplateInfoPage = async (params: any) => {
    return await request.get({ url: `/information/template-info/page`, params })
};

  // 查询模板管理详情
export const getTemplateInfo = async (id: number) => {
    return await request.get({ url: `/information/template-info/get?id=` + id })
};

  // 新增模板管理
export const createTemplateInfo = async (data: TemplateInfoVO) => {
    return await request.post({ url: `/information/template-info/create`, data })
};

  // 修改模板管理
export const updateTemplateInfo = async (data: TemplateInfoVO) => {
    return await request.put({ url: `/information/template-info/update`, data })
};

  // 删除模板管理
export const deleteTemplateInfo = async (id: number) => {
    return await request.delete({ url: `/information/template-info/delete?id=` + id })
};

  // 导出模板管理 Excel
export const exportTemplateInfo = async (params) => {
    return await request.download({ url: `/information/template-info/export-excel`, params })
};

// 启用模板
export const enabledTemplateInfo = async (id: number) => {
  return await request.put({ url: `/information/template-info/enabled?ids=` + id })
};
// 停用模板
export const disabledTemplateInfo = async (id: number) => {
  return await request.put({ url: `/information/template-info/disabled?ids=` + id })
};
