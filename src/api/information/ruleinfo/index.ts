import request from '@/utils/request/server';

// 规则管理 VO
export interface RuleInfoVO {
  id: number // 规则ID
  ruleCode: string // 规则编码
  ruleName: string // 规则描述
  releaseDate: Date // 发布时间
  releaseFrequency: number // 发布频率(s/秒)
  loopPlayback: boolean // 循环播放(0是，1否)
  sort: number // 显示顺序
  status: string // 规则状态（10新建 20启用 30停用）
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
  atributeText1: string // 备用text字段1
  atributeText2: string // 备用text字段2
  atributeText3: string // 备用text字段3
  atributeText4: string // 备用text字段4
  atributeText5: string // 备用text字段5
}

// 规则管理 API
  // 查询规则管理分页
export const getRuleInfoPage = async (params: any) => {
    return await request.get({ url: `/information/rule-info/page`, params })
};

  // 查询规则管理详情
export const getRuleInfo = async (id: number) => {
    return await request.get({ url: `/information/rule-info/get?id=` + id })
};

  // 新增规则管理
export const createRuleInfo = async (data: RuleInfoVO) => {
    return await request.post({ url: `/information/rule-info/create`, data })
};

  // 修改规则管理
export const updateRuleInfo = async (data: RuleInfoVO) => {
    return await request.put({ url: `/information/rule-info/update`, data })
};

  // 删除规则管理
export const deleteRuleInfo = async (id: number) => {
    return await request.delete({ url: `/information/rule-info/delete?id=` + id })
};

  // 导出规则管理 Excel
export const exportRuleInfo = async (params) => {
    return await request.download({ url: `/information/rule-info/export-excel`, params })
};

// 启用规则
export const enabledRuleInfo = async (id: number) => {
  return await request.put({ url: `/information/rule-info/enabled?ids=` + id })
};
// 停用规则
export const disabledRuleInfo = async (id: number) => {
  return await request.put({ url: `/information/rule-info/disabled?ids=` + id })
};

// 查询规则（精简)列表
export const getSimpleRuleList = async (): Promise<RuleInfoVO[]> => {
  return await request.get({ url: '/information/rule-info/simple-list' })
}