import request from '@/utils/request/server';

// 标签管理 VO
export interface LabelInfoVO {
  id: number // 标签ID
  labelCode: string // 标签编码
  labelName: string // 标签描述
  parentId: number // 父标签id
  sort: number // 显示顺序
  status: string // 标签状态（10新建 20启用 30停用）
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
  atributeText1: string // 备用text字段1
  atributeText2: string // 备用text字段2
  atributeText3: string // 备用text字段3
  atributeText4: string // 备用text字段4
  atributeText5: string // 备用text字段5
}

// 标签管理 API
  // 查询标签管理列表
export const getLabelInfoList = async (params) => {
    return await request.get({ url: `/information/label-info/list`, params })
};

  // 查询标签管理详情
export const getLabelInfo = async (id: number) => {
    return await request.get({ url: `/information/label-info/get?id=` + id })
};

  // 新增标签管理
export const createLabelInfo = async (data: LabelInfoVO) => {
    return await request.post({ url: `/information/label-info/create`, data })
};

  // 修改标签管理
export const updateLabelInfo = async (data: LabelInfoVO) => {
    return await request.put({ url: `/information/label-info/update`, data })
};

  // 删除标签管理
export const deleteLabelInfo = async (id: number) => {
    return await request.delete({ url: `/information/label-info/delete?id=` + id })
};

  // 导出标签管理 Excel
export const exportLabelInfo = async (params) => {
    return await request.download({ url: `/information/label-info/export-excel`, params })
};

// 查询标签（精简)列表
export const getSimpleLabelList = async (): Promise<LabelInfoVO[]> => {
  return await request.get({ url: '/information/label-info/simple-list' })
}

// 启用标签
export const enabledLabelInfo = async (id: number) => {
  return await request.put({ url: `/information/label-info/enabled?ids=` + id })
};
// 停用标签
export const disabledLabelInfo = async (id: number) => {
  return await request.put({ url: `/information/label-info/disabled?ids=` + id })
};