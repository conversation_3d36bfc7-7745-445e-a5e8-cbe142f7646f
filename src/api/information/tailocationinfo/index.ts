import request from '@/utils/request/server';

// 地址管理 VO
export interface TailocationInfoVO {
  id: number // 地址ID
  code: string // 地址id
  name: string // 名称
  address: string // 地址
  status: string // 组状态（10新建 20启用 30停用）
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
  atributeText1: string // 备用text字段1
  atributeText2: string // 备用text字段2
  atributeText3: string // 备用text字段3
  atributeText4: string // 备用text字段4
  atributeText5: string // 备用text字段5
}

// 地址管理 API
  // 查询地址管理分页
export const getTailocationInfoPage = async (params: any) => {
    return await request.get({ url: `/information/tailocation-info/page`, params })
};

  // 查询地址管理详情
export const getTailocationInfo = async (id: number) => {
    return await request.get({ url: `/information/tailocation-info/get?id=` + id })
};

  // 新增地址管理
export const createTailocationInfo = async (data: TailocationInfoVO) => {
    return await request.post({ url: `/information/tailocation-info/create`, data })
};

  // 修改地址管理
export const updateTailocationInfo = async (data: TailocationInfoVO) => {
    return await request.put({ url: `/information/tailocation-info/update`, data })
};

  // 删除地址管理
export const deleteTailocationInfo = async (id: number) => {
    return await request.delete({ url: `/information/tailocation-info/delete?id=` + id })
};

  // 导出地址管理 Excel
export const exportTailocationInfo = async (params) => {
    return await request.download({ url: `/information/tailocation-info/export-excel`, params })
};

export const refreshLocationInfo = async () => {
  return await request.get({ url: `/external/information-locations/interface_get_location` })
};

export const simpleLocationInfo = async () => {
  return await request.get({ url: `/information/tailocation-info/simple-list` })
};

