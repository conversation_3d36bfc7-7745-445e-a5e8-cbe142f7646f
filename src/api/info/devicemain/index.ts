import request from '@/utils/request/server';

// 设备信息 VO
export interface DeviceMainVO {
          id: number // id
          deviceCode: string // 设备编码
          deviceName: string // 设备名称
          spaceId: number // 空间id
          spaceCode: string // 空间编码
          spaceName: string // 空间名称
          sourceCode: string // 来源，默认-MANUAL-手动创建
          deptId: number // 部门ID
          sort: number // 显示顺序
          externalId: string // 外部设备id
          status: number // 设备状态 1启用，0禁用
          atributeVarchar1: string // 备用字段1
          atributeVarchar2: string // 备用字段2
          atributeVarchar3: string // 备用字段3
          atributeVarchar4: string // 备用字段4
          atributeVarchar5: string // 备用字段5
}

// 设备信息 API
// 查询设备信息分页
export const getDeviceMainPage = async (params: any) => {
  return await request.get({ url: `/info/device-main/page`, params })
};

// 查询设备信息详情
export const getDeviceMain = async (id: number) => {
  return await request.get({ url: `/info/device-main/get?id=` + id })
};

// 新增设备信息
export const createDeviceMain = async (data: DeviceMainVO) => {
  return await request.post({ url: `/info/device-main/create`, data })
};

// 修改设备信息
export const updateDeviceMain = async (data: DeviceMainVO) => {
  return await request.put({ url: `/info/device-main/update`, data })
};

// 删除设备信息
export const deleteDeviceMain = async (id: number) => {
  return await request.delete({ url: `/info/device-main/delete?id=` + id })
};

// 导出设备信息 Excel
export const exportDeviceMain = async (params) => {
  return await request.download({ url: `/info/device-main/export-excel`, params })
};
