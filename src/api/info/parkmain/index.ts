import request from '@/utils/request/server';

// 园区信息 VO
export interface ParkMainVO {
          id: number // id
          parkCode: string // 园区编码
          parkName: string // 园区名称
          sourceCode: string // 来源，默认-MANUAL-手动创建
          deptId: number // 部门ID
          provincesId: number // 省id
          provincesCode: string // 省编码
          provincesName: string // 省名称
          cityId: number // 市id
          cityCode: string // 市编码
          cityName: string // 市名称
          districtId: number // 区id
          districtCode: string // 区编码
          districtName: string // 区名称
          townId: number // 镇id
          townCode: string // 镇编码
          townName: string // 镇名称
          proviceCityArea: string // 省市区
          addressDetail: string // 详细地址
          parkArea: number // 园区面积
          buildingArea: number // 建筑面积
          certificateArea: number // 产证面积
          leaseArea: number // 租赁面积
          saleArea: number // 销售面积
          parkingLot: number // 车位个数
          greeningRate: number // 绿化率
          monitoringCoverage: number // 监控覆盖率
          developers: string // 开发商
          contractor: string // 承建商
          status: number // 园区状态 1启用，0禁用
          atributeVarchar1: string // 备用字段1
          atributeVarchar2: string // 备用字段2
          atributeVarchar3: string // 备用字段3
          atributeVarchar4: string // 备用字段4
          atributeVarchar5: string // 备用字段5
}

// 园区信息 API
// 查询园区信息分页
export const getParkMainPage = async (params: any) => {
  return await request.get({ url: `/info/park-main/page`, params })
};

// 查询园区信息详情
export const getParkMain = async (id: number) => {
  return await request.get({ url: `/info/park-main/get?id=` + id })
};

// 新增园区信息
export const createParkMain = async (data: ParkMainVO) => {
  return await request.post({ url: `/info/park-main/create`, data })
};

// 修改园区信息
export const updateParkMain = async (data: ParkMainVO) => {
  return await request.put({ url: `/info/park-main/update`, data })
};

// 删除园区信息
export const deleteParkMain = async (id: number) => {
  return await request.delete({ url: `/info/park-main/delete?id=` + id })
};

// 导出园区信息 Excel
export const exportParkMain = async (params) => {
  return await request.download({ url: `/info/park-main/export-excel`, params })
};
