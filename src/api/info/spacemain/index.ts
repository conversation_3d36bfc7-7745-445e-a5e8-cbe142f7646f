import request from '@/utils/request/server';

// 空间信息 VO
export interface SpaceMainVO {
  id: number; // id
  spaceCode: string; // 空间编码
  spaceName: string; // 空间名称
  parkId: number; // id
  parkCode: string; // 园区编码
  parkName: string; // 园区名称
  proviceCityArea: string; // 省市区
  addressDetail: string; // 详细地址
  parentId: number; // 上级空间id，为0表示顶级空间
  parentCode: string; // 上级编码
  parentName: string; // 上级名称
  sourceCode: string; // 来源，默认-MANUAL-手动创建
  deptId: number; // 部门ID
  floorCount: number; // 楼层数
  buildingHeight: number; // 楼高
  totalArea: number; // 建筑面积
  certificateArea: number; // 产证面积
  rentalArea: number; // 出租面积
  completionDate: Date; // 竣工日期
  purpose: string; // 用途
  status: number; // 空间状态 1启用，0禁用
  atributeVarchar1: string; // 备用字段1
  atributeVarchar2: string; // 备用字段2
  atributeVarchar3: string; // 备用字段3
  atributeVarchar4: string; // 备用字段4
  atributeVarchar5: string; // 备用字段5
}

// 空间信息 API
// 查询空间信息分页
export const getSpaceMainPage = async (params: any) => {
  return await request.get({ url: `/info/space-main/page`, params });
};

// 查询空间信息详情
export const getSpaceMain = async (id: number) => {
  return await request.get({ url: `/info/space-main/get?id=` + id });
};

// 新增空间信息
export const createSpaceMain = async (data: SpaceMainVO) => {
  return await request.post({ url: `/info/space-main/create`, data });
};

// 修改空间信息
export const updateSpaceMain = async (data: SpaceMainVO) => {
  return await request.put({ url: `/info/space-main/update`, data });
};

// 删除空间信息
export const deleteSpaceMain = async (id: number) => {
  return await request.delete({ url: `/info/space-main/delete?id=` + id });
};

// 导出空间信息 Excel
export const exportSpaceMain = async (params) => {
  return await request.download({
    url: `/info/space-main/export-excel`,
    params
  });
};
// 查询空间信息分页
export const getSpaceMainList = async (params: any) => {
  return await request.get({ url: `/info/space-main/list`, params });
};

// 查询空间信息分页
export const getSpaceMainSimpleList = async (params: any) => {
  return await request.get({
    url: `/info/space-main/list-all-simple`,
    params
  });
};

// 启用空间信息
export const enabledSpaceMain = async (data: SpaceMainVO) => {
  return await request.post({ url: `/info/space-main/enable`, data });
};

// 禁用空间信息
export const disabledSpaceMain = async (data: SpaceMainVO) => {
  return await request.post({ url: `/info/space-main/disable`, data });
};
