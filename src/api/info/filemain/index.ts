import request from '@/utils/request/server';

// 素材管理 VO
export interface FileMainVO {
  id: number; // id
  fileId: number; // 文件表id
  name: string; // 文件名
  path: string; // 路径
  size: number; // 文件大小
  contentType: string; // 文件类型
  url: string; // 文件 URL
  isDirectory: number; // 是否是文件夹，1-是
  parentId: number; // 上级文件id
  deptId: number; // 部门ID
  status: number; // 状态 1启用，0禁用
  atributeVarchar1: string; // 备用字段1
  atributeVarchar2: string; // 备用字段2
  atributeVarchar3: string; // 备用字段3
  atributeVarchar4: string; // 备用字段4
  atributeVarchar5: string; // 备用字段5
}

// 素材管理 API
// 查询素材管理分页
export const getFileMainPage = async (params: any) => {
  return await request.get({ url: `/info/file-main/page`, params });
};

// 查询素材管理详情
export const getFileMain = async (id: number) => {
  return await request.get({ url: `/info/file-main/get?id=` + id });
};

// 新增素材管理
export const createFileMain = async (data: FileMainVO) => {
  return await request.post({ url: `/info/file-main/create`, data });
};

// 修改素材管理
export const updateFileMain = async (data: FileMainVO) => {
  return await request.put({ url: `/info/file-main/update`, data });
};

// 删除素材管理
export const deleteFileMain = async (id: number) => {
  return await request.delete({ url: `/info/file-main/delete?id=` + id });
};

// 删除素材管理
export const deleteFileList = async (id: number) => {
  return await request.delete({ url: `/info/file-main/delete-list?ids=` + id });
};

// 导出素材管理 Excel
export const exportFileMain = async (params) => {
  return await request.download({
    url: `/info/file-main/export-excel`,
    params
  });
};

// 查询国家地区信息分页
export const getFileList = async (params: any) => {
  return await request.get({ url: `/info/file-main/list`, params });
};

// 修改素材管理
export const updateFileParent = async (data: FileMainVO) => {
  return await request.put({ url: `/info/file-main/update-parent`, data });
};

// 修改素材管理
export const updateFileName = async (data: FileMainVO) => {
  return await request.put({ url: `/info/file-main/update-name`, data });
};
