import request from '@/utils/request/server';

// 国家地区信息 VO
export interface CountryAreaVO {
  id: number; // id
  areaCode: string; // 地区编码
  areaName: string; // 地区名称
  sourceCode: string; // 来源，默认-MANUAL-手动创建
  parentId: number; // 上级地区id，国家层级为空
  level: number; // 级别 0-国家，1-省，2-市，3-区，4-街道&镇
  levelIdPath: string; // 级别id路径 以|分割
  levelCodePath: string; // 级别code路径 以|分割
  deptId: number; // 部门ID
  status: number; // 状态 1启用，0禁用
  atributeVarchar1: string; // 备用字段1
  atributeVarchar2: string; // 备用字段2
  atributeVarchar3: string; // 备用字段3
  atributeVarchar4: string; // 备用字段4
  atributeVarchar5: string; // 备用字段5
}

// 国家地区信息 API
// 查询国家地区信息分页
export const getCountryAreaPage = async (params: any) => {
  return await request.get({ url: `/info/country-area/page`, params });
};

// 查询国家地区信息详情
export const getCountryArea = async (id: number) => {
  return await request.get({ url: `/info/country-area/get?id=` + id });
};

// 新增国家地区信息
export const createCountryArea = async (data: CountryAreaVO) => {
  return await request.post({ url: `/info/country-area/create`, data });
};

// 修改国家地区信息
export const updateCountryArea = async (data: CountryAreaVO) => {
  return await request.put({ url: `/info/country-area/update`, data });
};

// 删除国家地区信息
export const deleteCountryArea = async (id: number) => {
  return await request.delete({ url: `/info/country-area/delete?id=` + id });
};

// 导出国家地区信息 Excel
export const exportCountryArea = async (params) => {
  return await request.download({
    url: `/info/country-area/export-excel`,
    params
  });
};

// 查询国家地区信息分页
export const getCountryAreaList = async (params: any) => {
  return await request.get({ url: `/info/country-area/list`, params });
};

// 查询国家地区信息分页
export const getCountryAreaSimpleList = async (params: any) => {
  return await request.get({
    url: `/info/country-area/list-all-simple`,
    params
  });
};

// 启用国家地区信息
export const enabledCountryArea = async (data: CountryAreaVO) => {
  return await request.post({ url: `/info/country-area/enable`, data });
};

// 禁用国家地区信息
export const disabledCountryArea = async (data: CountryAreaVO) => {
  return await request.post({ url: `/info/country-area/disable`, data });
};
