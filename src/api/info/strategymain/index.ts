import request from '@/utils/request/server';
import type { Dayjs } from 'dayjs';

// 策略行信息 VO
export interface StrategyPlayVO {
  id: number; // id
  strategyId: number; // 策略id
  playId: number; // 播放列表id
  playCode: string; // 播放编码
  playName: string; // 播放名称
  strategyType: number; // 策略类型 0-定时，1-循环，3-长期
  startTime: string | Dayjs; // 开始时间
  endTime: string | Dayjs; // 结束时间
  interval?: number; // 播放间隔
  sort: number; // 优先级
  status?: number; // 策略行状态 1启用，0禁用
  atributeVarchar1: string; // 备用字段1
  atributeVarchar2: string; // 备用字段2
  atributeVarchar3: string; // 备用字段3
  atributeVarchar4: string; // 备用字段4
  atributeVarchar5: string; // 备用字段5
}

// 策略信息 VO
export interface StrategyMainVO {
  id: number; // id
  strategyCode: string; // 策略编码
  strategyName: string; // 策略名称
  sourceCode: string; // 来源，默认-MANUAL-手动创建
  deptId: number; // 部门ID
  sort: number; // 显示顺序
  status?: number; // 策略状态 1启用，0禁用
  atributeVarchar1: string; // 备用字段1
  atributeVarchar2: string; // 备用字段2
  atributeVarchar3: string; // 备用字段3
  atributeVarchar4: string; // 备用字段4
  atributeVarchar5: string; // 备用字段5
  strategyplays?: StrategyPlayVO[];
}

// 策略信息 API
// 查询策略信息分页
export const getStrategyMainPage = async (params: any) => {
  return await request.get({ url: `/info/strategy-main/page`, params });
};

// 查询策略信息详情
export const getStrategyMain = async (id: number) => {
  return await request.get({ url: `/info/strategy-main/get?id=` + id });
};

// 新增策略信息
export const createStrategyMain = async (data: StrategyMainVO) => {
  return await request.post({ url: `/info/strategy-main/create`, data });
};

// 修改策略信息
export const updateStrategyMain = async (data: StrategyMainVO) => {
  return await request.put({ url: `/info/strategy-main/update`, data });
};

// 删除策略信息
export const deleteStrategyMain = async (id: number) => {
  return await request.delete({ url: `/info/strategy-main/delete?id=` + id });
};

// 批量删除策略信息
export const deleteStrategyMainList = async (ids: number[]) => {
  return await request.delete({
    url: `/info/strategy-main/delete-list?ids=${ids.join(',')}`
  });
};

// 导出策略信息 Excel
export const exportStrategyMain = async (params) => {
  return await request.download({
    url: `/info/strategy-main/export-excel`,
    params
  });
};

// ==================== 子表（策略行） ====================

// 获得策略行列表
export const getStrategyPlayListByStrategyId = async (strategyId) => {
  return await request.get({
    url:
      `/info/strategy-main/strategy-play/list-by-strategy-id?strategyId=` +
      strategyId
  });
};
