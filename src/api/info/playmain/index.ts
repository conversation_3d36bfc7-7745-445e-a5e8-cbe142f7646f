import request from '@/utils/request/server';

// 播放信息 VO
export interface PlayMainVO {
  id: number; // id
  playCode: string; // 播放编码
  playName: string; // 播放名称
  playType: number; // 播放类型 0-广告，1-宣传
  sourceCode: string; // 来源，默认-MANUAL-手动创建
  deptId: number; // 部门ID
  sort: number; // 显示顺序
  status: number; // 播放状态 1启用，0禁用
  atributeVarchar1: string; // 备用字段1
  atributeVarchar2: string; // 备用字段2
  atributeVarchar3: string; // 备用字段3
  atributeVarchar4: string; // 备用字段4
  atributeVarchar5: string; // 备用字段5
}

// 播放信息 API
// 查询播放信息分页
export const getPlayMainPage = async (params: any) => {
  return await request.get({ url: `/info/play-main/page`, params });
};

// 查询播放信息详情
export const getPlayMain = async (id: number) => {
  return await request.get({ url: `/info/play-main/get?id=` + id });
};

// 新增播放信息
export const createPlayMain = async (data: PlayMainVO) => {
  return await request.post({ url: `/info/play-main/create`, data });
};

// 修改播放信息
export const updatePlayMain = async (data: PlayMainVO) => {
  return await request.put({ url: `/info/play-main/update`, data });
};

// 删除播放信息
export const deletePlayMain = async (id: number) => {
  return await request.delete({ url: `/info/play-main/delete?id=` + id });
};

// 导出播放信息 Excel
export const exportPlayMain = async (params) => {
  return await request.download({
    url: `/info/play-main/export-excel`,
    params
  });
};

// 查询播放列表
export const getPlayMainSimpleList = async (params: any) => {
  return await request.get({
    url: `/info/play-main/list-all-simple`,
    params
  });
};
