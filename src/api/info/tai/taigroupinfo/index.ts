import request from '@/utils/request/server';

// 组管理 VO
export interface TaigroupInfoVO {
  id: number // 组ID
  code: string // 组id
  name: string // 组描述
  status: string // 组状态（10新建 20启用 30停用）
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
  atributeText1: string // 备用text字段1
  atributeText2: string // 备用text字段2
  atributeText3: string // 备用text字段3
  atributeText4: string // 备用text字段4
  atributeText5: string // 备用text字段5
}

// 组管理 API
  // 查询组管理分页
export const getTaigroupInfoPage = async (params: any) => {
    return await request.get({ url: `/information/taigroup-info/page`, params })
};

  // 查询组管理详情
export const getTaigroupInfo = async (id: number) => {
    return await request.get({ url: `/information/taigroup-info/get?id=` + id })
};

  // 新增组管理
export const createTaigroupInfo = async (data: TaigroupInfoVO) => {
    return await request.post({ url: `/information/taigroup-info/create`, data })
};

  // 修改组管理
export const updateTaigroupInfo = async (data: TaigroupInfoVO) => {
    return await request.put({ url: `/information/taigroup-info/update`, data })
};

  // 删除组管理
export const deleteTaigroupInfo = async (id: number) => {
    return await request.delete({ url: `/information/taigroup-info/delete?id=` + id })
};

  // 导出组管理 Excel
export const exportTaigroupInfo = async (params) => {
    return await request.download({ url: `/information/taigroup-info/export-excel`, params })
};
export const refreshGroupInfo = async () => {
  return await request.get({ url: `/external/information-groups/interface_get_group` })
};

export const simpleGroupInfo = async () => {
  return await request.get({ url: `/information/taigroup-info/simple-list` })
};

