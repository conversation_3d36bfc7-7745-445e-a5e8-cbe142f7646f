import request from '@/utils/request/server';

// 播放列表分配文件列 VO
export interface TaifilePlaylistInfoVO {
  id: number // 地址ID
  taifileId: number // 文件表id
  name: string // 文件名
  imageSrc: string // 图片地址
  playlistId: number // 播放列表id
  sort: string // 排序
  status: string // 组状态（10新建 20启用 30停用）
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
  atributeText1: string // 备用text字段1
  atributeText2: string // 备用text字段2
  atributeText3: string // 备用text字段3
  atributeText4: string // 备用text字段4
  atributeText5: string // 备用text字段5
}

// 播放列表分配文件列 API
  // 查询播放列表分配文件列分页
export const getTaifilePlaylistInfoPage = async (params: any) => {
    return await request.get({ url: `/information/taifile-playlist-info/page`, params })
};

  // 查询播放列表分配文件列详情
export const getTaifilePlaylistInfo = async (id: number) => {
    return await request.get({ url: `/information/taifile-playlist-info/get?id=` + id })
};

  // 新增播放列表分配文件列
export const createTaifilePlaylistInfo = async (data: TaifilePlaylistInfoVO) => {
    return await request.post({ url: `/information/taifile-playlist-info/create`, data })
};

  // 修改播放列表分配文件列
export const updateTaifilePlaylistInfo = async (data: TaifilePlaylistInfoVO) => {
    return await request.put({ url: `/information/taifile-playlist-info/update`, data })
};

  // 删除播放列表分配文件列
export const deleteTaifilePlaylistInfo = async (id: number) => {
    return await request.delete({ url: `/information/taifile-playlist-info/delete?id=` + id })
};

  // 导出播放列表分配文件列 Excel
export const exportTaifilePlaylistInfo = async (params) => {
    return await request.download({ url: `/information/taifile-playlist-info/export-excel`, params })
};
