import request from '@/utils/request/server';

// 图片列 VO
export interface TaifileInfoVO {
  id: number // 地址ID
  fileId: number // 文件表id
  name: string // 文件名
  imageSrc: string // 图片地址
  status: string // 组状态（10新建 20启用 30停用）
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
  atributeText1: string // 备用text字段1
  atributeText2: string // 备用text字段2
  atributeText3: string // 备用text字段3
  atributeText4: string // 备用text字段4
  atributeText5: string // 备用text字段5
}

// 图片列 API
  // 查询图片列分页
export const getTaifileInfoPage = async (params: any) => {
    return await request.get({ url: `/information/taifile-info/page`, params })
};

export const getTaifileInfoList = async (params: any) => {
  return await request.get({ url: `/information/taifile-info/list`, params })
};

  // 查询图片列详情
export const getTaifileInfo = async (id: number) => {
    return await request.get({ url: `/information/taifile-info/get?id=` + id })
};

  // 新增图片列
export const createTaifileInfo = async (data: TaifileInfoVO) => {
    return await request.post({ url: `/information/taifile-info/create`, data })
};

  // 修改图片列
export const updateTaifileInfo = async (data: TaifileInfoVO) => {
    return await request.put({ url: `/information/taifile-info/update`, data })
};

  // 删除图片列
export const deleteTaifileInfo = async (id: number) => {
    return await request.delete({ url: `/information/taifile-info/delete?id=` + id })
};

  // 导出图片列 Excel
export const exportTaifileInfo = async (params) => {
    return await request.download({ url: `/information/taifile-info/export-excel`, params })
};
