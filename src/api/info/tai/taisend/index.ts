import request from '@/utils/request/server';

// 附件上传api
export const uploadFile = async (data: FormData) => {
  return await request.upload({
    url: `/information/taifile-info/upload`,
    data
  });
};
//获取当前的附件列表
export const getTaifileInfoPage = async (params: any) => {
  return await request.get({ url: `/external/information-files/page`, params });
};

//删除附件
export const deleteFile = async (ids: any) => {
  return await request.delete({
    url: `/external/information-files/delete?ids=` + ids
  });
};

//获取播放主题
export const getTaiplaylistInfoPage = async (params: any) => {
  return await request.get({
    url: `/external/information-playlists/page`,
    params
  });
};
//创建播放主题
export const createTaiplaylistInfo = async (data: any) => {
  return await request.post({
    url: `/external/information-playlists/create`,
    data
  });
};
//更新播放主题
export const updateTaiplaylistInfo = async (data: any) => {
  return await request.put({
    url: `/external/information-playlists/update`,
    data
  });
};
//删除播放主题
export const deleteTaiplaylistInfo = async (id: any) => {
  return await request.delete({
    url: `/external/information-playlists/delete?id=` + id
  });
};
//获取当前主题的播放列表
export const getTaifilePlaylistInfoList = async (id) => {
  return await request.get({
    url: `/external/information-playlists/get/pics?id=` + id
  });
};
export const assignFile = async (data: any) => {
  return await request.post({
    url: `/external/information-files-playlist/assign-file`,
    data
  });
};

//获取设备列表
export const getInformationDeviceList = async (params) => {
  return await request.get({
    url: `/external/information-devices/page`,
    params
  });
};

//分配设备
export const assignDevice = async (data: any) => {
  return await request.post({
    url: `/external/information-devices/assign-devices`,
    data
  });
};
