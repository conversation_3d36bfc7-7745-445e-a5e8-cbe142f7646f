import request from '@/utils/request/server';

// 设备管理 VO
export interface TaideviceInfoVO {
  id: number // 设备ID
  code: string // 设备id
  name: string // 设备描述
  version: string // 版本号
  playlistId: number // 播放列表ID
  deviceInterval: string // 间隔
  sort: number // 显示顺序
  model: string // 型号
  sn: string // sn
  orientation: string // 方向
  groupId: string // 组id
  active: string // 是否活动
  battery: string // 电池
  powerType: string // 电源类型
  ecoMode: string // 生态模式
  ipAddress: string // ip地址
  wifiSsid: string // wifi标识
  wifiPwd: string // wifi密码
  macAddress: string // mac地址
  playingPlayList: string // 播放播放列表
  lastConnected: string // 最后连接时间
  nextWakeup: string // 下一个唤醒
  telemetryVer: string // 遥测版本
  status: string // 设备状态（10新建 20启用 30停用）
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
  atributeText1: string // 备用text字段1
  atributeText2: string // 备用text字段2
  atributeText3: string // 备用text字段3
  atributeText4: string // 备用text字段4
  atributeText5: string // 备用text字段5
}

// 设备管理 API
  // 查询设备管理分页
export const getTaideviceInfoPage = async (params: any) => {
    return await request.get({ url: `/information/taidevice-info/page`, params })
};

  // 查询设备管理详情
export const getTaideviceInfo = async (id: number) => {
    return await request.get({ url: `/information/taidevice-info/get?id=` + id })
};

  // 新增设备管理
export const createTaideviceInfo = async (data: TaideviceInfoVO) => {
    return await request.post({ url: `/information/taidevice-info/create`, data })
};

  // 修改设备管理
export const updateTaideviceInfo = async (data: TaideviceInfoVO) => {
    return await request.put({ url: `/information/taidevice-info/update`, data })
};

  // 删除设备管理
export const deleteTaideviceInfo = async (id: number) => {
    return await request.delete({ url: `/information/taidevice-info/delete?id=` + id })
};

  // 导出设备管理 Excel
export const exportTaideviceInfo = async (params) => {
    return await request.download({ url: `/information/taidevice-info/export-excel`, params })
};
//刷新设备信息
export const refreshDeviceInfo = async () => {
  return await request.get({ url: `/external/information-devices/interface_get_device` })
};