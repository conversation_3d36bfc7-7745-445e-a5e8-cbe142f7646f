import request from '@/utils/request/server';

// 楼幢信息 VO
export interface BuildingInfoVO {
  id: number; // 楼幢信息主键ID
  uuid: string; // 雪花id，主要是为了对接外部系统用
  projectId: number; // 项目id
  projectCode: string; // 项目编码
  projectName: string; // 项目名称
  code: string; // 楼栋编码
  name: string; // 楼幢名称
  houseCount: number; // 房屋数
  stewardPhone: string; // 管家电话
  buildingArea: number; // 建筑面积
  totalFloor: number; // 总层数
  roomPerFloor: number; // 每层房间数
  elevatorStatus: number; // 有无电梯（0：无；1：有，值集编码：lease_have_or_none）
  decorationStandard: number; // 装修标准（枚举值，如 0：简装；1：精装 等，值集编码：lease_decoration_standards）
  houseType: number; // 房屋类型（枚举值，如 0：住宅；1：商办 等，值集编码：lease_house_type）
  remark: string; // 备注信息
  status: number; // 状态 10-新建，20-审核中，30-已发布，40-审批拒绝，50-作废，值集编码：lease_general_status
  deptId: number; // 部门id
  atributeVarchar1: string; // 备用字段1
  atributeVarchar2: string; // 备用字段2
  atributeVarchar3: string; // 备用字段3
  atributeVarchar4: string; // 备用字段4
  atributeVarchar5: string; // 备用字段5
}

// 楼幢信息 API
// 查询楼幢信息分页
export const getBuildingInfoPage = async (params: any) => {
  return await request.get({ url: `/lease/building-info/page`, params });
};

// 查询楼幢信息详情
export const getBuildingInfo = async (id: number) => {
  return await request.get({ url: `/lease/building-info/get?id=` + id });
};

// 新增楼幢信息
export const createBuildingInfo = async (data: BuildingInfoVO) => {
  return await request.post({ url: `/lease/building-info/create`, data });
};

// 修改楼幢信息
export const updateBuildingInfo = async (data: BuildingInfoVO) => {
  return await request.put({ url: `/lease/building-info/update`, data });
};

// 删除楼幢信息
export const deleteBuildingInfo = async (id: number) => {
  return await request.delete({ url: `/lease/building-info/delete?id=` + id });
};

// 导出楼幢信息 Excel
export const exportBuildingInfo = async (params) => {
  return await request.download({
    url: `/lease/building-info/export-excel`,
    params
  });
};
