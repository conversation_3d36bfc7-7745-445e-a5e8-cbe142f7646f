import request from '@/utils/request/server';

// 查询项目信息分页
export const getProjectPage = async (params: any) => {
  return await request.get({ url: `/lease/project-info/page`, params });
};

// 查询项目信息详情
export const getProject = async (id: number) => {
  return await request.get({ url: `/lease/project-info/get?id=` + id });
};

// 新增项目信息
export const createProject = async (data) => {
  return await request.post({ url: `/lease/project-info/create`, data });
};

// 修改项目信息
export const updateProject = async (data) => {
  return await request.put({ url: `/lease/project-info/update`, data });
};

// 删除项目信息
export const deleteProject = async (id: number) => {
  return await request.delete({
    url: `/lease/project-info/delete?id=` + id
  });
};

// 导出项目信息 Excel
export const exportProject = async (params) => {
  return await request.download({
    url: `/lease/project-info/export-excel`,
    params
  });
};
