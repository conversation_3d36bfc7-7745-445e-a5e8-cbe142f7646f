.draggable-workflow-designer {
  width: 100%;
  height: 100vh;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
  user-select: none;

  .toolbar {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    z-index: 100;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

    .toolbar-group {
      display: flex;
      align-items: center;
      gap: 8px;

      .zoom-level {
        min-width: 50px;
        text-align: center;
        font-weight: 500;
        color: #374151;
      }
    }

    .el-button {
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }
  }

  .canvas {
    width: 100%;
    height: 100%;
    position: relative;
    cursor: grab;
    transition: transform 0.1s ease-out;

    &:active {
      cursor: grabbing;
    }

    .grid-background {
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background-image: 
        radial-gradient(circle at 1px 1px, rgba(255, 255, 255, 0.3) 1px, transparent 0);
      background-size: 20px 20px;
      pointer-events: none;
    }

    .connections, .temp-connection {
      position: absolute;
      top: 0;
      left: 0;
      pointer-events: none;
      z-index: 1;

      .connection-line {
        transition: stroke-width 0.3s ease;

        &:hover {
          stroke-width: 3;
        }
      }
    }

    .workflow-node {
      position: absolute;
      width: 220px;
      min-height: 80px;
      background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
      border: 2px solid transparent;
      border-radius: 16px;
      box-shadow: 
        0 4px 20px rgba(0, 0, 0, 0.08),
        0 2px 8px rgba(0, 0, 0, 0.04);
      cursor: move;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      z-index: 10;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 
          0 8px 30px rgba(0, 0, 0, 0.12),
          0 4px 12px rgba(0, 0, 0, 0.08);
        border-color: rgba(99, 102, 241, 0.3);
      }

      &.node-selected {
        border-color: #6366f1;
        box-shadow: 
          0 8px 30px rgba(99, 102, 241, 0.2),
          0 4px 12px rgba(99, 102, 241, 0.1);
      }

      .node-header {
        display: flex;
        align-items: center;
        padding: 16px;
        gap: 12px;

        .node-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 36px;
          height: 36px;
          border-radius: 10px;
          font-size: 18px;
          color: white;
          flex-shrink: 0;
        }

        .node-title {
          flex: 1;
          font-size: 16px;
          font-weight: 600;
          color: #1e293b;
        }

        .node-actions {
          display: flex;
          gap: 4px;
        }
      }

      .node-content {
        padding: 0 16px 16px;
        font-size: 14px;
        color: #64748b;
        line-height: 1.5;
      }

      // 节点类型样式
      &.node-start .node-icon {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      }

      &.node-user-task .node-icon {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      }

      &.node-copy-task .node-icon {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
      }

      &.node-condition .node-icon {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
      }

      &.node-end .node-icon {
        background: linear-gradient(135deg, #64748b 0%, #475569 100%);
      }

      // 连接点
      .connection-point {
        position: absolute;
        width: 12px;
        height: 12px;
        background: #6366f1;
        border: 3px solid white;
        border-radius: 50%;
        cursor: crosshair;
        transition: all 0.3s ease;
        z-index: 20;

        &:hover {
          transform: scale(1.3);
          box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.2);
        }

        &.connection-input {
          top: 50%;
          left: -9px;
          transform: translateY(-50%);
        }

        &.connection-output {
          top: 50%;
          right: -9px;
          transform: translateY(-50%);
        }
      }
    }
  }

  .property-panel {
    position: absolute;
    top: 100px;
    right: 20px;
    width: 320px;
    max-height: calc(100vh - 140px);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    z-index: 100;
    overflow: hidden;

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #e2e8f0;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
      }
    }

    .panel-content {
      padding: 20px;
      max-height: calc(100vh - 220px);
      overflow-y: auto;

      .el-form-item {
        margin-bottom: 20px;

        .el-form-item__label {
          font-weight: 500;
          color: #374151;
        }

        .el-input, .el-select, .el-textarea {
          .el-input__wrapper, .el-select__wrapper, .el-textarea__inner {
            border-radius: 8px;
            transition: all 0.3s ease;

            &:hover {
              border-color: #6366f1;
            }

            &.is-focus {
              border-color: #6366f1;
              box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .draggable-workflow-designer {
    .toolbar {
      flex-direction: column;
      gap: 12px;
      left: 10px;
      right: 10px;
    }

    .property-panel {
      right: 10px;
      width: calc(100vw - 20px);
      max-width: 320px;
    }
  }
}
