<template>
  <div class="draggable-workflow-designer" ref="canvasContainer">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-group">
        <el-button size="small" @click="addNode('start')">
          <Icon icon="ep:video-play" />
          开始
        </el-button>
        <el-button size="small" @click="addNode('user-task')">
          <Icon icon="ep:user" />
          审批人
        </el-button>
        <el-button size="small" @click="addNode('copy-task')">
          <Icon icon="ep:document-copy" />
          抄送
        </el-button>
        <el-button size="small" @click="addNode('condition')">
          <Icon icon="ep:operation" />
          条件分支
        </el-button>
        <el-button size="small" @click="addNode('end')">
          <Icon icon="ep:video-pause" />
          结束
        </el-button>
      </div>
      <div class="toolbar-group">
        <el-button size="small" @click="zoomIn">
          <Icon icon="ep:zoom-in" />
        </el-button>
        <span class="zoom-level">{{ Math.round(zoomLevel * 100) }}%</span>
        <el-button size="small" @click="zoomOut">
          <Icon icon="ep:zoom-out" />
        </el-button>
        <el-button size="small" @click="resetZoom">
          <Icon icon="ep:refresh" />
        </el-button>
        <el-button size="small" type="primary" @click="saveWorkflow">
          <Icon icon="ep:check" />
          保存
        </el-button>
      </div>
    </div>

    <!-- 画布 -->
    <div
      class="canvas"
      ref="canvas"
      @mousedown="onCanvasMouseDown"
      @mousemove="onCanvasMouseMove"
      @mouseup="onCanvasMouseUp"
      @wheel="onCanvasWheel"
      :style="{
        transform: `translate(${panOffset.x}px, ${panOffset.y}px) scale(${zoomLevel})`,
        transformOrigin: '0 0'
      }"
    >
      <!-- 网格背景 -->
      <div class="grid-background"></div>

      <!-- 连接线 -->
      <svg class="connections" :style="{ width: '100%', height: '100%' }">
        <defs>
          <marker
            id="arrowhead"
            markerWidth="10"
            markerHeight="7"
            refX="9"
            refY="3.5"
            orient="auto"
          >
            <polygon points="0 0, 10 3.5, 0 7" fill="#6366f1" />
          </marker>
        </defs>
        <path
          v-for="connection in connections"
          :key="`${connection.from}-${connection.to}`"
          :d="getConnectionPath(connection)"
          stroke="#6366f1"
          stroke-width="2"
          fill="none"
          marker-end="url(#arrowhead)"
          class="connection-line"
        />
      </svg>

      <!-- 节点 -->
      <div
        v-for="node in nodes"
        :key="node.id"
        class="workflow-node"
        :class="[
          `node-${node.type}`,
          { 'node-selected': selectedNodeId === node.id }
        ]"
        :data-node-id="node.id"
        :style="{
          left: node.x + 'px',
          top: node.y + 'px'
        }"
        @mousedown="onNodeMouseDown($event, node)"
        @click="selectNode(node.id)"
      >
        <!-- 节点内容 -->
        <div class="node-header">
          <div class="node-icon">
            <Icon :icon="getNodeIcon(node.type)" />
          </div>
          <div class="node-title">{{ node.name }}</div>
          <div class="node-actions" v-if="selectedNodeId === node.id">
            <el-button size="small" text @click.stop="deleteNode(node.id)">
              <Icon icon="ep:delete" />
            </el-button>
          </div>
        </div>
        <div class="node-content" v-if="node.description">
          {{ node.description }}
        </div>

        <!-- 连接点 -->
        <div
          class="connection-point connection-input"
          v-if="node.type !== 'start'"
          @mousedown.stop="startConnection($event, node.id, 'input')"
        ></div>
        <div
          class="connection-point connection-output"
          v-if="node.type !== 'end'"
          @mousedown.stop="startConnection($event, node.id, 'output')"
        ></div>
      </div>

      <!-- 临时连接线 -->
      <svg
        v-if="isConnecting"
        class="temp-connection"
        :style="{ width: '100%', height: '100%' }"
      >
        <path
          :d="getTempConnectionPath()"
          stroke="#6366f1"
          stroke-width="2"
          fill="none"
          stroke-dasharray="5,5"
        />
      </svg>
    </div>

    <!-- 属性面板 -->
    <div class="property-panel" v-if="selectedNode">
      <div class="panel-header">
        <h3>节点属性</h3>
        <el-button size="small" text @click="selectedNodeId = null">
          <Icon icon="ep:close" />
        </el-button>
      </div>
      <div class="panel-content">
        <el-form label-width="80px">
          <el-form-item label="节点名称">
            <el-input v-model="selectedNode.name" />
          </el-form-item>
          <el-form-item label="描述">
            <el-input
              v-model="selectedNode.description"
              type="textarea"
              :rows="3"
            />
          </el-form-item>
          <el-form-item v-if="selectedNode.type === 'user-task'" label="审批人">
            <el-select
              v-model="selectedNode.assignee"
              placeholder="请选择审批人"
            >
              <el-option label="张三" value="zhangsan" />
              <el-option label="李四" value="lisi" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  interface WorkflowNode {
    id: string;
    type: 'start' | 'user-task' | 'copy-task' | 'condition' | 'end';
    name: string;
    description?: string;
    x: number;
    y: number;
    assignee?: string;
  }

  interface Connection {
    from: string;
    to: string;
  }

  const canvasContainer = ref<HTMLElement>();
  const canvas = ref<HTMLElement>();

  // 节点和连接
  const nodes = ref<WorkflowNode[]>([]);
  const connections = ref<Connection[]>([]);
  const selectedNodeId = ref<string | null>(null);

  // 画布状态
  const zoomLevel = ref(1);
  const panOffset = ref({ x: 0, y: 0 });
  const isPanning = ref(false);
  const panStart = ref({ x: 0, y: 0 });

  // 拖拽状态
  const isDragging = ref(false);
  const dragStart = ref({ x: 0, y: 0 });
  const dragNodeId = ref<string | null>(null);

  // 连接状态
  const isConnecting = ref(false);
  const connectionStart = ref({ nodeId: '', type: 'output', x: 0, y: 0 });
  const connectionEnd = ref({ x: 0, y: 0 });

  const selectedNode = computed(() =>
    nodes.value.find((node) => node.id === selectedNodeId.value)
  );

  // 事件定义
  const emit = defineEmits<{
    success: [data: { nodes: WorkflowNode[]; connections: Connection[] }];
  }>();

  // 保存工作流
  const saveWorkflow = () => {
    const workflowData = {
      nodes: nodes.value,
      connections: connections.value
    };
    emit('success', workflowData);
  };

  // 添加节点
  const addNode = (type: WorkflowNode['type']) => {
    const newNode: WorkflowNode = {
      id: `node_${Date.now()}`,
      type,
      name: getDefaultNodeName(type),
      x: 200 + Math.random() * 200,
      y: 200 + Math.random() * 200
    };
    nodes.value.push(newNode);
  };

  const getDefaultNodeName = (type: string) => {
    const names = {
      start: '开始',
      'user-task': '审批人',
      'copy-task': '抄送',
      condition: '条件分支',
      end: '结束'
    };
    return names[type] || '未知节点';
  };

  const getNodeIcon = (type: string) => {
    const icons = {
      start: 'ep:video-play',
      'user-task': 'ep:user',
      'copy-task': 'ep:document-copy',
      condition: 'ep:operation',
      end: 'ep:video-pause'
    };
    return icons[type] || 'ep:question';
  };

  // 节点选择
  const selectNode = (nodeId: string) => {
    selectedNodeId.value = nodeId;
  };

  // 删除节点
  const deleteNode = (nodeId: string) => {
    nodes.value = nodes.value.filter((node) => node.id !== nodeId);
    connections.value = connections.value.filter(
      (conn) => conn.from !== nodeId && conn.to !== nodeId
    );
    if (selectedNodeId.value === nodeId) {
      selectedNodeId.value = null;
    }
  };

  // 缩放控制
  const zoomIn = () => {
    zoomLevel.value = Math.min(zoomLevel.value * 1.2, 3);
  };

  const zoomOut = () => {
    zoomLevel.value = Math.max(zoomLevel.value / 1.2, 0.3);
  };

  const resetZoom = () => {
    zoomLevel.value = 1;
    panOffset.value = { x: 0, y: 0 };
  };

  // 鼠标事件处理
  const onCanvasMouseDown = (event: MouseEvent) => {
    if (event.target === canvas.value) {
      isPanning.value = true;
      panStart.value = {
        x: event.clientX - panOffset.value.x,
        y: event.clientY - panOffset.value.y
      };
      selectedNodeId.value = null;
    }
  };

  const onCanvasMouseMove = (event: MouseEvent) => {
    if (isPanning.value) {
      panOffset.value = {
        x: event.clientX - panStart.value.x,
        y: event.clientY - panStart.value.y
      };
    }

    if (isDragging.value && dragNodeId.value) {
      const node = nodes.value.find((n) => n.id === dragNodeId.value);
      if (node) {
        const rect = canvas.value!.getBoundingClientRect();
        node.x =
          (event.clientX - rect.left - panOffset.value.x) / zoomLevel.value -
          dragStart.value.x;
        node.y =
          (event.clientY - rect.top - panOffset.value.y) / zoomLevel.value -
          dragStart.value.y;
      }
    }

    if (isConnecting.value) {
      const rect = canvas.value!.getBoundingClientRect();
      connectionEnd.value = {
        x: (event.clientX - rect.left - panOffset.value.x) / zoomLevel.value,
        y: (event.clientY - rect.top - panOffset.value.y) / zoomLevel.value
      };
    }
  };

  const onCanvasMouseUp = () => {
    isPanning.value = false;
    isDragging.value = false;
    dragNodeId.value = null;
    isConnecting.value = false;
  };

  const onCanvasWheel = (event: WheelEvent) => {
    event.preventDefault();
    const delta = event.deltaY > 0 ? 0.9 : 1.1;
    zoomLevel.value = Math.max(0.3, Math.min(3, zoomLevel.value * delta));
  };

  // 节点拖拽
  const onNodeMouseDown = (event: MouseEvent, node: WorkflowNode) => {
    event.stopPropagation();
    isDragging.value = true;
    dragNodeId.value = node.id;
    const rect = canvas.value!.getBoundingClientRect();
    dragStart.value = {
      x:
        (event.clientX - rect.left - panOffset.value.x) / zoomLevel.value -
        node.x,
      y:
        (event.clientY - rect.top - panOffset.value.y) / zoomLevel.value -
        node.y
    };
  };

  // 连接处理
  const startConnection = (
    event: MouseEvent,
    nodeId: string,
    type: 'input' | 'output'
  ) => {
    event.stopPropagation();
    isConnecting.value = true;
    const node = nodes.value.find((n) => n.id === nodeId);
    if (node) {
      connectionStart.value = {
        nodeId,
        type,
        x: node.x + (type === 'output' ? 220 : 0),
        y: node.y + 40
      };
    }

    // 添加鼠标移动监听
    const handleMouseMove = (e: MouseEvent) => {
      const rect = canvas.value!.getBoundingClientRect();
      connectionEnd.value = {
        x: (e.clientX - rect.left - panOffset.value.x) / zoomLevel.value,
        y: (e.clientY - rect.top - panOffset.value.y) / zoomLevel.value
      };
    };

    const handleMouseUp = (e: MouseEvent) => {
      // 检查是否连接到了另一个节点
      const targetElement = document.elementFromPoint(e.clientX, e.clientY);
      const targetNode = targetElement?.closest('.workflow-node');

      if (
        targetNode &&
        targetNode !== event.target?.closest('.workflow-node')
      ) {
        const targetNodeId = targetNode.getAttribute('data-node-id');
        if (targetNodeId && targetNodeId !== nodeId) {
          // 创建连接
          const existingConnection = connections.value.find(
            (conn) => conn.from === nodeId && conn.to === targetNodeId
          );
          if (!existingConnection) {
            connections.value.push({ from: nodeId, to: targetNodeId });
          }
        }
      }

      isConnecting.value = false;
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // 连接路径计算
  const getConnectionPath = (connection: Connection) => {
    const fromNode = nodes.value.find((n) => n.id === connection.from);
    const toNode = nodes.value.find((n) => n.id === connection.to);

    if (!fromNode || !toNode) return '';

    const startX = fromNode.x + 110; // 节点宽度的一半
    const startY = fromNode.y + 40; // 节点高度的一半
    const endX = toNode.x + 110;
    const endY = toNode.y + 40;

    const midX = (startX + endX) / 2;

    return `M ${startX} ${startY} C ${midX} ${startY}, ${midX} ${endY}, ${endX} ${endY}`;
  };

  const getTempConnectionPath = () => {
    const startX = connectionStart.value.x;
    const startY = connectionStart.value.y;
    const endX = connectionEnd.value.x;
    const endY = connectionEnd.value.y;

    const midX = (startX + endX) / 2;

    return `M ${startX} ${startY} C ${midX} ${startY}, ${midX} ${endY}, ${endX} ${endY}`;
  };

  // 初始化示例数据
  onMounted(() => {
    nodes.value = [
      { id: 'start1', type: 'start', name: '开始', x: 100, y: 200 },
      {
        id: 'task1',
        type: 'user-task',
        name: '审批人',
        x: 350,
        y: 200,
        description: '请配置审批人'
      },
      { id: 'end1', type: 'end', name: '结束', x: 600, y: 200 }
    ];

    connections.value = [
      { from: 'start1', to: 'task1' },
      { from: 'task1', to: 'end1' }
    ];
  });
</script>
