# Query: dict-tag
# ContextLines: 1

20 个结果 - 18 文件

src/components/bpmnProcessDesigner/package/designer/ProcessViewer.vue:
  91              <template #default="scope">
  92:               <dict-data :type="DICT_TYPE.BPM_TASK_STATUS" :value="scope.row.status" />
  93              </template>

src/components/bpmnProcessDesigner/package/penal/listeners/ProcessListenerDialog.vue:
   8            <template #default="scope">
   9:             <dict-tag :type="DICT_TYPE.BPM_PROCESS_LISTENER_TYPE" :value="scope.row.type" />
  10            </template>

  14            <template #default="scope">
  15:             <dict-tag
  16                :type="DICT_TYPE.BPM_PROCESS_LISTENER_VALUE_TYPE"

src/components/SimpleProcessDesignerV2/src/nodes/EndEventNode.vue:
  48              <template #default="scope">
  49:               <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS" :value="scope.row.status" />
  50              </template>

src/components/SimpleProcessDesignerV2/src/nodes/StartUserNode.vue:
  89            <template #default="scope">
  90:             <dict-tag :type="DICT_TYPE.BPM_TASK_STATUS" :value="scope.row.status" />
  91            </template>

src/components/SimpleProcessDesignerV2/src/nodes/UserTaskNode.vue:
  104            <template #default="scope">
  105:             <dict-tag :type="DICT_TYPE.BPM_TASK_STATUS" :value="scope.row.status" />
  106            </template>

src/views/bpm/form/index.vue:
  45          <template #default="scope">
  46:           <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
  47          </template>

src/views/bpm/model/definition/index.vue:
  32          <template #default="{ row }">
  33:           <dict-tag :value="row.modelType" :type="DICT_TYPE.BPM_MODEL_TYPE" />
  34          </template>

src/views/bpm/oa/leave/detail.vue:
  4        <el-descriptions-item label="请假类型">
  5:         <dict-tag :type="DICT_TYPE.BPM_OA_LEAVE_TYPE" :value="detailData.type" />
  6        </el-descriptions-item>

src/views/bpm/oa/leave/index.vue:
   85          <template #default="scope">
   86:           <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS" :value="scope.row.status" />
   87          </template>

  104          <template #default="scope">
  105:           <dict-tag :type="DICT_TYPE.BPM_OA_LEAVE_TYPE" :value="scope.row.type" />
  106          </template>

src/views/bpm/processInstance/index.vue:
  166            <template v-else>
  167:             <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS" :value="scope.row.status" />
  168            </template>

src/views/bpm/processInstance/detail/index.vue:
  14            <div class="text-26px font-bold mb-5px">{{ processInstance.name }}</div>
  15:           <dict-tag
  16              v-if="processInstance.status"

src/views/bpm/processInstance/detail/ProcessInstanceTaskList.vue:
  24        <template #default="scope">
  25:         <dict-tag :type="DICT_TYPE.BPM_TASK_STATUS" :value="scope.row.status" />
  26        </template>

src/views/bpm/processInstance/report/index.vue:
  98          <template #default="scope">
  99:           <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS" :value="scope.row.status" />
  100          </template>

src/views/bpm/task/done/index.vue:
  170          <template #default="scope">
  171:           <dict-tag :type="DICT_TYPE.BPM_TASK_STATUS" :value="scope.row.status" />
  172          </template>

src/views/bpm/task/manager/index.vue:
  80          <template #default="scope">
  81:           <dict-tag :type="DICT_TYPE.BPM_TASK_STATUS" :value="scope.row.status" />
  82          </template>

src/views/infra/job/components/job-log-detail.vue:
  37        <el-descriptions-item label="任务状态">
  38:         <dict-tag :type="DICT_TYPE.INFRA_JOB_LOG_STATUS" :value="data.status" />
  39        </el-descriptions-item>

src/views/system/social/user/index copy.vue:
  79          <template #default="scope">
  80:           <dict-tag
  81              :type="DICT_TYPE.SYSTEM_SOCIAL_TYPE"

src/views/system/social/user/SocialUserDetail.vue:
  4        <el-descriptions-item label="社交平台" min-width="160">
  5:         <dict-tag :type="DICT_TYPE.SYSTEM_SOCIAL_TYPE" :value="detailData.type" />
  6        </el-descriptions-item>
