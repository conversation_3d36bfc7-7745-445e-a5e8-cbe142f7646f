.file-picker-modal {
  height: 692px;
  max-height: 94vh;
  max-height: 94dvh;
  min-height: 434px;
  display: flex;
  flex-direction: column;

  & > .el-dialog__body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    & > .ele-modal-body {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      padding: 0;
    }
  }
}

.file-picker-wrapper.ele-split-panel {
  overflow: hidden !important;

  .ele-split-collapse-button {
    top: auto;
    bottom: 124px;
    margin-top: 0;
  }

  &.is-collapse .ele-split-collapse-button {
    margin-left: 6px;
  }
}

.file-picker-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;

  & > .ele-loading-spinner {
    background: none;
    border-radius: var(--el-border-radius-base);
    pointer-events: auto;
  }
}

.file-picker-tree-icon {
  width: 18px;
  height: 18px;
  margin-right: 6px;
  user-select: none;
}

.file-picker-wrapper .file-picker-left {
  flex: 1;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: calc(var(--ele-tree-item-radius) * 2);
  padding-right: calc(var(--ele-tree-item-radius) * 2);
  box-sizing: border-box;
  overflow-x: hidden;
  overflow-y: auto;
  user-select: none;
  --ele-tree-item-height: 36px;

  .el-tree-node__content {
    position: relative;

    & > .el-tree-node__label::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 2;
    }

    & > .file-picker-tree-more {
      flex-shrink: 0;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 -8px 0 0;
      color: var(--el-text-color-placeholder);
      font-size: 12px;
      border-radius: var(--el-border-radius-base);
      transition: all 0.2s;
      z-index: 3;

      &:hover {
        color: var(--el-text-color-regular);
        background: hsla(0, 0%, 60%, 0.15);
      }
    }
  }
}

.file-picker-left-add {
  flex-shrink: 0;
  padding: 0 6px;
  margin: 2px 12px 8px 12px;
  border-radius: var(--el-border-radius-base);
  border: 1px dashed var(--el-color-primary);
  box-sizing: border-box;
  font-size: 12px;
  line-height: 20px;
  user-select: none;

  &:hover {
    border-color: var(--el-color-primary-light-5);
  }
}

.file-picker-main {
  flex: 1;
  display: flex;
  overflow: auto;
}

.file-picker-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
}

.file-picker-toolbar {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border-bottom: 1px solid var(--el-border-color-light);
  box-sizing: border-box;
}

.file-picker-search {
  flex: 1;
  display: flex;
  max-width: 220px;
  margin: 0 8px;

  & > .el-input {
    flex: 1;

    & > .el-input__wrapper {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
  }

  & > .el-button {
    flex-shrink: 0;
    margin-left: -1px;
    position: relative;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}

.file-picker-toolbar > .ele-segmented {
  --ele-segmented-height: 26px;
  --ele-segmented-font-size: 16px;
  --ele-segmented-item-padding: 8px;
}

.file-picker-file-list {
  flex: 1;
  overflow: auto;
}

.file-picker-file-list > .ele-file-list-group {
  --ele-file-item-width: 102px;
  --ele-file-item-padding: 4px 2px 2px 2px;

  & > .ele-file-list {
    & > .ele-file-list-header {
      display: none;
    }

    & > .ele-file-list-body {
      display: grid;
      grid-gap: 14px 0px;
      grid-template-columns: repeat(6, 1fr);
      padding: 6px 1px 1px 1px;

      & > .ele-file-list-item {
        margin: 0 auto;

        .ele-file-list-item-title {
          font-size: 12px;
          margin-top: 0;
        }
      }
    }
  }

  & > .ele-file-list-table {
    min-width: 528px;

    & > .ele-file-list-header {
      position: sticky;
      top: 0;
      background: var(--el-bg-color-overlay);
    }
  }

  &.is-ping-top > .ele-file-list-table > .ele-file-list-header {
    z-index: 2;
  }
}

.file-picker-body > .el-empty {
  padding: 0;
  flex: 1;
}

.file-picker-body > .el-pagination {
  padding-bottom: 8px;
}

.file-picker-right {
  flex-shrink: 0;
  width: 128px;
  border-left: 1px solid var(--el-border-color-light);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;
}

.file-picker-right-header {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 6px 12px 8px 12px;
  box-sizing: border-box;
}

.file-picker-right-title {
  flex: 1;
  width: 100%;
  overflow: hidden;
  padding-right: 8px;
  box-sizing: border-box;
  font-size: 13px;
}

.file-picker-right-clear {
  width: 100%;
  padding: 0 6px;
  margin-top: 6px;
  border-radius: var(--el-border-radius-base);
  border: 1px dashed var(--el-color-danger);
  box-sizing: border-box;
  font-size: 12px;
  line-height: 20px;
  user-select: none;

  &:hover {
    border-color: var(--el-color-danger-light-5);
  }
}

.file-picker-right > .ele-upload-list {
  flex: 1;
  overflow: auto;
  padding: 6px 0 8px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-wrap: nowrap;
  gap: 0;

  & > .ele-upload-item {
    flex-shrink: 0;
    margin: 0;

    & + .ele-upload-item {
      margin-top: 8px;
    }
  }
}

@media screen and (max-width: 980px) {
  .file-picker-file-list {
    & > .ele-file-list-group > .ele-file-list > .ele-file-list-body {
      grid-template-columns: repeat(5, 1fr);
    }
  }
}

@media screen and (max-width: 888px) {
  .file-picker-file-list {
    & > .ele-file-list-group > .ele-file-list > .ele-file-list-body {
      grid-template-columns: repeat(4, 1fr);
    }
  }
}

@media screen and (max-width: 788px) {
  .file-picker-main {
    flex-direction: column;
  }

  .file-picker-right {
    width: auto;
    height: 156px;
    border-left: none;
    border-top: 1px solid var(--el-border-color-light);

    & > .file-picker-right-header {
      padding-top: 8px;
      padding-bottom: 0;
      flex-direction: row;

      & > .file-picker-right-title,
      & > .file-picker-right-clear {
        width: auto;
        margin: 0;
      }
    }

    & > .ele-upload-list {
      flex-direction: row;
      padding: 0 8px;

      & > .ele-upload-item + .ele-upload-item {
        margin-top: 0;
        margin-left: 8px;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .file-picker-right-title {
    text-align: right;
  }
}

@media screen and (max-width: 460px) {
  .file-picker-file-list {
    & > .ele-file-list-group > .ele-file-list > .ele-file-list-body {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}

@media screen and (max-width: 358px) {
  .file-picker-file-list {
    & > .ele-file-list-group > .ele-file-list > .ele-file-list-body {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

.file-picker-move-wrapper {
  border: 1px solid var(--el-border-color-light);
  border-radius: var(--el-border-radius-base);

  & > .file-picker-move-tree {
    padding-top: 8px;
    padding-bottom: 8px;
    padding-left: calc(var(--ele-tree-item-radius) * 2);
    padding-right: calc(var(--ele-tree-item-radius) * 2);
    box-sizing: border-box;
    overflow-x: hidden;
    overflow-y: auto;
    user-select: none;
    --ele-tree-item-height: 36px;

    .el-tree-node__content {
      position: relative;
      z-index: 1;

      & > .el-tree-node__label.is-active {
        color: var(--el-color-primary);
        font-weight: bold;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: -1;
          background: var(--el-color-primary-light-9);
          border-radius: var(--ele-tree-item-radius);
        }
      }

      & > .el-radio {
        margin: 0 -12px 0 0;
      }
    }
  }
}
