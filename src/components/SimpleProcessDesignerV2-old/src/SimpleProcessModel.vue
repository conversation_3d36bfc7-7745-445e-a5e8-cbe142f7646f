<template>
  <ele-page flex-table>
    <!-- 固定顶部工具栏 -->
    <el-row type="flex" justify="end">
      <el-button-group key="scale-control" size="default">
        <el-button
          v-if="!readonly"
          size="default"
          :loading="exportLoading"
          @click="exportJson"
        >
          <Icon icon="ep:download" /> 导出
        </el-button>
        <el-button
          v-if="!readonly"
          size="default"
          :loading="importLoading"
          @click="importJson"
        >
          <Icon icon="ep:upload" />导入
        </el-button>
        <!-- 用于打开本地文件-->
        <input
          v-if="!readonly"
          type="file"
          id="files"
          ref="refFile"
          style="display: none"
          accept=".json"
          @change="importLocalFile"
        />
        <el-button
          size="default"
          :icon="ScaleToOriginal"
          :loading="zoomLoading"
          @click="processReZoom()"
        />
        <el-button
          size="default"
          :plain="true"
          :icon="ZoomOut"
          :loading="zoomLoading"
          @click="zoomOut()"
        />
        <el-button size="default" class="w-80px"> {{ scaleValue }}% </el-button>
        <el-button
          size="default"
          :plain="true"
          :icon="ZoomIn"
          :loading="zoomLoading"
          @click="zoomIn()"
        />
        <el-button
          size="default"
          :loading="resetLoading"
          @click="resetPosition"
        >
          重置
        </el-button>
      </el-button-group>
    </el-row>

    <!-- 流程设计器内容区域 -->
    <ele-card class="simple-process-model-container" v-loading="pageLoading">
      <div
        class="simple-process-model"
        :style="`transform: translate(${currentX}px, ${currentY}px) scale(${scaleValue / 100});`"
        @mousedown="startDrag"
        @mousemove="onDrag"
        @mouseup="stopDrag"
        @mouseleave="stopDrag"
        @mouseenter="setGrabCursor"
      >
        <ProcessNodeTree
          v-if="processNodeTree"
          v-model:flow-node="processNodeTree"
        />
      </div>
    </ele-card>
    <el-dialog
      v-model="errorDialogVisible"
      title="保存失败"
      width="400"
      :fullscreen="false"
    >
      <div class="mb-2">以下节点内容不完善，请修改后保存</div>
      <div
        class="mb-3 b-rounded-1 bg-gray-100 p-2 line-height-normal"
        v-for="(item, index) in errorNodes"
        :key="index"
      >
        {{ item.name }} : {{ NODE_DEFAULT_TEXT.get(item.type) }}
      </div>
      <template #footer>
        <el-button type="primary" @click="errorDialogVisible = false"
          >知道了</el-button
        >
      </template>
    </el-dialog>
  </ele-page>
</template>

<script setup lang="ts">
  import ProcessNodeTree from './ProcessNodeTree.vue';
  import { SimpleFlowNode, NodeType, NODE_DEFAULT_TEXT } from './consts';
  import { useWatchNode } from './node';
  import { ZoomOut, ZoomIn, ScaleToOriginal } from '@element-plus/icons-vue';
  import { isString } from '@/utils/is';

  defineOptions({
    name: 'SimpleProcessModel'
  });

  const props = defineProps({
    flowNode: {
      type: Object as () => SimpleFlowNode,
      required: true
    },
    readonly: {
      type: Boolean,
      required: false,
      default: true
    }
  });

  const emits = defineEmits<{
    save: [node: SimpleFlowNode | undefined];
  }>();

  const processNodeTree = useWatchNode(props);

  provide('readonly', props.readonly);

  // Loading 状态管理
  const pageLoading = ref(true); // 页面初始加载状态
  const exportLoading = ref(false); // 导出按钮loading
  const importLoading = ref(false); // 导入按钮loading
  const resetLoading = ref(false); // 重置按钮loading
  const zoomLoading = ref(false); // 缩放按钮loading

  // 错误对话框相关
  const errorDialogVisible = ref(false);
  const errorNodes = ref<SimpleFlowNode[]>([]);

  // TODO 可优化：拖拽有点卡顿
  /** 拖拽、放大缩小等操作 */
  let scaleValue = ref(100);
  const MAX_SCALE_VALUE = 200;
  const MIN_SCALE_VALUE = 50;
  const isDragging = ref(false);
  const startX = ref(0);
  const startY = ref(0);
  const currentX = ref(0);
  const currentY = ref(0);
  const initialX = ref(0);
  const initialY = ref(0);

  const setGrabCursor = () => {
    document.body.style.cursor = 'grab';
  };

  const resetCursor = () => {
    document.body.style.cursor = 'default';
  };

  const startDrag = (e: MouseEvent) => {
    isDragging.value = true;
    startX.value = e.clientX - currentX.value;
    startY.value = e.clientY - currentY.value;
    setGrabCursor(); // 设置小手光标
  };

  const onDrag = (e: MouseEvent) => {
    if (!isDragging.value) return;
    e.preventDefault(); // 禁用文本选择

    // 使用 requestAnimationFrame 优化性能
    requestAnimationFrame(() => {
      currentX.value = e.clientX - startX.value;
      currentY.value = e.clientY - startY.value;
    });
  };

  const stopDrag = () => {
    isDragging.value = false;
    resetCursor(); // 重置光标
  };

  const zoomIn = async () => {
    if (scaleValue.value == MAX_SCALE_VALUE) {
      return;
    }
    zoomLoading.value = true;
    try {
      await new Promise((resolve) => setTimeout(resolve, 200));
      scaleValue.value += 10;
    } finally {
      zoomLoading.value = false;
    }
  };

  const zoomOut = async () => {
    if (scaleValue.value == MIN_SCALE_VALUE) {
      return;
    }
    zoomLoading.value = true;
    try {
      await new Promise((resolve) => setTimeout(resolve, 200));
      scaleValue.value -= 10;
    } finally {
      zoomLoading.value = false;
    }
  };

  const processReZoom = async () => {
    zoomLoading.value = true;
    try {
      await new Promise((resolve) => setTimeout(resolve, 200));
      scaleValue.value = 100;
    } finally {
      zoomLoading.value = false;
    }
  };

  const resetPosition = async () => {
    resetLoading.value = true;
    try {
      await new Promise((resolve) => setTimeout(resolve, 300));
      currentX.value = initialX.value;
      currentY.value = initialY.value;
    } finally {
      resetLoading.value = false;
    }
  };

  /** 校验节点设置 */

  const validateNode = (
    node: SimpleFlowNode | undefined,
    errorNodes: SimpleFlowNode[]
  ) => {
    if (node) {
      const { type, showText, conditionNodes } = node;
      if (type == NodeType.END_EVENT_NODE) {
        return;
      }
      if (type == NodeType.START_USER_NODE) {
        // 发起人节点暂时不用校验，直接校验孩子节点
        validateNode(node.childNode, errorNodes);
      }

      if (
        type === NodeType.USER_TASK_NODE ||
        type === NodeType.COPY_TASK_NODE ||
        type === NodeType.CONDITION_NODE
      ) {
        if (!showText) {
          errorNodes.push(node);
        }
        validateNode(node.childNode, errorNodes);
      }

      if (
        type == NodeType.CONDITION_BRANCH_NODE ||
        type == NodeType.PARALLEL_BRANCH_NODE ||
        type == NodeType.INCLUSIVE_BRANCH_NODE
      ) {
        // 分支节点
        // 1. 先校验各个分支
        conditionNodes?.forEach((item) => {
          validateNode(item, errorNodes);
        });
        // 2. 校验孩子节点
        validateNode(node.childNode, errorNodes);
      }
    }
  };

  /** 获取当前流程数据 */
  const getCurrentFlowData = async () => {
    try {
      errorNodes.value = [];
      validateNode(processNodeTree.value, errorNodes.value);
      if (errorNodes.value.length > 0) {
        errorDialogVisible.value = true;
        return undefined;
      }
      return processNodeTree.value;
    } catch (error) {
      console.error('获取流程数据失败:', error);
      return undefined;
    }
  };

  defineExpose({
    getCurrentFlowData
  });

  /** 导出 JSON */
  const exportJson = async () => {
    exportLoading.value = true;
    try {
      // 模拟异步操作
      await new Promise((resolve) => setTimeout(resolve, 500));

      const jsonData = JSON.stringify(processNodeTree.value, null, 2);
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'model.json';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } finally {
      exportLoading.value = false;
    }
  };

  /** 导入 JSON */
  const refFile = ref();
  const importJson = () => {
    refFile.value.click();
  };
  const importLocalFile = async () => {
    importLoading.value = true;
    try {
      const file = refFile.value.files[0];
      if (!file) return;

      // 模拟异步操作
      await new Promise((resolve) => setTimeout(resolve, 300));

      const reader = new FileReader();
      reader.readAsText(file);
      reader.onload = function () {
        if (isString(this.result) && this.result) {
          processNodeTree.value = JSON.parse(this.result as string);
          emits('save', processNodeTree.value);
        }
        importLoading.value = false;
      };
      reader.onerror = function () {
        importLoading.value = false;
      };
    } catch (error) {
      importLoading.value = false;
    }
  };

  // 在组件初始化时记录初始位置并设置页面加载状态
  onMounted(async () => {
    try {
      // 模拟页面初始化加载
      await new Promise((resolve) => setTimeout(resolve, 1000));

      initialX.value = currentX.value;
      initialY.value = currentY.value;
    } finally {
      pageLoading.value = false;
    }
  });
</script>

<style lang="scss" scoped>
  // 页面容器样式
  :deep(.ele-page) {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden; // 防止整个页面出现滚动条
  }

  // 固定顶部工具栏卡片样式
  :deep(.ele-card:first-child) {
    flex-shrink: 0; // 工具栏不缩放，固定高度
    margin-bottom: 0;
    border-bottom: 1px solid #e5e7eb;
    border-radius: 0;
    position: sticky;
    top: 0;
    z-index: 100;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }

  .fixed-toolbar {
    flex-shrink: 0; // 工具栏不缩放
    z-index: 100;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #e5e7eb;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    :deep(.el-button-group) {
      .el-button {
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        border-color: #e5e7eb;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          border-color: #3b82f6;
          color: #3b82f6;
        }

        &:first-child {
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
        }

        &:last-child {
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
        }

        &:not(:first-child):not(:last-child) {
          border-radius: 0;
        }
      }
    }
  }

  // 流程设计器内容区域卡片样式
  :deep(.simple-process-model-container) {
  }

  .simple-process-model {
    position: relative;
    width: 100%;
    min-height: 100%;
    max-width: 100%; // 防止水平溢出
    box-sizing: border-box;
    padding: 40px 20px; // 内容区域的内边距
  }
</style>
