import type { App } from 'vue';
import { Icon } from './Icon';
import DictData from '@/components/DictData/index.vue';
import UserSelectForm from '@/components/UserSelectForm/index.vue';
import ContentWrap from '@/components/ContentWrap/src/ContentWrap.vue';
import DeptSelectForm from '@/components/DeptSelectForm/index.vue';

export const setupGlobCom = (app: App<Element>): void => {
  app.component('Icon', Icon);
  app.component('DictData', DictData);
  app.component('UserSelectForm', UserSelectForm);
  app.component('DeptSelectForm', DeptSelectForm);
  app.component('ContentWrap', ContentWrap);
};
