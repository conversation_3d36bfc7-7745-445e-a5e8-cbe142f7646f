<!-- 字典组件 -->
<template>
  <template v-if="type === 'text'">
    <span
      v-for="item in valueData"
      :key="item.key"
      v-bind="componentProps || {}"
    >
      {{ item.label }}
    </span>
  </template>
  <template v-else-if="type === 'tag'">
    <el-tag
      v-for="item in valueData"
      :key="item.key"
      size="small"
      :type="item.colorType === 'default' ? 'primary' : item.colorType"
      :disable-transitions="true"
      v-bind="componentProps || {}"
    >
      {{ item.label }}
    </el-tag>
  </template>
  <el-radio-group
    v-else-if="type === 'radio'"
    :disabled="disabled"
    v-bind="componentProps || {}"
    v-model="model"
  >
    <el-radio
      v-for="item in data"
      :key="item.key"
      :value="item.value"
      :label="item.label"
    />
  </el-radio-group>
  <el-checkbox-group
    v-else-if="type === 'checkbox'"
    :disabled="disabled"
    v-bind="componentProps || {}"
    v-model="model"
  >
    <el-checkbox
      v-for="item in data"
      :key="item.key"
      :value="item.value"
      :label="item.label"
    />
  </el-checkbox-group>
  <el-select
    v-else
    :disabled="disabled"
    :clearable="clearable"
    :placeholder="placeholder"
    :filterable="filterable"
    :teleported="teleported"
    class="ele-fluid"
    v-bind="componentProps || {}"
    :multiple="type === 'multipleSelect' || props.multiple"
    v-model="model"
  >
    <el-option
      v-for="item in data"
      :key="item.key"
      :value="item.value"
      :label="item.label"
    />
  </el-select>
</template>

<script setup>
  import { computed, watch } from 'vue';
  import { EleMessage } from 'sirius-platform-pro';
  import { storeToRefs } from 'pinia';
  import { useUserStore } from '@/store/modules/user';
  import { listDictDatas } from '@/api/system/dict/dict.data';

  defineOptions({ name: 'DictData' });

  const props = defineProps({
    /** 字典类型 */
    code: String,
    /** 组件类型 */
    type: {
      type: String,
      default: 'text'
    },
    /** 字典数据值类型 */
    valueType: String,
    /** 组件属性 */
    componentProps: Object,
    /** 是否禁用 */
    disabled: Boolean,
    /** 提示文本 */
    placeholder: String,
    /** select是否可清除 */
    clearable: {
      type: Boolean,
      default: true
    },
    /** select的下拉是否插入到body下 */
    teleported: {
      type: Boolean,
      default: true
    },
    /** select是否可搜索 */
    filterable: Boolean,
    /** 是否多选 翻译时候用 */
    isMultiple: Boolean,
    /** select是否多选 */
    multiple: Boolean
  });

  /** 字典值 */
  const model = defineModel({
    type: [String, Number, Boolean, Array]
  });

  /** 已缓存的字典 */
  const userStore = useUserStore();
  const { dicts } = storeToRefs(userStore);

  /** 字典的数据 */
  const data = computed(() => {
    const code = props.code;
    const list = (code ? dicts.value[code] : void 0) || [];
    return list.map((item) => {
      let result = {
        ...item,
        key: item.value,
        value:
          item.value == null
            ? null
            : props.valueType === 'number'
              ? Number(item.value)
              : props.valueType === 'boolean'
                ? item.value === 'true' || item.value === true
                : item.value,
        label: item.label
      };
      return result;
    });
  });

  /** 绑定值对应的数据 */
  const valueData = computed(() => {
    const result = [];
    let val = model.value;

    if (val == null || val === '') {
      return result;
    }

    // 判断是否为多选模式
    // 支持以下几种多选模式：
    // 1. isMultiple: true - 显式指定多选翻译模式
    // 2. multiple: true - select组件的多选属性
    // 3. type: 'multipleSelect' - 多选下拉类型
    // 4. type: 'checkbox' - 复选框类型
    const isMultipleMode =
      props.isMultiple ||
      props.multiple ||
      props.type === 'multipleSelect' ||
      props.type === 'checkbox';

    // 如果是多选模式且值是字符串（如 "1,2,3"），则转换为数组（如 [1,2,3]）
    if (isMultipleMode && typeof val === 'string' && val.includes(',')) {
      val = val
        .split(',')
        .map((item) => {
          const trimmedItem = item.trim();
          if (trimmedItem === '') return null;

          // 根据 valueType 转换数据类型
          if (props.valueType === 'number') {
            return Number(trimmedItem);
          } else if (props.valueType === 'boolean') {
            return trimmedItem === 'true' || trimmedItem === true;
          }
          return trimmedItem;
        })
        .filter((item) => item !== null && item !== '');
    }

    const values = Array.isArray(val) ? val : [val];
    values.forEach((v) => {
      const temp = data.value.find((d) => d.value == v);
      if (temp != null) {
        result.push(temp);
      } else {
        result.push({ key: v, value: v, label: v });
      }
    });
    return result;
  });

  /** 若还未缓存过则获取字典数据 */
  watch(
    () => props.code,
    (code) => {
      if (!code || dicts.value[code] != null) {
        return;
      }
      userStore.setDicts([], code);
      listDictDatas({ dictType: code })
        .then((list) => {
          userStore.setDicts(list, code);
        })
        .catch((e) => {
          EleMessage.error({ message: e.message, plain: true });
        });
    },
    { immediate: true }
  );
</script>
