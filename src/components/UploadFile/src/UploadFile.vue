<template>
  <div v-if="!disabled" class="upload-file">
    <el-upload
      ref="uploadRef"
      v-model:file-list="fileList"
      :action="uploadUrl"
      :auto-upload="autoUpload"
      :before-upload="beforeUpload"
      :disabled="disabled"
      :drag="drag"
      :http-request="httpRequest"
      :limit="props.limit"
      :multiple="props.limit > 1"
      :on-error="excelUploadError"
      :on-exceed="handleExceed"
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      :on-success="handleFileSuccess"
      :show-file-list="true"
      class="upload-file-uploader"
      name="file"
    >
      <el-button type="primary">
        <Icon icon="ep:upload-filled" />
        选取文件
      </el-button>
      <template v-if="isShowTip" #tip>
        <div style="font-size: 12px">
          大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
        </div>
        <div style="font-size: 12px">
          格式为 <b style="color: #f56c6c">{{ fileType.join('/') }}</b> 的文件
        </div>
      </template>
      <template #file="row">
        <div class="flex items-center">
          <span>{{ row.file.name }}</span>
          <div class="ml-10px">
            <el-link
              :href="row.file.url"
              :underline="false"
              download
              target="_blank"
              type="primary"
            >
              下载
            </el-link>
          </div>
          <div class="ml-10px">
            <el-button link type="danger" @click="handleRemove(row.file)">
              删除</el-button
            >
          </div>
        </div>
      </template>
    </el-upload>
  </div>

  <!-- 上传操作禁用时 -->
  <div
    v-if="disabled"
    class="upload-file upload-file-disabled attach-list-wrap"
  >
    <div
      v-for="(file, index) in fileList"
      :key="index"
      class="attach-list-item"
    >
      <span class="file-icon">
        <Icon icon="ep:document" />
      </span>
      <span class="file-name" :title="file.name">{{ file.name }}</span>
      <span class="file-actions">
        <el-link
          :href="file.url"
          :underline="false"
          download
          target="_blank"
          type="primary"
        >
          下载
        </el-link>
        <el-divider direction="vertical" />
        <el-link
          :underline="false"
          type="primary"
          @click="handlePreviewFile(file)"
        >
          预览
        </el-link>
      </span>
    </div>
    <div v-if="!fileList.length" class="no-file-tip"> 暂无可下载文件 </div>
  </div>
  <ele-modal
    v-model="showPreview"
    title="附件预览"
    :inner="true"
    fullscreen
    width="1100"
  >
    <vue-office-docx
      v-if="docType == 'doc' || docType == 'docx'"
      :src="docx"
      style="height: 80vh"
    />
    <vue-office-excel
      v-if="docType == 'xls' || docType == 'xlsx'"
      :src="docx"
      style="height: 80vh"
    />
    <vue-office-pdf v-if="docType == 'pdf'" :src="docx" style="height: 80vh" />
    <!-- 图片格式的话图片预览 -->
    <el-image
      v-if="docType == 'png' || docType == 'jpg' || docType == 'jpeg'"
      :src="docx"
      class="h-38px w-38px mr-10px rounded"
    />
  </ele-modal>
</template>
<script lang="ts" setup>
  import { propTypes } from '@/utils/propTypes';
  import type {
    UploadInstance,
    UploadProps,
    UploadRawFile,
    UploadUserFile
  } from 'element-plus';
  import { isString } from '@/utils/is';
  import { useUpload } from '@/components/UploadFile/src/useUpload';
  import { UploadFile } from 'element-plus/es/components/upload/src/upload';
  import { useMessage } from '@/hooks/web/useMessage';
  import { isArray } from 'lodash-es';
  //引入VueOffice组件
  import VueOfficeDocx from '@vue-office/docx';
  import VueOfficeExcel from '@vue-office/excel';
  import VueOfficePdf from '@vue-office/pdf';

  //引入相关样式
  import '@vue-office/docx/lib/index.css';
  defineOptions({ name: 'UploadFile' });

  const message = useMessage(); // 消息弹窗
  const emit = defineEmits(['update:modelValue']);

  const props = defineProps({
    modelValue: propTypes.oneOfType<string | string[]>([String, Array<String>])
      .isRequired,
    fileType: propTypes.array.def([
      'png',
      'jpg',
      'jpeg',
      'doc',
      'xls',
      'ppt',
      'txt',
      'pdf'
    ]), // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileSize: propTypes.number.def(5), // 大小限制(MB)
    limit: propTypes.number.def(5), // 数量限制
    autoUpload: propTypes.bool.def(true), // 自动上传
    drag: propTypes.bool.def(false), // 拖拽上传
    isShowTip: propTypes.bool.def(true), // 是否显示提示
    disabled: propTypes.bool.def(false), // 是否禁用上传组件 ==> 非必传（默认为 false）
    directory: propTypes.string.def(undefined) // 上传目录 ==> 非必传（默认为 undefined）
  });

  const docx = ref('');
  const showPreview = ref(false);
  const docType = ref('');
  // ========== 上传相关 ==========
  const uploadRef = ref<UploadInstance>();
  const uploadList = ref<UploadUserFile[]>([]);
  const fileList = ref<UploadUserFile[]>([]);
  const uploadNumber = ref<number>(0);

  const { uploadUrl, httpRequest } = useUpload(props.directory);

  // 文件上传之前判断
  const beforeUpload: UploadProps['beforeUpload'] = (file: UploadRawFile) => {
    if (fileList.value.length >= props.limit) {
      message.error(`上传文件数量不能超过${props.limit}个!`);
      return false;
    }
    let fileExtension = '';
    if (file.name.lastIndexOf('.') > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1);
    }
    const isImg = props.fileType.some((type: string) => {
      if (file.type.indexOf(type) > -1) return true;
      return !!(fileExtension && fileExtension.indexOf(type) > -1);
    });
    const isLimit = file.size < props.fileSize * 1024 * 1024;
    if (!isImg) {
      message.error(`文件格式不正确, 请上传${props.fileType.join('/')}格式!`);
      return false;
    }
    if (!isLimit) {
      message.error(`上传文件大小不能超过${props.fileSize}MB!`);
      return false;
    }
    message.success('正在上传文件，请稍候...');
    // 只有在验证通过后才增加计数器
    uploadNumber.value++;
    return true;
  };
  // 处理上传的文件发生变化
  // const handleFileChange = (uploadFile: UploadFile): void => {
  //   uploadRef.value.data.path = uploadFile.name
  // }
  // 文件上传成功
  const handleFileSuccess: UploadProps['onSuccess'] = (res: any): void => {
    message.success('上传成功');
    // 删除自身
    const index = fileList.value.findIndex(
      (item: any) => item.response?.data === res.data
    );
    fileList.value.splice(index, 1);
    uploadList.value.push({ name: res.data.name, url: res.data.url });
    if (uploadList.value.length == uploadNumber.value) {
      fileList.value.push(...uploadList.value);
      uploadList.value = [];
      uploadNumber.value = 0;
      emitUpdateModelValue();
    }
  };
  // 文件数超出提示
  const handleExceed: UploadProps['onExceed'] = (): void => {
    message.error(`上传文件数量不能超过${props.limit}个!`);
  };
  // 上传错误提示
  const excelUploadError: UploadProps['onError'] = (): void => {
    message.error('导入数据失败，请您重新上传！');
    // 上传失败时减少计数器，避免后续上传被阻塞
    uploadNumber.value = Math.max(0, uploadNumber.value - 1);
  };
  // 删除上传文件
  const handleRemove = (file: UploadFile) => {
    const index = fileList.value.map((f) => f.name).indexOf(file.name);
    if (index > -1) {
      fileList.value.splice(index, 1);
      emitUpdateModelValue();
    }
  };
  const handlePreview: UploadProps['onPreview'] = (uploadFile) => {
    console.log(uploadFile);
  };
  const handlePreviewFile = (file) => {
    docType.value = file.name.split('.')[1];
    if (
      docType.value != 'doc' &&
      docType.value != 'docx' &&
      docType.value != 'xls' &&
      docType.value != 'xlsx' &&
      docType.value != 'pdf' &&
      docType.value != 'png' &&
      docType.value != 'jpg' &&
      docType.value != 'jpeg'
    ) {
      message.error('当前文件无法在线预览!');
      return false;
    }
    showPreview.value = true;
    docx.value = file.url;
  };
  // 监听模型绑定值变动
  watch(
    () => props.modelValue,
    (val: any | any[]) => {
      if (!val) {
        fileList.value = []; // fix：处理掉缓存，表单重置后上传组件的内容并没有重置
        return;
      }

      fileList.value = []; // 保障数据为空
      // 情况1：字符串
      if (!isArray(val)) {
        fileList.value.push({
          name: val.name,
          url: val.url
        });
        return;
      }
      // 情况2：数组
      fileList.value.push(
        ...(val as any[]).map((item) => ({
          name: item.name,
          url: item.url
        }))
      );
    },
    { immediate: true, deep: true }
  );
  // 发送文件链接列表更新
  const emitUpdateModelValue = () => {
    emit('update:modelValue', fileList.value);
  };
</script>
<style lang="scss" scoped>
  .upload-file-disabled.attach-list-wrap {
    /* 去掉背景和边框，仅保留内边距和布局 */
    background: none;
    border-radius: 0;
    padding: 0;
    min-height: 0;
    border: none;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .attach-list-item {
    display: flex;
    align-items: center;
    width: 100%;
    min-height: 40px;
    background: #fff;
    border-radius: 6px;
    border: 1px solid #f0f0f0;
    padding: 0 16px;
    font-size: 14px;
    color: #333;
    transition: box-shadow 0.2s;
    box-sizing: border-box;
    &:hover {
      box-shadow: 0 2px 8px #f0f1f2;
      background: #f7faff;
    }
    .file-icon {
      margin-right: 8px;
      color: #409eff;
      font-size: 18px;
      display: flex;
      align-items: center;
    }
    .file-name {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 12px;
    }
    .file-actions {
      display: flex;
      align-items: center;
      gap: 6px;
      .el-link {
        font-size: 13px;
        padding: 0 2px;
      }
      .el-divider {
        margin: 0 4px;
        height: 16px;
        border-color: #e4e7ed;
      }
    }
  }

  .no-file-tip {
    color: #bbb;
    font-size: 13px;
    text-align: center;
    padding: 16px 0 8px 0;
  }
</style>
