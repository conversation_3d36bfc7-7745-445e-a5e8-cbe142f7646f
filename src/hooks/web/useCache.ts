/**
 * 配置浏览器本地存储的方式，可直接存储对象数组。
 */

import WebStorageCache from 'web-storage-cache';

type CacheType = 'localStorage' | 'sessionStorage';

export const CACHE_KEY = {
  // 用户相关
  ROLE_ROUTERS: 'roleRouters',
  USER: 'user'
};

export const useCache = (type: CacheType = 'localStorage') => {
  const wsCache: WebStorageCache = new WebStorageCache({
    storage: type
  });

  return {
    wsCache
  };
};

export const deleteUserCache = () => {
  const { wsCache } = useCache();
  wsCache.delete(CACHE_KEY.USER);
  wsCache.delete(CACHE_KEY.ROLE_ROUTERS);
};
