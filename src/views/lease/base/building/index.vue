<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="楼幢名称" prop="name">
              <el-input
                v-model.trim="queryParams.name"
                placeholder="请输入楼幢名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询
              </el-button>
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            v-permission="['lease:building-info:create']"
            :icon="Plus"
            @click="openEdit(null)"
          >
            新增
          </el-button>
          <el-button
            class="ele-btn-icon"
            v-permission="['lease:building-info:export']"
            :icon="DownloadOutlined"
            @click="exportData"
            :loading="exportLoading"
          >
            导出
          </el-button>
          <el-button :icon="Close" type="danger" @click="closeTab">
            关闭
          </el-button>
        </template>
        <template #decorationStandard="{ row }">
          <dict-data
            :code="DICT_TYPE.LEASE_DECORATION_STANDARDS"
            type="text"
            :model-value="row.decorationStandard"
          />
        </template>
        <template #houseType="{ row }">
          <dict-data
            :code="DICT_TYPE.LEASE_HOUSE_TYPE"
            type="text"
            :model-value="row.houseType"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openEdit(row)"
            v-permission="['lease:building-info:update']"
          >
            编辑
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['lease:building-info:delete']"
          />
          <el-link
            :underline="false"
            type="primary"
            @click="removeBatch(row)"
            v-permission="['lease:building-info:delete']"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <BuildingInfoForm v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { useRouter } from 'vue-router';
  import { DICT_TYPE } from '@/utils/dict';
  import { Search, Refresh, Plus, Close } from '@element-plus/icons-vue';
  import { DownloadOutlined } from '@/components/icons';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import download from '@/utils/download';
  import * as BuildingInfoApi from '@/api/lease/building/index';
  import BuildingInfoForm from './BuildingInfoForm.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { usePageTab } from '@/utils/use-page-tab';
  import { decryptParam } from '@/utils/encrypt';

  /** 楼幢信息 列表 */
  defineOptions({ name: 'BuildingInfoIndex' });

  const { routeTabKey, removePageTab } = usePageTab();
  const router = useRouter(); // 路由
  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    uuid: undefined,
    projectId: undefined,
    projectCode: undefined,
    projectName: undefined,
    code: undefined,
    name: undefined,
    houseCount: undefined,
    stewardPhone: undefined,
    buildingArea: undefined,
    totalFloor: undefined,
    roomPerFloor: undefined,
    elevatorStatus: undefined,
    decorationStandard: undefined,
    houseType: undefined,
    remark: undefined,
    status: undefined,
    deptId: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref<any>(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'name',
        label: '楼幢名称',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'houseCount',
        label: '房屋数',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'stewardPhone',
        label: '管家电话',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'buildingArea',
        label: '建筑面积',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'totalFloor',
        label: '总层数',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'decorationStandard',
        label: '装修标准',
        align: 'center',
        minWidth: 110,
        slot: 'decorationStandard'
      },
      {
        prop: 'houseType',
        label: '房屋类型',
        align: 'center',
        minWidth: 110,
        slot: 'houseType'
      },
      {
        prop: 'createTime',
        label: '创建时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 230,
        align: 'center',
        fixed: 'right',
        slot: 'action',
        hideInPrint: true,
        hideInExport: true
      }
    ];
  });
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    if (queryParams.projectId) {
      queryParams.projectId = decryptParam(queryParams.projectId);
    }
    return BuildingInfoApi.getBuildingInfoPage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await BuildingInfoApi.deleteBuildingInfo(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data = await BuildingInfoApi.exportBuildingInfo(queryParams);
      download.excel(data, '楼幢信息.xls');
    } catch {
    } finally {
      exportLoading.value = false;
    }
  };
  const closeTab = () => {
    removePageTab({ key: routeTabKey });
    router.push('/base/project');
  };
</script>
