<template>
  <ele-drawer
    form
    size="80%"
    :title="title"
    v-model="visible"
    destroy-on-close
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      v-loading="loading"
    >
      <!-- 基本信息 -->
      <div class="collapse-content">
        <span class="section-block-title">基本信息</span>
        <el-row>
          <el-col :span="8">
            <el-form-item label="楼幢名称" prop="name" required>
              <el-input v-model="form.name" placeholder="请输入楼幢名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="房屋数" prop="houseCount">
              <el-input v-model="form.houseCount" placeholder="请输入房屋数" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="管家电话" prop="stewardPhone">
              <el-input
                v-model="form.stewardPhone"
                placeholder="请输入管家电话"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="建筑面积" prop="buildingArea" required>
              <el-input
                v-model="form.buildingArea"
                placeholder="请输入建筑面积"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="总层数" prop="totalFloor">
              <el-input v-model="form.totalFloor" placeholder="请输入总层数" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="每层房间数" prop="roomPerFloor">
              <el-input
                v-model="form.roomPerFloor"
                placeholder="请输入每层房间数"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="有无电梯" prop="elevatorStatus" required>
              <dict-data
                :code="DICT_TYPE.LEASE_HAVE_OR_NONE"
                type="radio"
                valueType="number"
                v-model="form.elevatorStatus"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="装修标准" prop="decorationStandard" required>
              <dict-data
                :code="DICT_TYPE.LEASE_DECORATION_STANDARDS"
                type="select"
                v-model="form.decorationStandard"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="房屋类型" prop="houseType" required>
              <dict-data
                :code="DICT_TYPE.LEASE_HOUSE_TYPE"
                type="select"
                v-model="form.houseType"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注信息" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                :rows="2"
                placeholder="请输入备注信息"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-drawer>
</template>
<script setup lang="ts">
  import { useI18n } from 'vue-i18n';
  import { useMessage } from '@/hooks/web/useMessage';
  import { DICT_TYPE } from '@/utils/dict';
  import * as BuildingInfoApi from '@/api/lease/building/index';
  import { useFormData } from '@/utils/use-form-data';
  import { Plus } from '@element-plus/icons-vue';

  /** 楼幢信息 表单 */
  defineOptions({ name: 'BuildingInfoForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref<any>(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    uuid: undefined,
    projectId: undefined,
    projectCode: undefined,
    projectName: undefined,
    code: undefined,
    name: undefined,
    houseCount: undefined,
    stewardPhone: undefined,
    buildingArea: undefined,
    totalFloor: undefined,
    roomPerFloor: undefined,
    elevatorStatus: undefined,
    decorationStandard: undefined,
    houseType: undefined,
    remark: undefined,
    status: undefined
  });
  /** 表单验证规则 */
  const rules = reactive({
    name: [{ required: true, message: '楼幢名称不能为空', trigger: 'blur' }],
    buildingArea: [
      { required: true, message: '建筑面积不能为空', trigger: 'blur' }
    ],
    elevatorStatus: [
      { required: true, message: '请维护电梯情况', trigger: 'blur' }
    ],
    decorationStandard: [
      { required: true, message: '装修标准不能为空', trigger: 'blur' }
    ],
    totalFloor: [
      { required: true, message: '总层数不能为空', trigger: 'blur' }
    ],
    roomPerFloor: [
      { required: true, message: '每层房间数不能为空', trigger: 'blur' }
    ],
    houseType: [
      { required: true, message: '房屋类型不能为空', trigger: 'blur' }
    ]
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        await BuildingInfoApi.createBuildingInfo(form);
        message.success(t('common.createSuccess'));
      } else {
        await BuildingInfoApi.updateBuildingInfo(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await BuildingInfoApi.getBuildingInfo(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
<style scoped>
  /* 模态框标题分隔线 */
  :deep(.ele-modal .el-dialog__header) {
    border-bottom: 1px solid #e4e7ed;
    padding-bottom: 16px;
    margin-bottom: 0;
  }

  :deep(.ele-modal .el-dialog__body) {
    padding-top: 20px;
  }

  .form-collapse {
    background: transparent;
    border: none;
  }

  .form-collapse :deep(.el-collapse-item) {
    margin-bottom: 16px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e4e7ed;
  }

  .form-collapse :deep(.el-collapse-item__header) {
    background: #f8f9fa;
    border-bottom: 1px solid #e4e7ed;
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    border-radius: 8px 8px 0 0;
  }

  .form-collapse :deep(.el-collapse-item__content) {
    padding: 0;
    border: none;
  }

  .collapse-content {
    padding: 5px;
  }

  .section-block-title {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    padding: 12px 0 12px 20px;
    background: #f5f7fa;
    border-left: 4px solid #409eff;
    border-radius: 0 4px 4px 0;
    position: relative;
    transition: all 0.3s ease;
  }

  .section-block-title:hover {
    background: #ecf5ff;
    color: #409eff;
    transform: translateX(2px);
  }

  .survey-note {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #fff7e6;
    border: 1px solid #ffd591;
    border-radius: 6px;
    color: #d46b08;
    font-size: 14px;
  }

  .survey-note .el-icon {
    color: #fa8c16;
    font-size: 16px;
  }

  .facilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px 24px;
    padding: 16px 0;
  }

  .facilities-grid .el-checkbox {
    margin: 0;
  }

  .facilities-grid .el-checkbox__label {
    font-size: 14px;
    color: #333;
  }

  /* 经纬度输入组件样式 */
  .coordinate-input-container {
    width: 100%;
  }

  .coordinate-label {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
    color: #606266;
  }

  .required-star {
    color: #f56c6c;
    margin-right: 4px;
  }

  .coordinate-prefix {
    font-size: 14px;
    color: #909399;
    font-weight: 500;
    min-width: 16px;
  }

  .coordinate-input {
    flex: 1;
  }

  .coordinate-input :deep(.el-input__wrapper) {
    background: transparent;
    box-shadow: none;
  }

  .coordinate-input :deep(.el-input__inner) {
    background: transparent;
    border: none;
    padding: 0;
    font-size: 13px;
    color: #303133;
  }

  .coordinate-input :deep(.el-input__inner::placeholder) {
    color: #c0c4cc;
    font-size: 12px;
  }

  .coordinate-icon {
    display: flex;
    align-items: center;
    margin-left: 8px;
  }

  .location-icon {
    font-size: 16px;
    color: #909399;
    cursor: pointer;
    transition: color 0.2s;
  }

  .location-icon:hover {
    color: #409eff;
  }

  /* 页面头部紧凑样式 */
  .page-header-card {
    margin-bottom: 12px;
  }

  .page-header-card :deep(.ele-card__body) {
    padding: 8px 16px;
  }

  .custom-page-header {
    padding: 0;
    min-height: auto;
  }

  .custom-page-header :deep(.el-page-header__left) {
    margin-right: 12px;
  }

  .custom-page-header :deep(.el-page-header__content) {
    flex: 1;
  }

  .header-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background: #409eff;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .header-icon:hover {
    background: #337ecc;
  }

  .back-icon {
    color: white;
    font-size: 14px;
  }

  .header-content {
    flex: 1;
    display: flex;
    align-items: center;
  }

  .page-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin: 0;
    line-height: 1;
  }

  .header-actions {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .header-actions .el-button {
    height: 28px;
    padding: 0 12px;
    font-size: 13px;
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .facilities-grid {
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
      gap: 12px 20px;
    }
  }

  @media (max-width: 768px) {
    .project-form-container {
      padding: 16px;
    }

    .collapse-content {
      padding: 20px;
    }

    .form-collapse :deep(.el-collapse-item) {
      margin-bottom: 12px;
    }

    .facilities-grid {
      grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
      gap: 10px 16px;
    }
  }

  @media (max-width: 480px) {
    .project-form-container {
      padding: 12px;
    }

    .collapse-content {
      padding: 16px;
    }

    .form-collapse :deep(.el-collapse-item__header) {
      padding: 12px 16px;
      font-size: 14px;
    }

    .facilities-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 8px 12px;
    }
  }
</style>
