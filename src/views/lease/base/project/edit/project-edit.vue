<template>
  <ele-modal
    form
    :width="1200"
    :inner="true"
    fullscreen
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
    
    @close="cancle"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="140px"
      v-loading="loading"
    >
      <!-- 项目信息 -->
      <div class="collapse-content">
        <span class="section-block-title">项目信息</span>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="项目名称" prop="name" required>
              <el-input style="width: 70%" v-model="form.name" />
              <!-- 增加一个按钮地图选点 -->
              <span style="width: 5%"></span>
              <el-button
                style="width: 25%"
                @click="handleSelectAddress"
                type="primary"
                >地图选点</el-button
              >
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目地址" prop="projectAddress" required>
              <el-input v-model="form.projectAddress" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目别名" prop="projectAlias">
              <el-input v-model="form.projectAlias" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="楼栋数" prop="buildingCount">
              <el-input v-model="form.buildingCount" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="门店名称" prop="storeName" required>
              <el-input v-model="form.storeName" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="管家电话" prop="stewardPhone">
              <el-input v-model="form.stewardPhone" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="区域" prop="region" required>
              <dict-data
                :code="DICT_TYPE.LEASE_REGION_INFO"
                type="select"
                v-model="form.region"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="物业费(元/月/㎡)" prop="propertyFee" required>
              <!-- 两位小数，只能为正 -->
              <el-input-number
                v-model="form.propertyFee"
                controls-position="right"
                :precision="2"
                :min="0"
                class="ele-fluid"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="水费(元/方)" prop="heatingFee">
              <el-input-number
                v-model="form.heatingFee"
                controls-position="right"
                :precision="2"
                :min="0"
                class="ele-fluid"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="电费(元/度)" prop="heatingFee">
              <el-input-number
                v-model="form.heatingFee"
                controls-position="right"
                :precision="2"
                :min="0"
                class="ele-fluid"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="暖气费(元/㎡)" prop="heatingFee">
              <el-input-number
                v-model="form.heatingFee"
                controls-position="right"
                :precision="2"
                :min="0"
                class="ele-fluid"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="燃气费(元/立方)" prop="gasFee">
              <el-input-number
                v-model="form.gasFee"
                controls-position="right"
                placeholder="请输入燃气费"
                :precision="2"
                :min="0"
                class="ele-fluid"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="水电交割" prop="waterElectricitySeparation">
              <el-switch v-model="form.waterElectricitySeparation" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收取物业费" prop="collectPropertyFee">
              <el-switch v-model="form.collectPropertyFee" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="房源业态" prop="feeManagement">
              <dict-data
                :code="DICT_TYPE.LEASE_HOUSE_SOURCE_BUSINESS_TYPE"
                type="select"
                multiple
                v-model="form.feeManagement"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <!-- 经纬度 -->

            <el-form-item label="地图标点" prop="addressInfo" required>
              <el-input
                v-model="form.addressInfo"
                placeholder="经：117.001956209 纬：36.6848077450"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="经营范围" prop="businessScope" required>
              <el-select v-model="form.businessScope" placeholder="请选择">
                <el-option label="请选择" value="" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item prop="facilities" label="配套设施">
              <el-checkbox-group v-model="form.facilities">
                <el-checkbox value="空调">空调</el-checkbox>
                <el-checkbox value="供暖">供暖</el-checkbox>
                <el-checkbox value="智能门禁">智能门禁</el-checkbox>
                <el-checkbox value="公区管理">公区管理</el-checkbox>
                <el-checkbox value="网络">网络</el-checkbox>
                <el-checkbox value="电梯">电梯</el-checkbox>
                <el-checkbox value="停车位">停车位</el-checkbox>
                <el-checkbox value="保安">保安</el-checkbox>
                <el-checkbox value="保洁">保洁</el-checkbox>
                <el-checkbox value="会议室">会议室</el-checkbox>
                <el-checkbox value="前台接待">前台接待</el-checkbox>
                <el-checkbox value="休息区">休息区</el-checkbox>
                <el-checkbox value="茶水间">茶水间</el-checkbox>
                <el-checkbox value="影印设备">影印设备</el-checkbox>
                <el-checkbox value="前台">前台</el-checkbox>
                <el-checkbox value="会客厅">会客厅</el-checkbox>
                <el-checkbox value="公共WIFI">公共WIFI</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="collapse-content">
        <span class="section-block-title">特色标签</span>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="标签1" prop="featureTag1">
              <el-select v-model="form.featureTag1" placeholder="请选择">
                <el-option label="请选择" value="" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="标签2" prop="featureTag2">
              <el-select v-model="form.featureTag2" placeholder="请选择">
                <el-option label="请选择" value="" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="标签3" prop="featureTag3">
              <el-select v-model="form.featureTag2" placeholder="请选择">
                <el-option label="请选择" value="" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="标签4" prop="featureTag4">
              <el-select v-model="form.featureTag2" placeholder="请选择">
                <el-option label="请选择" value="" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 项目介绍 -->
      <div class="collapse-content">
        <el-form-item prop="projectDescription">
          <el-input
            v-model="form.projectDescription"
            type="textregion"
            :rows="4"
            placeholder="请输入项目介绍"
          />
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
    <!-- 地图位置选择弹窗 -->
    <ele-map-picker
      v-model="mapVisible"
      :return-regions="true"
      :selected="selectedData"
      :dark-mode="darkMode"
      mode="keyword"
      height="100%"
      :modal-props="{
        bodyStyle: { height: '460px', minHeight: '100%', maxHeight: '100%' },
        maxable: true,
        resizable: true,
        resizeIconStyle: { zIndex: 200 },
        minWidth: 372,
        minHeight: 280
      }"
      :side-style="{ maxWidth: '448px' }"
      :suggestionCity="suggestionCity"
      :center="center"
      :zoom="10"
      @done="handleChoose"
    />
  </ele-modal>
</template>
<script setup lang="ts">
  import { useFormData } from '@/utils/use-form-data';
  import { Warning } from '@element-plus/icons-vue';
  import { useThemeStore } from '@/store/modules/theme';
  import { storeToRefs } from 'pinia';
  import { DICT_TYPE } from '@/utils/dict';
  /** 项目编辑表单 */
  defineOptions({ name: 'ProjectEditForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const suggestionCity = ref('济南市');
  const center = ref([117.123352, 36.670507]);
  /** 是否显示地图选择弹窗 */
  const mapVisible = ref(false);
  /** 回显选中的位置 */
  const selectedData = ref<any>();
  const themeStore = useThemeStore();
  const { darkMode } = storeToRefs(themeStore);
  /** 地图选择后回调 */
  const handleChoose = (location: any) => {
    console.log(JSON.parse(JSON.stringify(location)));
    assignFields({
      name: location.name,
      projectAddress: `${location.city?.province}${location.city?.city}${location.city?.district}${location.address}`,
      mapLongitude: location.lng,
      mapLatitude: location.lat
    });
    mapVisible.value = false;
    selectedData.value = location;
  };
  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref<any>(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    // 项目基本信息
    name: '',
    projectAddress: '',
    projectAlias: '',
    buildingCount: '',
    storeName: '',
    stewardPhone: '',
    region: '',
    propertyFee: '',
    heatingFee: '',
    gasFee: '',
    waterElectricitySeparation: false,
    collectPropertyFee: false,
    feeManagement: false,
    contractTemplate: '',
    platformContractTemplate: '',
    renewalContractTemplate: '',
    propertyStatus: '',
    buildingManager: '',
    mapLongitude: '', //经度
    mapLatitude: '', //纬度
    businessScope: '',
    facilities: [],

    // 综合信息
    featureTag1: '',
    comprehensive2: '',
    comprehensive3: '',
    comprehensive4: '',

    // 项目介绍
    projectDescription: ''
  });
  /** 表单验证规则 */
  const rules = reactive({
    name: [
      {
        required: true,
        message: '项目名称不能为空',
        trigger: 'blur'
      }
    ],
    storeName: [
      {
        required: true,
        message: '门店名称不能为空',
        trigger: 'blur'
      }
    ],
    propertyFee: [
      {
        required: true,
        message: '物业费不能为空',
        trigger: 'blur'
      }
    ],
    propertyStatus: [
      {
        required: true,
        message: '房源状态不能为空',
        trigger: 'change'
      }
    ],
    buildingManager: [
      {
        required: true,
        message: '大厦负责人不能为空',
        trigger: 'change'
      }
    ],
    addressInfo: [
      {
        required: true,
        message: '地图标点不能为空',
        trigger: 'blur'
      }
    ],
    businessScope: [
      {
        required: true,
        message: '经营范围不能为空',
        trigger: 'change'
      }
    ]
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
    emit('done');
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        message.success(t('common.createSuccess'));
      } else {
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };
  //地图选点
  const handleSelectAddress = () => {
    mapVisible.value = true;
  };
  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        assignFields({});
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value
        ? t('action.update') + '项目'
        : t('action.create') + '项目';
    } finally {
      loading.value = false;
    }
  };
</script>

<style scoped>
  /* 模态框标题分隔线 */
  :deep(.ele-modal .el-dialog__header) {
    border-bottom: 1px solid #e4e7ed;
    padding-bottom: 16px;
    margin-bottom: 0;
  }

  :deep(.ele-modal .el-dialog__body) {
    padding-top: 20px;
  }

  .form-collapse {
    background: transparent;
    border: none;
  }

  .form-collapse :deep(.el-collapse-item) {
    margin-bottom: 16px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e4e7ed;
  }

  .form-collapse :deep(.el-collapse-item__header) {
    background: #f8f9fa;
    border-bottom: 1px solid #e4e7ed;
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    border-radius: 8px 8px 0 0;
  }

  .form-collapse :deep(.el-collapse-item__content) {
    padding: 0;
    border: none;
  }

  .collapse-content {
    padding: 5px;
  }

  .section-block-title {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    padding: 12px 0 12px 20px;
    background: #f5f7fa;
    border-left: 4px solid #409eff;
    border-radius: 0 4px 4px 0;
    position: relative;
    transition: all 0.3s ease;
  }

  .section-block-title:hover {
    background: #ecf5ff;
    color: #409eff;
    transform: translateX(2px);
  }

  .survey-note {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #fff7e6;
    border: 1px solid #ffd591;
    border-radius: 6px;
    color: #d46b08;
    font-size: 14px;
  }

  .survey-note .el-icon {
    color: #fa8c16;
    font-size: 16px;
  }

  .facilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px 24px;
    padding: 16px 0;
  }

  .facilities-grid .el-checkbox {
    margin: 0;
  }

  .facilities-grid .el-checkbox__label {
    font-size: 14px;
    color: #333;
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .facilities-grid {
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
      gap: 12px 20px;
    }
  }

  @media (max-width: 768px) {
    .project-form-container {
      padding: 16px;
    }

    .collapse-content {
      padding: 20px;
    }

    .form-collapse :deep(.el-collapse-item) {
      margin-bottom: 12px;
    }

    .facilities-grid {
      grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
      gap: 10px 16px;
    }
  }

  @media (max-width: 480px) {
    .project-form-container {
      padding: 12px;
    }

    .collapse-content {
      padding: 16px;
    }

    .form-collapse :deep(.el-collapse-item__header) {
      padding: 12px 16px;
      font-size: 14px;
    }

    .facilities-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 8px 12px;
    }
  }
</style>
