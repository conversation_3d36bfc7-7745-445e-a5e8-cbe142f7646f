<template>
  <ele-modal
    form
    :width="1200"
    :inner="true"
    fullscreen
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    @open="handleOpen"
    :show-close="false"
    @close="cancle"
    :header-style="{ padding: 0 }"
  >
    <ele-card>
      <el-page-header @back="cancle">
        <template #content>
          <div class="flex items-center">
            <span class="text-small font-600 mr-3"> 合同维护 </span>
          </div>
        </template>
        <template #extra>
          <el-button @click="cancle">关闭</el-button>
          <el-button @click="save" type="primary" :loading="loading"
            >保存</el-button
          >
        </template>
      </el-page-header>
    </ele-card>
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="140px"
      v-loading="loading"
    >
      <!-- 项目信息 -->
      <div class="collapse-content">
        <span class="section-block-title">项目信息</span>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="项目名称" prop="name" required>
              <el-input style="width: 70%" v-model="form.name" />
              <!-- 增加一个按钮地图选点 -->
              <span style="width: 5%"></span>
              <el-button
                style="width: 25%"
                @click="handleSelectAddress"
                type="primary"
                >地图选点</el-button
              >
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目地址" prop="projectAddress" required>
              <el-input v-model="form.projectAddress" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目别名" prop="projectAlias">
              <el-input v-model="form.projectAlias" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="楼栋数" prop="buildingCount">
              <el-input v-model="form.buildingCount" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="门店名称" prop="storeName" required>
              <el-input v-model="form.storeName" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="管家电话" prop="stewardPhone">
              <el-input v-model="form.stewardPhone" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="区域" prop="region" required>
              <dict-data
                :code="DICT_TYPE.LEASE_REGION_INFO"
                type="select"
                valueType="number"
                v-model="form.region"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="物业费(元/月/㎡)" prop="propertyFee" required>
              <!-- 两位小数，只能为正 -->
              <el-input-number
                v-model="form.propertyFee"
                controls-position="right"
                :precision="2"
                :min="0"
                class="ele-fluid"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="水费(元/立方)" prop="waterFee">
              <el-input-number
                v-model="form.waterFee"
                controls-position="right"
                :precision="2"
                :min="0"
                class="ele-fluid"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="电费(元/度)" prop="electricityFee">
              <el-input-number
                v-model="form.electricityFee"
                controls-position="right"
                :precision="2"
                :min="0"
                class="ele-fluid"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="暖气费(元/㎡)" prop="heatingFee">
              <el-input-number
                v-model="form.heatingFee"
                controls-position="right"
                :precision="2"
                :min="0"
                class="ele-fluid"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="燃气费(元/立方)" prop="gasFee">
              <el-input-number
                v-model="form.gasFee"
                controls-position="right"
                placeholder="请输入燃气费"
                :precision="2"
                :min="0"
                class="ele-fluid"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="水电交割" prop="waterElectricitySeparation">
              <dict-data
                :code="DICT_TYPE.LEASE_YES_OR_NO"
                type="radio"
                valueType="number"
                v-model="form.waterElectricitySeparation"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收取物业费" prop="collectPropertyFee">
              <dict-data
                :code="DICT_TYPE.LEASE_YES_OR_NO"
                type="radio"
                valueType="number"
                v-model="form.collectPropertyFee"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="房源业态" prop="houseSourceBusinessType">
              <dict-data
                :code="DICT_TYPE.LEASE_HOUSE_SOURCE_BUSINESS_TYPE"
                type="select"
                multiple
                v-model="form.houseSourceBusinessType"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <!-- 经纬度 -->
            <el-form-item label="地图标点" prop="addressInfo" required>
              <span class="coordinate-prefix">经</span>
              <el-input
                v-model="form.mapLongitude"
                class="coordinate-input"
                @input="updateAddressInfo"
              />
              <span class="coordinate-prefix">纬</span>
              <el-input
                v-model="form.mapLatitude"
                class="coordinate-input"
                @input="updateAddressInfo"
              />
              <div class="coordinate-icon">
                <el-icon class="location-icon" @click="handleClickAddress">
                  <LocationFilled />
                </el-icon>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item prop="facilities" label="配套设施">
              <el-checkbox-group v-model="form.facilities">
                <el-checkbox value="空调">空调</el-checkbox>
                <el-checkbox value="供暖">供暖</el-checkbox>
                <el-checkbox value="智能门禁">智能门禁</el-checkbox>
                <el-checkbox value="公区管理">公区管理</el-checkbox>
                <el-checkbox value="网络">网络</el-checkbox>
                <el-checkbox value="电梯">电梯</el-checkbox>
                <el-checkbox value="停车位">停车位</el-checkbox>
                <el-checkbox value="保安">保安</el-checkbox>
                <el-checkbox value="保洁">保洁</el-checkbox>
                <el-checkbox value="会议室">会议室</el-checkbox>
                <el-checkbox value="前台接待">前台接待</el-checkbox>
                <el-checkbox value="休息区">休息区</el-checkbox>
                <el-checkbox value="茶水间">茶水间</el-checkbox>
                <el-checkbox value="影印设备">影印设备</el-checkbox>
                <el-checkbox value="前台">前台</el-checkbox>
                <el-checkbox value="会客厅">会客厅</el-checkbox>
                <el-checkbox value="公共WIFI">公共WIFI</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item prop="markInfo" label="项目备注">
              <el-input
                v-model="form.markInfo"
                type="textarea"
                :rows="3"
                placeholder="项目备注"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="collapse-content">
        <span class="section-block-title">特色标签</span>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="标签1" prop="featureTag1">
              <el-select v-model="form.featureTag1" placeholder="请选择">
                <el-option label="请选择" value="" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="标签2" prop="featureTag2">
              <el-select v-model="form.featureTag2" placeholder="请选择">
                <el-option label="请选择" value="" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="标签3" prop="featureTag3">
              <el-select v-model="form.featureTag2" placeholder="请选择">
                <el-option label="请选择" value="" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="标签4" prop="featureTag4">
              <el-select v-model="form.featureTag2" placeholder="请选择">
                <el-option label="请选择" value="" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 项目介绍 -->
      <div class="collapse-content">
        <span class="section-block-title">项目简介</span>
        <!-- 编辑器 -->
        <tinymce-editor
          ref="editorRef"
          :init="config"
          v-model="form.projectIntro"
          :disabled="disabled"
        />
      </div>
    </el-form>
    <!-- 地图位置选择弹窗 -->
    <ele-map-picker
      v-model="mapVisible"
      :return-regions="true"
      :selected="selectedData"
      :dark-mode="darkMode"
      mode="keyword"
      height="100%"
      :modal-props="{
        bodyStyle: { height: '460px', minHeight: '100%', maxHeight: '100%' },
        maxable: true,
        resizable: true,
        resizeIconStyle: { zIndex: 200 },
        minWidth: 372,
        minHeight: 280
      }"
      :side-style="{ maxWidth: '448px' }"
      :suggestionCity="suggestionCity"
      :center="center"
      :zoom="10"
      @done="handleChoose"
    />
    <!-- 地图标点弹窗 -->
    <ele-map-picker
      v-model="clickVisible"
      :return-regions="true"
      :selected="selectedData"
      :dark-mode="darkMode"
      mode="lnglat"
      height="100%"
      :modal-props="{
        bodyStyle: { height: '460px', minHeight: '100%', maxHeight: '100%' },
        maxable: true,
        resizable: true,
        resizeIconStyle: { zIndex: 200 },
        minWidth: 372,
        minHeight: 280
      }"
      :side-style="{ maxWidth: '448px' }"
      :suggestionCity="suggestionCity"
      :center="center"
      :zoom="10"
      @done="handleClickChoose"
    />
  </ele-modal>
</template>
<script setup lang="ts">
  import { useFormData } from '@/utils/use-form-data';
  import { LocationFilled } from '@element-plus/icons-vue';
  import { useThemeStore } from '@/store/modules/theme';
  import { storeToRefs } from 'pinia';
  import { DICT_TYPE } from '@/utils/dict';
  import TinymceEditor from '@/components/TinymceEditor/index.vue';
  import * as ProjectApi from '@/api/lease/project/index';

  /** 项目编辑表单 */
  defineOptions({ name: 'ProjectEditForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });
  /** 编辑器配置 */
  const config = ref({
    height: 520
  });
  /** 是否禁用 */
  const disabled = ref(false);
  const suggestionCity = ref('济南市');
  const center = ref([117.123352, 36.670507]);
  /** 是否显示地图选择弹窗 */
  const mapVisible = ref(false);
  const clickVisible = ref(false);
  /** 回显选中的位置 */
  const selectedData = ref<any>();
  const themeStore = useThemeStore();
  const { darkMode } = storeToRefs(themeStore);
  /** 地图选择后回调 */
  const handleChoose = (location: any) => {
    setFieldValue('name', location.name);
    setFieldValue(
      'projectAddress',
      `${location.city?.province}${location.city?.city}${location.city?.district}${location.address}`
    );
    setFieldValue('mapLongitude', location.lng);
    setFieldValue('mapLatitude', location.lat);
    setFieldValue('addressInfo', `经：${location.lng} 纬：${location.lat}`);
    mapVisible.value = false;
    selectedData.value = location;
  };
  const handleClickChoose = (location: any) => {
    setFieldValue('mapLongitude', location.lng);
    setFieldValue('mapLatitude', location.lat);
    clickVisible.value = false;
  };
  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(undefined); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref<any>(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    // 项目基本信息
    id: undefined,
    code: undefined,
    name: undefined,
    projectAddress: undefined,
    projectAlias: undefined,
    buildingCount: undefined,
    storeName: undefined,
    stewardPhone: undefined,
    region: undefined,
    propertyFee: undefined,
    waterFee: undefined,
    electricityFee: undefined,
    heatingFee: undefined,
    gasFee: undefined,
    waterElectricitySeparation: 0,
    collectPropertyFee: 0,
    houseSourceBusinessType: undefined,
    mapLongitude: undefined, //经度
    mapLatitude: undefined, //纬度
    facilities: [],
    featureTag1: undefined,
    featureTag2: undefined,
    featureTag3: undefined,
    featureTag4: undefined,
    // 项目介绍
    projectIntro: undefined,
    markInfo: undefined,
    contractTemplate: undefined,
    platformContractTemplate: undefined,
    renewalContractTemplate: undefined,
    addressInfo: undefined //地图标点信息
  });
  /** 表单验证规则 */
  const rules = reactive({
    name: [
      {
        required: true,
        message: '项目名称不能为空',
        trigger: 'blur'
      }
    ],
    storeName: [
      {
        required: true,
        message: '门店名称不能为空',
        trigger: 'blur'
      }
    ],
    projectAddress: [
      {
        required: true,
        message: '项目地址不能为空',
        trigger: 'blur'
      }
    ],
    region: [
      {
        required: true,
        message: '区域不能为空',
        trigger: 'blur'
      }
    ],
    propertyFee: [
      {
        required: true,
        message: '物业费不能为空',
        trigger: 'blur'
      }
    ],
    addressInfo: [
      {
        required: true,
        message: '地图标点不能为空',
        trigger: 'blur',
        validator: (rule: any, value: any, callback: any) => {
          if (!form.mapLongitude || !form.mapLatitude) {
            callback(new Error('地图标点不能为空'));
          } else {
            callback();
          }
        }
      }
    ]
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
    emit('done');
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        form.facilities = form.facilities.join(',');
        await ProjectApi.createProject(form);
        message.success(t('common.createSuccess'));
      } else {
        form.facilities = form.facilities.join(',');
        await ProjectApi.updateProject(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };
  //地图选点
  const handleSelectAddress = () => {
    mapVisible.value = true;
  };
  //地图标点
  const handleClickAddress = () => {
    clickVisible.value = true;
  };
  // 更新地址信息字段
  const updateAddressInfo = () => {
    if (form.mapLongitude && form.mapLatitude) {
      form.addressInfo = `经：${form.mapLongitude} 纬：${form.mapLatitude}`;
    } else {
      form.addressInfo = undefined;
    }
  };
  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        //获取当前的项目信息
        const result = await ProjectApi.getProject(props.data.id);
        result.facilities = result.facilities.split(',');
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
    } finally {
      loading.value = false;
    }
  };
</script>

<style scoped>
  /* 模态框标题分隔线 */
  :deep(.ele-modal .el-dialog__header) {
    border-bottom: 1px solid #e4e7ed;
    padding-bottom: 16px;
    margin-bottom: 0;
  }

  :deep(.ele-modal .el-dialog__body) {
    padding-top: 20px;
  }

  .form-collapse {
    background: transparent;
    border: none;
  }

  .form-collapse :deep(.el-collapse-item) {
    margin-bottom: 16px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e4e7ed;
  }

  .form-collapse :deep(.el-collapse-item__header) {
    background: #f8f9fa;
    border-bottom: 1px solid #e4e7ed;
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    border-radius: 8px 8px 0 0;
  }

  .form-collapse :deep(.el-collapse-item__content) {
    padding: 0;
    border: none;
  }

  .collapse-content {
    padding: 5px;
  }

  .section-block-title {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    padding: 12px 0 12px 20px;
    background: #f5f7fa;
    border-left: 4px solid #409eff;
    border-radius: 0 4px 4px 0;
    position: relative;
    transition: all 0.3s ease;
  }

  .section-block-title:hover {
    background: #ecf5ff;
    color: #409eff;
    transform: translateX(2px);
  }

  .survey-note {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #fff7e6;
    border: 1px solid #ffd591;
    border-radius: 6px;
    color: #d46b08;
    font-size: 14px;
  }

  .survey-note .el-icon {
    color: #fa8c16;
    font-size: 16px;
  }

  .facilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px 24px;
    padding: 16px 0;
  }

  .facilities-grid .el-checkbox {
    margin: 0;
  }

  .facilities-grid .el-checkbox__label {
    font-size: 14px;
    color: #333;
  }

  /* 经纬度输入组件样式 */
  .coordinate-input-container {
    width: 100%;
  }

  .coordinate-label {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
    color: #606266;
  }

  .required-star {
    color: #f56c6c;
    margin-right: 4px;
  }

  .coordinate-prefix {
    font-size: 14px;
    color: #909399;
    font-weight: 500;
    min-width: 16px;
  }

  .coordinate-input {
    flex: 1;
  }

  .coordinate-input :deep(.el-input__wrapper) {
    background: transparent;
    box-shadow: none;
  }

  .coordinate-input :deep(.el-input__inner) {
    background: transparent;
    border: none;
    padding: 0;
    font-size: 13px;
    color: #303133;
  }

  .coordinate-input :deep(.el-input__inner::placeholder) {
    color: #c0c4cc;
    font-size: 12px;
  }

  .coordinate-icon {
    display: flex;
    align-items: center;
    margin-left: 8px;
  }

  .location-icon {
    font-size: 16px;
    color: #909399;
    cursor: pointer;
    transition: color 0.2s;
  }

  .location-icon:hover {
    color: #409eff;
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .facilities-grid {
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
      gap: 12px 20px;
    }
  }

  @media (max-width: 768px) {
    .project-form-container {
      padding: 16px;
    }

    .collapse-content {
      padding: 20px;
    }

    .form-collapse :deep(.el-collapse-item) {
      margin-bottom: 12px;
    }

    .facilities-grid {
      grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
      gap: 10px 16px;
    }
  }

  @media (max-width: 480px) {
    .project-form-container {
      padding: 12px;
    }

    .collapse-content {
      padding: 16px;
    }

    .form-collapse :deep(.el-collapse-item__header) {
      padding: 12px 16px;
      font-size: 14px;
    }

    .facilities-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 8px 12px;
    }
  }
</style>
