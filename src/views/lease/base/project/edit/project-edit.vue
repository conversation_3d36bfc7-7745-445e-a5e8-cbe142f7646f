<template>
  <ele-modal
    form
    :width="1200"
    :inner="true"
    fullscreen
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
    @close="cancle"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="140px"
      v-loading="loading"
    >
      <!-- 项目信息 -->
      <div class="collapse-content">
        <span class="section-block-title">项目信息</span>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="项目名称" prop="projectName" required>
              <el-input
                v-model="form.projectName"
                placeholder="城发成都时代中心A项目"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目地址" prop="projectAddress">
              <el-input
                v-model="form.projectAddress"
                placeholder="济南市天桥区北园大街596号东亚商厦6层"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目名称" prop="projectDisplayName">
              <el-input
                v-model="form.projectDisplayName"
                placeholder="北园时代中心A项目(简介)"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="建筑面积" prop="buildingArea">
              <el-input
                v-model="form.buildingArea"
                placeholder="请输入建筑面积"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="门店名称" prop="storeName" required>
              <el-select
                v-model="form.storeName"
                placeholder="北园时代中心A项目(简介)"
              >
                <el-option
                  label="北园时代中心A项目(简介)"
                  value="北园时代中心A项目(简介)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="管理电话" prop="managementPhone">
              <el-input
                v-model="form.managementPhone"
                placeholder="请输入管理电话"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="区域" prop="area">
              <el-select v-model="form.area" placeholder="天桥区">
                <el-option label="天桥区" value="天桥区" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="物业费(元/月)" prop="propertyFee" required>
              <el-input v-model="form.propertyFee" placeholder="6.5" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="暖气费(元/月)" prop="heatingFee">
              <el-input v-model="form.heatingFee" placeholder="请输入暖气费" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="水费(元/方)" prop="waterFee">
              <el-input v-model="form.waterFee" placeholder="请输入水费" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="电费(元/度)" prop="electricityFee">
              <el-input
                v-model="form.electricityFee"
                placeholder="请输入电费"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="燃气费(元/方)" prop="gasFee">
              <el-input v-model="form.gasFee" placeholder="请输入燃气费" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="水收费" prop="waterCollection">
              <el-switch v-model="form.waterCollection" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收费标准" prop="feeStandard">
              <el-switch v-model="form.feeStandard" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收费管理" prop="feeManagement">
              <el-switch v-model="form.feeManagement" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="合同模板" prop="contractTemplate">
              <el-input
                v-model="form.contractTemplate"
                placeholder="房产、商业公司管理委员会标准租赁合同模板"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="平台合同模板" prop="platformContractTemplate">
              <el-input
                v-model="form.platformContractTemplate"
                placeholder="房产、平台公司管理委员会标准租赁合同模板"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="续签合同模板" prop="renewalContractTemplate">
              <el-input
                v-model="form.renewalContractTemplate"
                placeholder="房产、续签公司管理委员会标准租赁合同模板"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="房源状态" prop="propertyStatus" required>
              <el-select
                v-model="form.propertyStatus"
                placeholder="请选择房源状态"
              >
                <el-option label="可租分区" value="可租分区" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="大厦负责人" prop="buildingManager" required>
              <el-select v-model="form.buildingManager" placeholder="友布">
                <el-option label="友布" value="友布" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="地址信息" prop="addressInfo" required>
              <el-input
                v-model="form.addressInfo"
                placeholder="经：117.001956209 纬：36.6848077450"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="经营范围" prop="businessScope" required>
              <el-select v-model="form.businessScope" placeholder="请选择">
                <el-option label="请选择" value="" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="客户入驻调研">
              <div class="survey-note">
                <el-icon><Warning /></el-icon>
                <span>提醒：在入驻客户之前，请先做好入驻客户调研</span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="国外客户入驻调研">
              <div class="survey-note">
                <el-icon><Warning /></el-icon>
                <span
                  >提醒：在入驻客户之前，请先做好入驻客户调研，请务必注意入驻客户的合规性，避免违法违规行为</span
                >
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 配套设施 -->
      <div class="collapse-content">
        <span class="section-block-title">配套设施</span>
        <div class="facilities-grid">
          <el-checkbox v-model="form.facilities.airCondition">空调</el-checkbox>
          <el-checkbox v-model="form.facilities.heating">供暖</el-checkbox>
          <el-checkbox v-model="form.facilities.smartAccess"
            >智能门禁</el-checkbox
          >
          <el-checkbox v-model="form.facilities.publicArea"
            >公区管理</el-checkbox
          >
          <el-checkbox v-model="form.facilities.network">网络</el-checkbox>
          <el-checkbox v-model="form.facilities.elevator">电梯</el-checkbox>
          <el-checkbox v-model="form.facilities.parking">停车场</el-checkbox>
          <el-checkbox v-model="form.facilities.security">保安</el-checkbox>
          <el-checkbox v-model="form.facilities.cleaning">保洁</el-checkbox>
          <el-checkbox v-model="form.facilities.meeting">会议室</el-checkbox>
          <el-checkbox v-model="form.facilities.reception"
            >前台接待</el-checkbox
          >
          <el-checkbox v-model="form.facilities.restArea">休息区</el-checkbox>
          <el-checkbox v-model="form.facilities.kitchen">茶水间</el-checkbox>
          <el-checkbox v-model="form.facilities.printer">影印设备</el-checkbox>
          <el-checkbox v-model="form.facilities.frontDesk">前台</el-checkbox>
          <el-checkbox v-model="form.facilities.conference">会客厅</el-checkbox>
          <el-checkbox v-model="form.facilities.publicWifi"
            >公共WIFI</el-checkbox
          >
        </div>
      </div>

      <div class="collapse-content">
        <span class="section-block-title">综合信息【综合为对外平台信息】</span>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="综合1" prop="comprehensive1">
              <el-select v-model="form.comprehensive1" placeholder="请选择">
                <el-option label="请选择" value="" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="综合2" prop="comprehensive2">
              <el-select v-model="form.comprehensive2" placeholder="请选择">
                <el-option label="请选择" value="" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="综合3" prop="comprehensive3">
              <el-select v-model="form.comprehensive3" placeholder="请选择">
                <el-option label="请选择" value="" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="综合4" prop="comprehensive4">
              <el-select v-model="form.comprehensive4" placeholder="请选择">
                <el-option label="请选择" value="" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 项目介绍 -->
      <div class="collapse-content">
        <el-form-item prop="projectDescription">
          <el-input
            v-model="form.projectDescription"
            type="textarea"
            :rows="4"
            placeholder="请输入项目介绍"
          />
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import { useFormData } from '@/utils/use-form-data';
  import { Warning } from '@element-plus/icons-vue';

  /** 项目编辑表单 */
  defineOptions({ name: 'ProjectEditForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref<any>(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    // 项目基本信息
    projectName: '',
    projectAddress: '',
    projectDisplayName: '',
    buildingArea: '',
    storeName: '',
    managementPhone: '',
    area: '',
    propertyFee: '',
    heatingFee: '',
    waterFee: '',
    electricityFee: '',
    gasFee: '',
    waterCollection: false,
    feeStandard: false,
    feeManagement: false,
    contractTemplate: '',
    platformContractTemplate: '',
    renewalContractTemplate: '',
    propertyStatus: '',
    buildingManager: '',
    addressInfo: '',
    businessScope: '',

    // 配套设施
    facilities: {
      airCondition: false,
      heating: false,
      smartAccess: false,
      publicArea: false,
      network: false,
      elevator: false,
      parking: false,
      security: false,
      cleaning: false,
      meeting: false,
      reception: false,
      restArea: false,
      kitchen: false,
      printer: false,
      frontDesk: false,
      conference: false,
      publicWifi: false
    },

    // 综合信息
    comprehensive1: '',
    comprehensive2: '',
    comprehensive3: '',
    comprehensive4: '',

    // 项目介绍
    projectDescription: ''
  });
  /** 表单验证规则 */
  const rules = reactive({
    projectName: [
      {
        required: true,
        message: '项目名称不能为空',
        trigger: 'blur'
      }
    ],
    storeName: [
      {
        required: true,
        message: '门店名称不能为空',
        trigger: 'blur'
      }
    ],
    propertyFee: [
      {
        required: true,
        message: '物业费不能为空',
        trigger: 'blur'
      }
    ],
    propertyStatus: [
      {
        required: true,
        message: '房源状态不能为空',
        trigger: 'change'
      }
    ],
    buildingManager: [
      {
        required: true,
        message: '大厦负责人不能为空',
        trigger: 'change'
      }
    ],
    addressInfo: [
      {
        required: true,
        message: '地址信息不能为空',
        trigger: 'blur'
      }
    ],
    businessScope: [
      {
        required: true,
        message: '经营范围不能为空',
        trigger: 'change'
      }
    ]
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
    emit('done');
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        message.success(t('common.createSuccess'));
      } else {
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        assignFields({});
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value
        ? t('action.update') + '项目'
        : t('action.create') + '项目';
    } finally {
      loading.value = false;
    }
  };
</script>

<style scoped>
  /* 模态框标题分隔线 */
  :deep(.ele-modal .el-dialog__header) {
    border-bottom: 1px solid #e4e7ed;
    padding-bottom: 16px;
    margin-bottom: 0;
  }

  :deep(.ele-modal .el-dialog__body) {
    padding-top: 20px;
  }

  .form-collapse {
    background: transparent;
    border: none;
  }

  .form-collapse :deep(.el-collapse-item) {
    margin-bottom: 16px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e4e7ed;
  }

  .form-collapse :deep(.el-collapse-item__header) {
    background: #f8f9fa;
    border-bottom: 1px solid #e4e7ed;
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    border-radius: 8px 8px 0 0;
  }

  .form-collapse :deep(.el-collapse-item__content) {
    padding: 0;
    border: none;
  }

  .collapse-content {
    padding: 5px;
  }

  .section-block-title {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    padding: 12px 0 12px 20px;
    background: #f5f7fa;
    border-left: 4px solid #409eff;
    border-radius: 0 4px 4px 0;
    position: relative;
    transition: all 0.3s ease;
  }

  .section-block-title:hover {
    background: #ecf5ff;
    color: #409eff;
    transform: translateX(2px);
  }

  .survey-note {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #fff7e6;
    border: 1px solid #ffd591;
    border-radius: 6px;
    color: #d46b08;
    font-size: 14px;
  }

  .survey-note .el-icon {
    color: #fa8c16;
    font-size: 16px;
  }

  .facilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px 24px;
    padding: 16px 0;
  }

  .facilities-grid .el-checkbox {
    margin: 0;
  }

  .facilities-grid .el-checkbox__label {
    font-size: 14px;
    color: #333;
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .facilities-grid {
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
      gap: 12px 20px;
    }
  }

  @media (max-width: 768px) {
    .project-form-container {
      padding: 16px;
    }

    .collapse-content {
      padding: 20px;
    }

    .form-collapse :deep(.el-collapse-item) {
      margin-bottom: 12px;
    }

    .facilities-grid {
      grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
      gap: 10px 16px;
    }
  }

  @media (max-width: 480px) {
    .project-form-container {
      padding: 12px;
    }

    .collapse-content {
      padding: 16px;
    }

    .form-collapse :deep(.el-collapse-item__header) {
      padding: 12px 16px;
      font-size: 14px;
    }

    .facilities-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 8px 12px;
    }
  }
</style>
