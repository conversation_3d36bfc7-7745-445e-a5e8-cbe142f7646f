<template>
  <ele-page flex-table>
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <!-- 搜索工作栏 -->
      <el-form @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="企业名称：">
              <el-input
                clearable
                v-model.trim="queryParams.name"
                placeholder="请输入企业名称"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="门店名称">
              <el-input
                clearable
                v-model.trim="queryParams.name"
                placeholder="请输入门店名称"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="项目名称">
              <el-input
                clearable
                v-model.trim="queryParams.name"
                placeholder="请输入项目名称"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="searchExpand" :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="项目地址">
              <el-input
                clearable
                v-model.trim="queryParams.name"
                placeholder="请输入项目地址"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="searchExpand" :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="外网发布">
              <el-input
                clearable
                v-model.trim="queryParams.name"
                placeholder="请输入外网发布"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="searchExpand" :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="统计发布">
              <el-input
                clearable
                v-model.trim="queryParams.name"
                placeholder="请输入统计发布"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
              <el-link
                type="primary"
                underline="never"
                @click="toggleExpand"
                style="margin-left: 12px"
              >
                <template v-if="searchExpand">
                  <span>收起</span>
                </template>
                <template v-else>
                  <span>展开</span>
                </template>
              </el-link>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>

    <!-- 列表 -->
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <ele-pro-table
        ref="tableRef"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            v-permission="['lease:project-info:create']"
            :icon="Plus"
            @click="handleAudit(null)"
            >新增项目</el-button
          >
          <el-button
            :icon="Download"
            v-permission="['lease:project-info:export']"
            @click="exportData"
            :loading="exportLoading"
            >导出</el-button
          >
        </template>
        <template #houseSourceBusinessType="{ row }">
          <dict-data
            :code="DICT_TYPE.LEASE_HOUSE_SOURCE_BUSINESS_TYPE"
            type="tag"
            isMultiple
            :model-value="row.houseSourceBusinessType"
          />
        </template>
        <template #region="{ row }">
          <dict-data
            :code="DICT_TYPE.LEASE_REGION_INFO"
            type="text"
            :model-value="row.region"
          />
        </template>
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.LEASE_GENERAL_STATUS"
            type="text"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-button
            link
            v-permission="['lease:project-info:update']"
            type="primary"
            @click="handleAudit(row)"
            >编辑
          </el-button>
          <el-divider
            direction="vertical"
            v-permission="['lease:project-info:delete']"
          />
          <el-button
            link
            v-permission="['lease:building-info:query']"
            type="primary"
            @click="gotoBuilding(row)"
            >楼栋
          </el-button>
          <el-divider
            direction="vertical"
            v-permission="['lease:building-info:query']"
          />
          <el-button
            link
            type="danger"
            v-permission="['lease:project-info:delete']"
            @click=""
            >删除
          </el-button>
        </template>
      </ele-pro-table>
    </ele-card>
    <project-edit-form :data="current" v-model="showEdit" @done="reload" />
  </ele-page>
</template>

<script lang="ts" setup>
  import { DICT_TYPE } from '@/utils/dict';
  import { useFormData } from '@/utils/use-form-data';
  import { dateFormatter } from '@/utils/formatTime';
  import * as ProjectApi from '@/api/lease/project/index';
  import ProjectEditForm from './ProjectEditForm.vue';
  import { Search, Refresh, Plus, Download } from '@element-plus/icons-vue';
  import { encryptParam } from '@/utils/encrypt';
  import download from '@/utils/download';

  defineOptions({ name: 'BpmTodoTask' });

  const message = useMessage(); // 消息弹窗
  const router = useRouter(); // 路由
  const [queryParams, resetFields] = useFormData({
    name: '',
    category: undefined,
    processDefinitionKey: '',
    createTime: []
  });
  const exportLoading = ref(false); // 导出的加载中

  /** 是否编辑页面 */
  const showEdit = ref(false);
  /** 当前编辑数据 */
  const current = ref<any>(null);
  /** 表格实例 */
  const tableRef = ref<any>(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'name',
        label: '项目名称',
        align: 'left',
        minWidth: 280
      },
      {
        prop: 'storeName',
        label: '门店名称',
        align: 'left',
        minWidth: 140
      },
      {
        prop: 'region',
        label: '区域',
        align: 'left',
        minWidth: 110,
        slot: 'region'
      },
      {
        prop: 'projectAddress',
        label: '项目地址',
        align: 'left',
        minWidth: 360
      },
      {
        prop: 'projectAlias',
        label: '项目别名',
        align: 'center',
        minWidth: 110
      },

      {
        prop: 'houseSourceBusinessType',
        label: '房源业态',
        align: 'left',
        minWidth: 190,
        slot: 'houseSourceBusinessType'
      },
      // {
      //   prop: 'status',
      //   label: '状态',
      //   align: 'center',
      //   minWidth: 90,
      //   slot: 'status'
      // },
      {
        prop: 'createTime',
        label: '创建时间',
        align: 'center',
        minWidth: 170,
        formatter: dateFormatter
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 180,
        align: 'center',
        fixed: 'right',
        slot: 'action',
        hideInPrint: true,
        hideInExport: true
      }
    ];
  });
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return ProjectApi.getProjectPage({
      ...where,
      ...filters,
      ...pages
    });
  };

  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 项目遍及按钮 */
  const handleAudit = (row: any) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 初始化 **/
  onMounted(async () => {});
  /** 搜索表单是否展开 */
  const searchExpand = ref(true);
  /** 搜索展开/收起 */
  const toggleExpand = () => {
    searchExpand.value = !searchExpand.value;
  };
  const gotoBuilding = (row: any) => {
    router.push({
      name: 'BuildingIndex',
      query: {
        projectId: encryptParam(row.id)
      }
    });
  };
  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data = await ProjectApi.exportProject(queryParams);
      download.excel(data, '项目信息.xls');
    } catch {
    } finally {
      exportLoading.value = false;
    }
  };
</script>
