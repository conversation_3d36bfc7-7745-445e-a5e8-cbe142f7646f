<template>
  <ele-page flex-table>
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <!-- 搜索工作栏 -->
      <el-form @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="企业名称：">
              <el-input
                clearable
                v-model.trim="queryParams.name"
                placeholder="请输入企业名称"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="门店名称">
              <el-input
                clearable
                v-model.trim="queryParams.name"
                placeholder="请输入门店名称"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="项目名称">
              <el-input
                clearable
                v-model.trim="queryParams.name"
                placeholder="请输入项目名称"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="searchExpand" :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="项目地址">
              <el-input
                clearable
                v-model.trim="queryParams.name"
                placeholder="请输入项目地址"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="searchExpand" :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="外网发布">
              <el-input
                clearable
                v-model.trim="queryParams.name"
                placeholder="请输入外网发布"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="searchExpand" :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="统计发布">
              <el-input
                clearable
                v-model.trim="queryParams.name"
                placeholder="请输入统计发布"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
              <el-link
                type="primary"
                underline="never"
                @click="toggleExpand"
                style="margin-left: 12px"
              >
                <template v-if="searchExpand">
                  <span>收起</span>
                </template>
                <template v-else>
                  <span>展开</span>
                </template>
              </el-link>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>

    <!-- 列表 -->
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <ele-pro-table
        ref="tableRef"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            v-permission="['lease:base:project:edit']"
            :icon="Plus"
            @click="handleAudit(null)"
            >新增项目</el-button
          >
          <el-button :icon="Download" @click="">导出</el-button>
        </template>
        <template #action="{ row }">
          <el-button
            link
            v-permission="['lease:base:project:edit']"
            type="primary"
            @click="handleAudit(row)"
            >编辑
          </el-button>
          <el-divider
            direction="vertical"
            v-permission="['lease:base:project:delete']"
          />
          <el-button
            link
            type="danger"
            v-permission="['lease:base:project:delete']"
            @click=""
            >删除
          </el-button>
        </template>
      </ele-pro-table>
    </ele-card>
    <project-edit :data="current" v-model="showEdit" @done="reload" />
  </ele-page>
</template>

<script lang="ts" setup>
  import { useFormData } from '@/utils/use-form-data';
  import { dateFormatter } from '@/utils/formatTime';
  import * as TaskApi from '@/api/bpm/task';
  import { CategoryApi, CategoryVO } from '@/api/bpm/category';
  import * as DefinitionApi from '@/api/bpm/definition';
  import ProjectEdit from './edit/project-edit.vue';
  import { Search, Refresh, Plus, Download } from '@element-plus/icons-vue';

  defineOptions({ name: 'BpmTodoTask' });

  const processDefinitionList = ref<any[]>([]); // 流程定义列表
  const [queryParams, resetFields] = useFormData({
    name: '',
    category: undefined,
    processDefinitionKey: '',
    createTime: []
  });
  const categoryList = ref<CategoryVO[]>([]); // 流程分类列表
  /** 是否编辑页面 */
  const showEdit = ref(false);
  /** 当前编辑数据 */
  const current = ref<any>(null);
  /** 表格实例 */
  const tableRef = ref<any>(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'name1',
        label: '企业名称',
        align: 'left',
        minWidth: 200
      },
      {
        prop: 'name2',
        label: '门店名称',
        align: 'left',
        minWidth: 180
      },
      {
        prop: 'name1',
        label: '项目名称',
        align: 'left',
        minWidth: 200
      },
      {
        prop: 'name2',
        label: '区域',
        align: 'center',
        minWidth: 180
      },
      {
        prop: 'name1',
        label: '商圈',
        align: 'center',
        minWidth: 200
      },
      {
        prop: 'name2',
        label: '项目地址',
        align: 'left',
        minWidth: 220
      },
      {
        prop: 'name2',
        label: '统计发布',
        align: 'left',
        minWidth: 220
      },
      {
        prop: 'name2',
        label: '外网发布',
        align: 'left',
        minWidth: 220
      },
      {
        prop: 'createTime',
        label: '发布时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        prop: 'createTime',
        label: '创建时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 170,
        fixed: 'right',
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return TaskApi.getTaskTodoPage({
      ...where,
      ...filters,
      ...pages
    });
  };

  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 项目遍及按钮 */
  const handleAudit = (row: any) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 初始化 **/
  onMounted(async () => {
    categoryList.value = await CategoryApi.getCategorySimpleList();
    // 获取流程定义列表
    processDefinitionList.value =
      await DefinitionApi.getSimpleProcessDefinitionList();
  });
  /** 搜索表单是否展开 */
  const searchExpand = ref(true);
  /** 搜索展开/收起 */
  const toggleExpand = () => {
    searchExpand.value = !searchExpand.value;
  };
</script>
