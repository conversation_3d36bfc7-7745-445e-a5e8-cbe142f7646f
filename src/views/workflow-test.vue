<template>
  <div class="workflow-test-page">
    <DraggableWorkflowDesigner @success="handleWorkflowSave" />
  </div>
</template>

<script setup lang="ts">
import { DraggableWorkflowDesigner } from '@/components/DraggableWorkflowDesigner/src';

defineOptions({
  name: 'WorkflowTest'
});

const handleWorkflowSave = (data: any) => {
  console.log('工作流数据:', data);
  // 这里可以保存到后端
};
</script>

<style lang="scss" scoped>
.workflow-test-page {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}
</style>
