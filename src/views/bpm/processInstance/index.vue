<template>
  <ele-page flex-table>
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form @keyup.enter="reload" @submit.prevent="">
        <!-- 搜索工作栏 -->
        <el-row :gutter="8">
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="流程名称">
              <el-input
                clearable
                v-model.trim="queryParams.name"
                placeholder="请输入流程名称"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="流程分类">
              <el-select
                v-model="queryParams.category"
                placeholder="请选择流程分类"
                clearable
              >
                <el-option
                  v-for="category in categoryList"
                  :key="category.code"
                  :label="category.name"
                  :value="category.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="searchExpand" :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="流程状态">
              <!-- <dict-data
              :code="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS"
              v-model="queryParams.status"
              placeholder="请选择流程状态"
            /> -->
              <el-select
                v-model="queryParams.status"
                placeholder="请选择流程状态"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="searchExpand" :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="所属流程">
              <el-select
                v-model="queryParams.processDefinitionKey"
                placeholder="请选择所属流程"
                clearable
              >
                <el-option
                  v-for="item in processDefinitionList"
                  :key="item.key"
                  :label="item.name"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="searchExpand" :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="发起时间">
              <el-date-picker
                v-model="queryParams.createTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-240px"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button type="primary" :icon="Search" @click="reload"
                >查询</el-button
              >
              <el-button @click="reset" :icon="Refresh">重置</el-button>
              <el-link
                type="primary"
                underline="never"
                @click="toggleExpand"
                style="margin-left: 12px"
              >
                <template v-if="searchExpand">
                  <span>收起</span>
                  <el-icon style="vertical-align: -1px">
                    <ArrowUp />
                  </el-icon>
                </template>
                <template v-else>
                  <span>展开</span>
                  <el-icon style="vertical-align: -2px">
                    <ArrowDown />
                  </el-icon>
                </template>
              </el-link>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <!-- 列表 -->
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <ele-pro-table
        ref="tableRef"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #summary="{ row }">
          <div
            class="flex flex-col"
            v-if="row.summary && row.summary.length > 0"
          >
            <div v-for="(item, index) in row.summary" :key="index">
              <el-text type="info"> {{ item.key }} : {{ item.value }} </el-text>
            </div>
          </div>
        </template>
        <template #status="{ row }">
          <!-- 审批中状态 -->
          <template
            v-if="
              row.status === BpmProcessInstanceStatus.RUNNING &&
              row.tasks?.length > 0
            "
          >
            <!-- 单人审批 -->
            <template v-if="row.tasks.length === 1">
              <span>
                <el-button link type="primary" @click="handleDetail(row)">
                  {{ row.tasks[0].assigneeUser?.nickname }}
                </el-button>
                ({{ row.tasks[0].name }}) 审批中
              </span>
            </template>
            <!-- 多人审批 -->
            <template v-else>
              <span>
                <el-button link type="primary" @click="handleDetail(row)">
                  {{ row.tasks[0].assigneeUser?.nickname }}
                </el-button>
                等 {{ row.tasks.length }} 人 ({{ row.tasks[0].name }})审批中
              </span>
            </template>
          </template>
          <!-- 非审批中状态 -->
          <template v-else>
            <dict-data
              :code="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS"
              type="tag"
              :model-value="row.status"
            />
          </template>
        </template>
        <template #durationInMillis="{ row }">
          {{
            row.durationInMillis > 0 ? formatPast2(row.durationInMillis) : '-'
          }}
        </template>
        <template #currentTask="{ row }">
          <el-button
            type="primary"
            v-for="task in row.tasks"
            :key="task.id"
            link
          >
            <span>{{ task.name }}</span>
          </el-button>
        </template>
        <template #action="{ row }">
          <el-button
            link
            type="primary"
            v-permission="['bpm:process-instance:query']"
            @click="handleDetail(row)"
          >
            详情
          </el-button>
          <el-divider
            direction="vertical"
            v-permission="['bpm:process-instance:cancel']"
          />
          <el-button
            link
            type="danger"
            v-if="row.status === 1"
            v-permission="['bpm:process-instance:cancel']"
            @click="handleCancel(row)"
          >
            取消
          </el-button>
          <el-button link type="primary" v-else @click="handleCreate(row)">
            重新发起
          </el-button>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>
<script lang="ts" setup>
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { dateFormatter, formatPast2 } from '@/utils/formatTime';
  import { ElMessageBox } from 'element-plus';
  import * as ProcessInstanceApi from '@/api/bpm/processInstance';
  import { CategoryApi, CategoryVO } from '@/api/bpm/category';
  import { ProcessInstanceVO } from '@/api/bpm/processInstance';
  import * as DefinitionApi from '@/api/bpm/definition';
  import { BpmProcessInstanceStatus } from '@/utils/constants';
  import { Search, Refresh } from '@element-plus/icons-vue';

  defineOptions({ name: 'BpmProcessInstanceMy' });
  const router = useRouter(); // 路由
  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化

  const loading = ref(true); // 列表的加载中
  const processDefinitionList = ref<any[]>([]); // 流程定义列表
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    name: '',
    processDefinitionKey: undefined,
    category: undefined,
    status: undefined,
    createTime: []
  });
  const categoryList = ref<CategoryVO[]>([]); // 流程分类列表

  /** 表格实例 */
  const tableRef = ref<any>(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        prop: 'name',
        label: '流程名称',
        align: 'center',
        minWidth: 200
      },
      {
        prop: 'summary',
        label: '摘要',
        align: 'left',
        minWidth: 180,
        slot: 'summary'
      },
      {
        prop: 'categoryName',
        label: '流程分类',
        align: 'center',
        minWidth: 100
      },
      {
        prop: 'status',
        label: '流程状态',
        align: 'left',
        minWidth: 190,
        slot: 'status'
      },
      {
        prop: 'startTime',
        label: '发起时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        prop: 'endTime',
        label: '结束时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 170,
        fixed: 'right',
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return ProcessInstanceApi.getProcessInstanceMyPage({
      ...where,
      ...filters,
      ...pages
    });
  };

  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };

  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };

  /** 发起流程操作 **/
  const handleCreate = async (row?: ProcessInstanceVO) => {
    // 如果是【业务表单】，不支持重新发起
    if (row?.id) {
      const processDefinitionDetail = await DefinitionApi.getProcessDefinition(
        row.processDefinitionId
      );
      if (processDefinitionDetail.formType === 20) {
        message.error(
          '重新发起流程失败，原因：该流程使用业务表单，不支持重新发起'
        );
        return;
      }
    }
    // 跳转发起流程界面
    await router.push({
      name: 'BpmProcessInstanceCreate',
      query: { processInstanceId: row?.id }
    });
  };

  /** 查看详情 */
  const handleDetail = (row: ProcessInstanceVO) => {
    router.push({
      name: 'BpmProcessInstanceDetail',
      query: {
        id: row.id
      }
    });
  };

  /** 取消按钮操作 */
  const handleCancel = async (row: ProcessInstanceVO) => {
    // 二次确认
    const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
      confirmButtonText: t('common.ok'),
      cancelButtonText: t('common.cancel'),
      inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
      inputErrorMessage: '取消原因不能为空'
    });
    // 发起取消
    await ProcessInstanceApi.cancelProcessInstanceByStartUser(row.id, value);
    message.success('取消成功');
    // 刷新列表
    reload();
  };

  /** 激活时 **/
  onActivated(() => {
    reload();
  });

  /** 初始化 **/
  onMounted(async () => {
    categoryList.value = await CategoryApi.getCategorySimpleList();
    // 获取流程定义列表
    processDefinitionList.value =
      await DefinitionApi.getSimpleProcessDefinitionList();
  });
  /** 搜索表单是否展开 */
  const searchExpand = ref(true);
  /** 搜索展开/收起 */
  const toggleExpand = () => {
    searchExpand.value = !searchExpand.value;
  };
</script>
