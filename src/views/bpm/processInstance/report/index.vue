<template>
  <ele-page flex-table>
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <!-- 搜索工作栏 -->
      <el-form @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="24">
          <el-col :lg="7" :md="12" :sm="12" :xs="24">
            <el-form-item label="发起人">
              <el-select
                v-model="queryParams.startUserId"
                placeholder="请选择发起人"
              >
                <el-option
                  v-for="user in userList"
                  :key="user.id"
                  :label="user.nickname"
                  :value="user.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="7" :md="12" :sm="12" :xs="24">
            <el-form-item label="流程名称">
              <el-input
                v-model="queryParams.name"
                placeholder="请输入流程名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col v-if="searchExpand" :lg="7" :md="12" :sm="12" :xs="24">
            <el-form-item label="流程状态">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择流程状态"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="searchExpand" :lg="7" :md="12" :sm="12" :xs="24">
            <el-form-item label="发起时间">
              <el-date-picker
                v-model="queryParams.createTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="searchExpand" :lg="7" :md="12" :sm="12" :xs="24">
            <el-form-item label="结束时间">
              <el-date-picker
                v-model="queryParams.endTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="7" :md="12" :sm="12" :xs="24">
            <el-form-item>
              <el-button @click="reload" type="primary"
                ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
              >
              <el-button @click="reset"
                ><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button
              >
              <el-link
                type="primary"
                underline="never"
                @click="toggleExpand"
                style="margin-left: 12px"
              >
                <template v-if="searchExpand">
                  <span>收起</span>
                  <el-icon style="vertical-align: -1px">
                    <ArrowUp />
                  </el-icon>
                </template>
                <template v-else>
                  <span>展开</span>
                  <el-icon style="vertical-align: -2px">
                    <ArrowDown />
                  </el-icon>
                </template>
              </el-link>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>

    <!-- 列表 -->
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <ele-pro-table
        ref="tableRef"
        :columns="columns"
        :datasource="dataSource"
        :show-search="false"
        :show-pagination="true"
      >
        <!-- 流程状态列 -->
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS"
            :model-value="row.status"
          />
        </template>

        <!-- 动态表单字段列 -->
        <template
          v-for="(item, index) in formFields"
          :key="`form-field-${index}`"
          #[`formField_${item.field}`]="{ row }"
        >
          {{ row.formVariables[item.field] ?? '' }}
        </template>

        <!-- 操作列 -->
        <template #action="{ row }">
          <el-button
            link
            type="primary"
            v-permission="['bpm:process-instance:query']"
            @click="handleDetail(row)"
          >
            详情
          </el-button>
          <el-divider
            direction="vertical"
            v-if="row.status === 1"
            v-permission="'bpm:process-instance:cancel'"
          />
          <el-button
            link
            type="danger"
            v-if="row.status === 1"
            v-permission="['bpm:process-instance:cancel']"
            @click="handleCancel(row)"
          >
            取消
          </el-button>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>
<script lang="ts" setup>
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { dateFormatter } from '@/utils/formatTime';
  import * as ProcessInstanceApi from '@/api/bpm/processInstance';
  import * as UserApi from '@/api/system/user';
  import * as DefinitionApi from '@/api/bpm/definition';
  import { parseFormFields } from '@/components/FormCreate/src/utils';
  import { ElMessageBox } from 'element-plus';

  defineOptions({ name: 'BpmProcessInstanceReport' });

  const router = useRouter(); // 路由
  const { query } = useRoute();
  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化

  // 表格引用
  const tableRef = ref();
  const formFields = ref<any[]>([]);
  const processDefinitionId = query.processDefinitionId as string;
  const [queryParams, resetFields] = useFormData({
    startUserId: undefined,
    name: '',
    processDefinitionKey: query.processDefinitionKey,
    status: undefined,
    createTime: [],
    endTime: [],
    formFieldsParams: {}
  });
  const userList = ref<any[]>([]); // 用户列表

  // 保留原有的搜索表单，不使用 pro-table 的搜索配置

  // 不使用 pro-table 的搜索功能，保留原有的搜索表单

  // 基础表格列配置
  const baseColumns = [
    {
      prop: 'name',
      label: '流程名称',
      align: 'center',
      fixed: 'left',
      width: 200
    },
    {
      prop: 'startUser.nickname',
      label: '流程发起人',
      align: 'center',
      width: 140
    },
    {
      prop: 'status',
      label: '流程状态',
      width: 140,
      align: 'center',
      slot: 'status'
    },
    {
      prop: 'startTime',
      label: '发起时间',
      align: 'center',
      width: 220,
      formatter: dateFormatter
    },
    {
      prop: 'endTime',
      label: '结束时间',
      align: 'center',
      width: 220,
      formatter: dateFormatter
    }
  ];

  // 动态表格列配置
  const columns = computed(() => {
    const dynamicColumns = formFields.value.map((item) => ({
      prop: `formVariables.${item.field}`,
      label: item.title,
      width: 130,
      slot: `formField_${item.field}`
    }));

    const actionColumn = {
      prop: 'action',
      label: '操作',
      align: 'center',
      fixed: 'right',
      slot: 'action'
    };

    return [...baseColumns, ...dynamicColumns, actionColumn];
  });

  /** 查询列表 - 用于 ele-pro-table */
  const dataSource = async ({ pages, where, orders }) => {
    return ProcessInstanceApi.getProcessInstanceManagerPage({
      ...where,
      ...orders,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };

  /** 获取流程定义 */
  const getProcessDefinition = async () => {
    const processDefinition =
      await DefinitionApi.getProcessDefinition(processDefinitionId);
    formFields.value = parseFormCreateFields(processDefinition.formFields);
  };

  /** 解析表单字段 */
  const parseFormCreateFields = (formFields?: string[]) => {
    const result: Array<Record<string, any>> = [];
    if (formFields) {
      formFields.forEach((fieldStr: string) => {
        parseFormFields(JSON.parse(fieldStr), result);
      });
    }
    return result;
  };

  /** 查看详情 */
  const handleDetail = (row: any) => {
    router.push({
      name: 'BpmProcessInstanceDetail',
      query: {
        id: row.id
      }
    });
  };

  /** 取消按钮操作 */
  const handleCancel = async (row: any) => {
    // 二次确认
    const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
      confirmButtonText: t('common.ok'),
      cancelButtonText: t('common.cancel'),
      inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
      inputErrorMessage: '取消原因不能为空'
    });
    // 发起取消
    await ProcessInstanceApi.cancelProcessInstanceByAdmin(row.id, value);
    message.success('取消成功');
    // 刷新列表
    tableRef.value?.refresh();
  };
  /** 搜索表单是否展开 */
  const searchExpand = ref(true);
  /** 搜索展开/收起 */
  const toggleExpand = () => {
    searchExpand.value = !searchExpand.value;
  };
  /** 初始化 **/
  onMounted(async () => {
    // 获取流程定义，用于 table column 的展示
    await getProcessDefinition();
    // 获取用户列表
    userList.value = await UserApi.getSimpleUserList();
  });
</script>
