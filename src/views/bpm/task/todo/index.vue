<template>
  <ele-page flex-table>
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <!-- 搜索工作栏 -->
      <el-form @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="任务名称">
              <el-input
                clearable
                v-model.trim="queryParams.name"
                placeholder="请输入任务名称"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="发起时间">
              <el-date-picker
                v-model="queryParams.createTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="searchExpand" :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="流程分类">
              <el-select
                v-model="queryParams.category"
                placeholder="请选择流程分类"
                clearable
              >
                <el-option
                  v-for="category in categoryList"
                  :key="category.code"
                  :label="category.name"
                  :value="category.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="searchExpand" :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="所属流程">
              <el-select
                v-model="queryParams.processDefinitionKey"
                placeholder="请选择流程定义"
                clearable
              >
                <el-option
                  v-for="item in processDefinitionList"
                  :key="item.key"
                  :label="item.name"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
              <el-link
                type="primary"
                underline="never"
                @click="toggleExpand"
                style="margin-left: 12px"
              >
                <template v-if="searchExpand">
                  <span>收起</span>
                  <el-icon style="vertical-align: -1px">
                    <ArrowUp />
                  </el-icon>
                </template>
                <template v-else>
                  <span>展开</span>
                  <el-icon style="vertical-align: -2px">
                    <ArrowDown />
                  </el-icon>
                </template>
              </el-link>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>

    <!-- 列表 -->
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <ele-pro-table
        ref="tableRef"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #summary="{ row }">
          <div
            class="flex flex-col"
            v-if="
              row.processInstance.summary &&
              row.processInstance.summary.length > 0
            "
          >
            <div
              v-for="(item, index) in row.processInstance.summary"
              :key="index"
            >
              <el-text type="info"> {{ item.key }} : {{ item.value }} </el-text>
            </div>
          </div>
        </template>
        <template #action="{ row }">
          <el-button link type="primary" @click="handleAudit(row)"
            >办理</el-button
          >
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script lang="ts" setup>
  import { useFormData } from '@/utils/use-form-data';
  import { dateFormatter } from '@/utils/formatTime';
  import * as TaskApi from '@/api/bpm/task';
  import { CategoryApi, CategoryVO } from '@/api/bpm/category';
  import * as DefinitionApi from '@/api/bpm/definition';
  import { Search, Refresh } from '@element-plus/icons-vue';

  defineOptions({ name: 'BpmTodoTask' });

  const { push } = useRouter(); // 路由
  const processDefinitionList = ref<any[]>([]); // 流程定义列表
  const [queryParams, resetFields] = useFormData({
    name: '',
    category: undefined,
    processDefinitionKey: '',
    createTime: []
  });
  const categoryList = ref<CategoryVO[]>([]); // 流程分类列表

  /** 表格实例 */
  const tableRef = ref<any>(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        prop: 'processInstance.name',
        label: '流程名称',
        align: 'center',
        minWidth: 200
      },
      {
        prop: 'summary',
        label: '摘要',
        align: 'left',
        minWidth: 180,
        slot: 'summary'
      },
      {
        prop: 'processInstance.startUser.nickname',
        label: '发起人',
        align: 'center',
        minWidth: 100
      },
      {
        prop: 'createTime',
        label: '发起时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        prop: 'name',
        label: '当前任务',
        align: 'center',
        minWidth: 180
      },
      {
        prop: 'createTime',
        label: '任务时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        prop: 'processInstanceId',
        label: '流程编号',
        align: 'center',
        minWidth: 320
      },
      {
        prop: 'id',
        label: '任务编号',
        align: 'center',
        minWidth: 320
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 170,
        fixed: 'right',
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return TaskApi.getTaskTodoPage({
      ...where,
      ...filters,
      ...pages
    });
  };

  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 处理审批按钮 */
  const handleAudit = (row: any) => {
    push({
      name: 'BpmProcessInstanceDetail',
      query: {
        id: row.processInstance.id,
        taskId: row.id
      }
    });
  };

  /** 初始化 **/
  onMounted(async () => {
    categoryList.value = await CategoryApi.getCategorySimpleList();
    // 获取流程定义列表
    processDefinitionList.value =
      await DefinitionApi.getSimpleProcessDefinitionList();
  });
  /** 搜索表单是否展开 */
  const searchExpand = ref(true);
  /** 搜索展开/收起 */
  const toggleExpand = () => {
    searchExpand.value = !searchExpand.value;
  };
</script>
