<template>
  <ele-page flex-table>
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="任务名称">
              <el-input
                clearable
                v-model.trim="queryParams.name"
                placeholder="请输入任务名称"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="发起时间">
              <el-date-picker
                v-model="queryParams.createTime"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                end-placeholder="结束日期"
                start-placeholder="开始日期"
                type="daterange"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="searchExpand" :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="流程状态">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择流程状态"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="searchExpand" :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="流程分类">
              <el-select
                v-model="queryParams.category"
                placeholder="请选择流程分类"
                clearable
              >
                <el-option
                  v-for="category in categoryList"
                  :key="category.code"
                  :label="category.name"
                  :value="category.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="searchExpand" :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="所属流程">
              <el-select
                v-model="queryParams.processDefinitionKey"
                placeholder="请选择流程定义"
                clearable
              >
                <el-option
                  v-for="item in processDefinitionList"
                  :key="item.key"
                  :label="item.name"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
              <el-link
                type="primary"
                underline="never"
                @click="toggleExpand"
                style="margin-left: 12px"
              >
                <template v-if="searchExpand">
                  <span>收起</span>
                  <el-icon style="vertical-align: -1px">
                    <ArrowUp />
                  </el-icon>
                </template>
                <template v-else>
                  <span>展开</span>
                  <el-icon style="vertical-align: -2px">
                    <ArrowDown />
                  </el-icon>
                </template>
              </el-link>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>

    <!-- 列表 -->
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        cache-key="ProcessDoneTable"
      >
        <template #summary="{ row }">
          <div
            class="flex flex-col"
            v-if="
              row.processInstance.summary &&
              row.processInstance.summary.length > 0
            "
          >
            <div
              v-for="(item, index) in row.processInstance.summary"
              :key="index"
            >
              <el-text type="info"> {{ item.key }} : {{ item.value }} </el-text>
            </div>
          </div>
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.BPM_TASK_STATUS"
            :model-value="row.status"
          />
        </template>
        <template #durationInMillis="{ row }">
          {{ formatPast2(row.durationInMillis) }}
        </template>
        <template #action="{ row }">
          <el-button link type="primary" @click="handleAudit(row)">
            历史
          </el-button>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>
<script lang="ts" setup>
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { dateFormatter, formatPast2 } from '@/utils/formatTime';
  import * as TaskApi from '@/api/bpm/task';
  import { CategoryApi, CategoryVO } from '@/api/bpm/category';
  import * as DefinitionApi from '@/api/bpm/definition';
  import { Search, Refresh } from '@element-plus/icons-vue';

  defineOptions({ name: 'BpmDoneTask' });

  const categoryList = ref<CategoryVO[]>([]); // 流程分类列表
  const { push } = useRouter(); // 路由
  const processDefinitionList = ref<any[]>([]); // 流程定义列表
  const [queryParams, resetFields] = useFormData({
    name: '',
    category: undefined,
    status: undefined,
    processDefinitionKey: '',
    createTime: []
  });

  /** 表格实例 */
  const tableRef = ref<any>(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        prop: 'processInstance.name',
        label: '流程',
        align: 'center',
        minWidth: 200
      },
      {
        prop: 'summary',
        label: '摘要',
        align: 'left',
        minWidth: 180,
        slot: 'summary'
      },
      {
        prop: 'processInstance.startUser.nickname',
        label: '发起人',
        align: 'center',
        minWidth: 100
      },
      {
        prop: 'createTime',
        label: '发起时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        prop: 'name',
        label: '当前任务',
        align: 'center',
        minWidth: 180
      },
      {
        prop: 'createTime',
        label: '任务开始时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        prop: 'endTime',
        label: '任务结束时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        prop: 'status',
        label: '审批状态',
        align: 'center',
        minWidth: 180,
        slot: 'status'
      },
      {
        prop: 'reason',
        label: '审批建议',
        align: 'center',
        minWidth: 180
      },
      {
        prop: 'durationInMillis',
        label: '耗时',
        align: 'center',
        minWidth: 180,
        slot: 'durationInMillis'
      },
      {
        prop: 'processInstanceId',
        label: '流程编号',
        align: 'center',
        minWidth: 320
      },
      {
        prop: 'id',
        label: '任务编号',
        align: 'center',
        minWidth: 320
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 170,
        fixed: 'right',
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return TaskApi.getTaskDonePage({
      ...where,
      ...filters,
      ...pages
    });
  };

  /** 处理审批按钮 */
  const handleAudit = (row: any) => {
    push({
      name: 'BpmProcessInstanceDoneDetail',
      query: {
        id: row.processInstance.id,
        taskId: row.id
      }
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 搜索表单是否展开 */
  const searchExpand = ref(true);
  /** 搜索展开/收起 */
  const toggleExpand = () => {
    searchExpand.value = !searchExpand.value;
  };
  /** 初始化 **/
  onMounted(async () => {
    categoryList.value = await CategoryApi.getCategorySimpleList();
    // 获取流程定义列表
    processDefinitionList.value =
      await DefinitionApi.getSimpleProcessDefinitionList();
  });
</script>
