<!-- 工作流 - 抄送我的流程 -->
<template>
  <ele-page flex-table>
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <!-- 搜索工作栏 -->
      <el-form @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="流程名称">
              <el-input
                clearable
                v-model.trim="queryParams.processInstanceName"
                placeholder="请输入流程名称"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="抄送时间">
              <el-date-picker
                v-model="queryParams.createTime"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                end-placeholder="结束日期"
                start-placeholder="开始日期"
                type="daterange"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>

    <!-- 列表 -->
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        cache-key="ProcessInstanceTable"
      >
        <template #summary="{ row }">
          <div
            class="flex flex-col"
            v-if="
              row.processInstance.summary &&
              row.processInstance.summary.length > 0
            "
          >
            <div
              v-for="(item, index) in row.processInstance.summary"
              :key="index"
            >
              <el-text type="info"> {{ item.key }} : {{ item.value }} </el-text>
            </div>
          </div>
        </template>
        <template #creatorName="{ row }">
          {{ row.createUser?.nickname || '系统' }}
        </template>
        <template #action="{ row }">
          <el-button link type="primary" @click="handleAudit(row)">
            详情
          </el-button>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>
<script lang="ts" setup>
  import { useFormData } from '@/utils/use-form-data';
  import { dateFormatter } from '@/utils/formatTime';
  import * as ProcessInstanceApi from '@/api/bpm/processInstance';
  import { Search, Refresh } from '@element-plus/icons-vue';

  defineOptions({ name: 'BpmProcessInstanceCopy' });

  const { push } = useRouter(); // 路由

  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    processInstanceId: '',
    processInstanceName: '',
    createTime: []
  });
  /** 表格实例 */
  const tableRef = ref<any>(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        prop: 'processInstanceName',
        label: '流程名',
        align: 'center',
        minWidth: 200
      },
      {
        prop: 'summary',
        label: '摘要',
        align: 'left',
        minWidth: 180,
        slot: 'summary'
      },
      {
        prop: 'startUser.nickname',
        label: '流程发起人',
        align: 'center',
        minWidth: 100
      },
      {
        prop: 'processInstanceStartTime',
        label: '流程发起时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        prop: 'activityName',
        label: '抄送节点',
        align: 'center',
        minWidth: 180
      },
      {
        prop: 'creatorName',
        label: '抄送人',
        align: 'center',
        minWidth: 180,
        slot: 'creatorName'
      },
      {
        prop: 'reason',
        label: '抄送意见',
        align: 'center',
        minWidth: 180
      },
      {
        prop: 'createTime',
        label: '抄送时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 170,
        fixed: 'right',
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return ProcessInstanceApi.getProcessInstanceCopyPage({
      ...where,
      ...filters,
      ...pages
    });
  };

  /** 处理审批按钮 */
  const handleAudit = (row: any) => {
    const query = {
      id: row.processInstanceId,
      activityId: undefined
    };
    if (row.activityId) {
      query.activityId = row.activityId;
    }
    push({
      name: 'BpmProcessInstanceCopyDetail',
      query: query
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
</script>
