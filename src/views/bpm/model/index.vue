<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <div class="bpm-model-page">
        <div class="page-header">
          <div class="title-area">
            <div class="title-row">
              <h2 class="page-title">流程模型</h2>
              <el-tag
                v-if="isCategorySorting"
                type="warning"
                effect="plain"
                size="small"
                >分类排序中</el-tag
              >
            </div>
            <p class="page-subtitle">分类管理流程模型</p>
          </div>

          <!-- 工具栏：搜索与操作 -->
          <div class="toolbar" v-if="!isCategorySorting">
            <el-form
              :model="queryParams"
              :inline="true"
              @submit.prevent
              class="toolbar-form"
            >
              <el-form-item prop="name" class="toolbar-item">
                <el-input
                  v-model="queryParams.name"
                  placeholder="搜索流程名称"
                  clearable
                  @keyup.enter="handleQuery"
                  class="toolbar-search"
                >
                  <template #prefix>
                    <el-icon class="mx-10px"><Search /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item class="toolbar-item">
                <el-button
                  type="primary"
                  @click="openForm('create')"
                  v-permission="['bpm:model:create']"
                >
                  <el-icon class="mx-5px"><Plus /></el-icon>新建模型
                </el-button>
              </el-form-item>
              <el-form-item class="toolbar-item">
                <el-dropdown
                  @command="(command) => handleCommand(command)"
                  placement="bottom-end"
                >
                  <el-button class="w-32px" plain>
                    <el-icon class="mx-5px"><Setting /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="handleCategoryAdd">
                        <el-icon class="mx-5px"><CirclePlus /></el-icon>
                        新建分类
                      </el-dropdown-item>
                      <el-dropdown-item command="handleCategorySort">
                        <el-icon class="mx-5px"><Sort /></el-icon>
                        分类排序
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </el-form-item>
            </el-form>
          </div>

          <!-- 排序状态操作 -->
          <div class="toolbar" v-else>
            <div class="toolbar-sort">
              <el-button @click="handleCategorySortCancel">取 消</el-button>
              <el-button type="primary" @click="handleCategorySortSubmit"
                >保存排序</el-button
              >
            </div>
          </div>
        </div>
        <el-divider />
        <!-- 按照分类，展示其所属的模型列表 -->
        <div class="category-grid">
          <draggable
            :disabled="!isCategorySorting"
            v-model="categoryGroup"
            item-key="id"
            :animation="400"
          >
            <template #item="{ element }">
              <el-card
                class="rounded-lg transition-all duration-300 ease-in-out hover:shadow-xl"
                v-loading="loading"
                :body-style="{ padding: 0 }"
                :key="element.id"
              >
                <CategoryDraggableModel
                  :isCategorySorting="isCategorySorting"
                  :categoryInfo="element"
                  @success="getList"
                />
              </el-card>
            </template>
          </draggable>
        </div>
      </div>
    </ele-card>

    <!-- 表单弹窗：添加分类 -->
    <CategoryForm ref="categoryFormRef" @success="getList" />

    <!-- 弹窗：表单详情 -->
    <el-dialog
      title="表单详情"
      v-model="formDetailVisible"
      width="800"
      class="form-detail-dialog"
    >
      <form-create
        :rule="formDetailPreview.rule"
        :option="formDetailPreview.option"
      />
    </el-dialog>
  </ele-page>
</template>

<script lang="ts" setup>
  import draggable from 'vuedraggable';
  import { CategoryApi } from '@/api/bpm/category';
  import * as ModelApi from '@/api/bpm/model';
  import CategoryForm from '../category/CategoryForm.vue';
  // @ts-ignore: type defs for lodash-es may be missing in this project, safe to ignore
  import { cloneDeep } from 'lodash-es';
  import CategoryDraggableModel from './CategoryDraggableModel.vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { onActivated, ref, reactive } from 'vue';
  import { useRouter } from 'vue-router';
  import {
    Setting,
    Search,
    Plus,
    Sort,
    CirclePlus
  } from '@element-plus/icons-vue';

  defineOptions({ name: 'BpmModel' });

  const { push } = useRouter();
  const message = useMessage(); // 消息弹窗
  const loading = ref(true); // 列表的加载中
  const isCategorySorting = ref(false); // 是否 category 正处于排序状态
  const queryParams = reactive({
    name: undefined
  });
  const categoryGroup: any = ref([]); // 按照 category 分组的数据
  const originalData: any = ref([]); // 原始数据

  /** 搜索按钮操作 */
  const handleQuery = () => {
    getList();
  };

  /** 添加/修改操作 */
  const openForm = (type: string, id?: number) => {
    if (type === 'create') {
      push({
        name: 'BpmModelCreate'
      });
    } else {
      push({
        name: 'BpmModelUpdate',
        params: { id }
      });
    }
  };

  /** 流程表单的详情按钮操作 */
  const formDetailVisible = ref(false);
  const formDetailPreview = ref({
    rule: [],
    option: {}
  });

  /** 右上角设置按钮 */
  const handleCommand = (command: string) => {
    switch (command) {
      case 'handleCategoryAdd':
        handleCategoryAdd();
        break;
      case 'handleCategorySort':
        handleCategorySort();
        break;
      default:
        break;
    }
  };

  /** 新建分类 */
  const categoryFormRef = ref();
  const handleCategoryAdd = () => {
    categoryFormRef.value.open('create');
  };

  /** 分类排序的提交 */
  const handleCategorySort = () => {
    // 保存初始数据
    originalData.value = cloneDeep(categoryGroup.value);
    isCategorySorting.value = true;
  };

  /** 分类排序的取消 */
  const handleCategorySortCancel = () => {
    // 恢复初始数据
    categoryGroup.value = cloneDeep(originalData.value);
    isCategorySorting.value = false;
  };

  /** 分类排序的保存 */
  const handleCategorySortSubmit = async () => {
    // 保存排序
    const ids = categoryGroup.value.map((item: any) => item.id);
    await CategoryApi.updateCategorySortBatch(ids);
    // 刷新列表
    isCategorySorting.value = false;
    message.success('排序分类成功');
    await getList();
  };

  /** 加载数据 */
  const getList = async () => {
    loading.value = true;
    try {
      // 查询模型 + 分裂的列表
      const modelList = await ModelApi.getModelList(queryParams.name);
      const categoryList = await CategoryApi.getCategorySimpleList();
      // 按照 category 聚合
      // 注意：必须一次性赋值给 categoryGroup，否则每次操作后，列表会重新渲染，滚动条的位置会偏离！！！
      categoryGroup.value = categoryList.map((category: any) => ({
        ...category,
        modelList: modelList.filter(
          (model: any) => model.categoryName == category.name
        )
      }));
    } finally {
      loading.value = false;
    }
  };

  /** 初始化 **/
  onActivated(() => {
    getList();
  });
</script>

<style lang="scss" scoped>
  .page-header {
    display: grid;
    grid-template-columns: 1fr auto;
    grid-gap: 16px 24px;
    align-items: center;
    padding: 8px 20px 0 20px;
  }

  .title-area {
    display: flex;
    flex-direction: column;
    min-width: 0;
  }

  .title-row {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .page-title {
    font-size: 18px;
    font-weight: 800;
    line-height: 24px;
    margin: 0;
    color: #111827;
  }

  .page-subtitle {
    margin: 4px 0 0;
    font-size: 12px;
    color: #6b7280;
  }

  .toolbar {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .toolbar-form {
    display: flex;
    align-items: center;
  }

  .toolbar-item {
    margin-bottom: 0 !important;
  }

  .toolbar-search {
    width: 240px;
  }

  .category-grid {
    padding: 0 15px 15px;
  }

  /* 分类卡片 hover 提升层次感 */
  :deep(.el-card) {
    border-radius: 10px;
    transition:
      box-shadow 0.2s ease,
      transform 0.2s ease;
  }
  :deep(.el-card:hover) {
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
  }

  /* 排序状态固定工具条（可选） */
  .toolbar-sort {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  /* 小屏适配 */
  @media (max-width: 1024px) {
    .page-header {
      grid-template-columns: 1fr;
    }
    .toolbar {
      justify-content: flex-start;
    }
    .toolbar-search {
      width: 200px;
    }
  }

  /* 保留全局微调 */
  :deep() {
    .el-table--fit .el-table__inner-wrapper::before {
      height: 0;
    }
    .el-form--inline .el-form-item {
      margin-right: 10px;
    }
    .el-divider--horizontal {
      margin-top: 6px;
    }
  }
</style>
