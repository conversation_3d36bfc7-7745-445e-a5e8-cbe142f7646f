<template>
  <ele-page flex-table>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <ele-pro-table
        ref="tableRef"
        :columns="columns"
        :datasource="dataSource"
        cache-key="bpm-definition"
      >
        <!-- 流程图标列 -->
        <template #icon="{ row }">
          <el-image
            v-if="row.icon"
            :src="row.icon"
            class="h-24px w-24px rounded"
          />
        </template>

        <!-- 可见范围列 -->
        <template #startUserIds="{ row }">
          <el-text v-if="!row.startUsers?.length"> 全部可见 </el-text>
          <el-text v-else-if="row.startUsers.length === 1">
            {{ row.startUsers[0].nickname }}
          </el-text>
          <el-text v-else>
            <el-tooltip
              class="box-item"
              effect="dark"
              placement="top"
              :content="
                row.startUsers.map((user: any) => user.nickname).join('、')
              "
            >
              {{ row.startUsers[0].nickname }}等
              {{ row.startUsers.length }} 人可见
            </el-tooltip>
          </el-text>
        </template>

        <!-- 流程类型列 -->
        <template #modelType="{ row }">
          <dict-data
            type="tag"
            :model-value="row.modelType"
            :code="DICT_TYPE.BPM_MODEL_TYPE"
          />
        </template>

        <!-- 表单信息列 -->
        <template #formType="{ row }">
          <el-button
            v-if="row.formType === BpmModelFormType.NORMAL"
            type="primary"
            link
            @click="handleFormDetail(row)"
          >
            <span>{{ row.formName }}</span>
          </el-button>
          <el-button
            v-else-if="row.formType === BpmModelFormType.CUSTOM"
            type="primary"
            link
            @click="handleFormDetail(row)"
          >
            <span>{{ row.formCustomCreatePath }}</span>
          </el-button>
          <label v-else>暂无表单</label>
        </template>

        <!-- 流程版本列 -->
        <template #version="{ row }">
          <el-tag>v{{ row.version }}</el-tag>
        </template>

        <!-- 操作列 -->
        <template #action="{ row }">
          <el-button
            link
            type="primary"
            @click="openModelForm(row.id)"
            v-permission="['bpm:model:update']"
          >
            恢复
          </el-button>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 弹窗：表单详情 -->
    <el-dialog title="表单详情" v-model="formDetailVisible" width="800">
      <form-create
        :rule="formDetailPreview.rule"
        :option="formDetailPreview.option"
      />
    </el-dialog>
  </ele-page>
</template>

<script lang="ts" setup>
  import * as DefinitionApi from '@/api/bpm/definition';
  import { setConfAndFields2 } from '@/utils/formCreate';
  import { DICT_TYPE } from '@/utils/dict';
  import { BpmModelFormType } from '@/utils/constants';
  import { dateFormatter } from '@/utils/formatTime';

  defineOptions({ name: 'BpmProcessDefinition' });

  const { push } = useRouter(); // 路由
  const { query } = useRoute(); // 查询参数

  // 表格引用
  const tableRef = ref();

  // 表格列配置
  const columns = [
    {
      prop: 'id',
      label: '定义编号',
      align: 'center',
      minWidth: 400
    },
    {
      prop: 'name',
      label: '流程名称',
      align: 'center',
      minWidth: 150
    },
    {
      prop: 'icon',
      label: '流程图标',
      align: 'center',
      minWidth: 90,
      slot: 'icon'
    },
    {
      prop: 'startUserIds',
      label: '可见范围',
      minWidth: 100,
      slot: 'startUserIds'
    },
    {
      prop: 'modelType',
      label: '流程类型',
      minWidth: 120,
      slot: 'modelType'
    },
    {
      prop: 'formType',
      label: '表单信息',
      minWidth: 150,
      slot: 'formType'
    },
    {
      prop: 'version',
      label: '流程版本',
      align: 'center',
      minWidth: 80,
      slot: 'version'
    },
    {
      prop: 'deploymentTime',
      label: '部署时间',
      align: 'center',
      width: 180,
      formatter: dateFormatter
    },
    {
      prop: 'action',
      label: '操作',
      align: 'center',
      slot: 'action',
      fixed: 'right'
    }
  ];

  /** 表格数据源 */
  const dataSource = ({ pages, where, orders }) => {
    const otheParams = {
      key: query.key
    };
    return DefinitionApi.getProcessDefinitionPage({
      ...where,
      ...orders,
      ...pages,
      ...otheParams
    });
  };

  /** 流程表单的详情按钮操作 */
  const formDetailVisible = ref(false);
  const formDetailPreview = ref({
    rule: [],
    option: {}
  });
  const handleFormDetail = async (row: any) => {
    if (row.formType == BpmModelFormType.NORMAL) {
      // 设置表单
      setConfAndFields2(formDetailPreview, row.formConf, row.formFields);
      // 弹窗打开
      formDetailVisible.value = true;
    } else {
      await push({
        path: row.formCustomCreatePath
      });
    }
  };

  /** 恢复流程模型弹窗 */
  const openModelForm = async (id?: number) => {
    await push({
      name: 'BpmModelUpdate',
      params: { id, type: 'definition' }
    });
  };
</script>

<style lang="scss" scoped>
  .flow-icon {
    display: flex;
    width: 38px;
    height: 38px;
    margin-right: 10px;
    background-color: var(--el-color-primary);
    border-radius: 0.25rem;
    align-items: center;
    justify-content: center;
  }
</style>
