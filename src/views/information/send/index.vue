<template>
  <div class="epd-container">
    <!-- 中间：播放列表和目标设备 -->
    <div class="main-section">
      <!-- 左侧：播放列表 -->
      <div class="playlist-section glass-effect">
        <div class="section-header">
          <div class="header-left">
            <div class="section-icon playlist-icon">
              <el-icon><VideoPlay /></el-icon>
            </div>
            <div class="header-text">
              <h3 class="section-title">播放列表</h3>
              <div class="section-subtitle">
                已添加
                <span class="highlight">{{ playlistImages.length }}</span>
                个内容
                <span class="separator">•</span>
                总时长 <span class="highlight">{{ totalDuration }}</span> 秒
              </div>
            </div>
          </div>
          <div class="playlist-actions">
            <el-button
              type="info"
              plain
              @click="openMaterialLib"
              class="action-btn"
            >
              <el-icon><FolderOpened /></el-icon>
              素材库
            </el-button>
            <el-button type="primary" @click="triggerUpload" class="action-btn">
              <el-icon><Upload /></el-icon>
              上传图片
            </el-button>
          </div>
        </div>

        <div class="playlist-content">
          <!-- 隐藏的上传组件 -->
          <el-upload
            ref="uploadRef"
            class="hidden-upload"
            action="#"
            :show-file-list="false"
            :before-upload="beforeUpload"
            :on-success="handleUploadSuccess"
            accept="image/*"
            multiple
          />

          <el-empty
            v-if="!playlistImages.length"
            description="暂无播放内容"
            class="playlist-empty"
          >
            <template #image>
              <div class="empty-image">
                <el-icon class="empty-icon"><VideoPlay /></el-icon>
              </div>
            </template>
            <template #default>
              <div class="empty-actions">
                <el-button type="primary" @click="triggerUpload">
                  <el-icon><Upload /></el-icon>
                  上传第一张图片
                </el-button>
                <el-button type="info" plain @click="openMaterialLib">
                  <el-icon><FolderOpened /></el-icon>
                  从素材库选择
                </el-button>
              </div>
            </template>
          </el-empty>

          <el-scrollbar v-else class="playlist-scroll" max-height="400px">
            <draggable
              v-model="playlistImages"
              item-key="id"
              handle=".drag-handle"
              class="draggable-list"
              @end="handleDragEnd"
            >
              <template #item="{ element, index }">
                <div
                  class="playlist-item"
                  :class="{ 'is-active': index === 0 }"
                >
                  <el-icon class="drag-handle"><Rank /></el-icon>
                  <div class="item-number">{{ index + 1 }}</div>
                  <div class="item-preview">
                    <el-image
                      :src="element.url"
                      :alt="element.name"
                      fit="cover"
                      :preview-src-list="[element.url]"
                      :initial-index="0"
                      preview-teleported
                    >
                      <template #placeholder>
                        <div class="image-loading">
                          <el-icon class="loading-icon"><Loading /></el-icon>
                        </div>
                      </template>
                      <template #error>
                        <div class="image-error">
                          <el-icon><Picture /></el-icon>
                        </div>
                      </template>
                    </el-image>
                    <div class="preview-overlay" v-if="index === 0">
                      <el-icon><VideoPlay /></el-icon>
                    </div>
                  </div>
                  <div class="item-info">
                    <div class="item-name">{{ element.name }}</div>
                    <div class="item-meta">
                      <span class="item-size">{{
                        formatFileSize(element.size)
                      }}</span>
                      <span class="separator">•</span>
                      <span class="item-duration"
                        >{{ playSettings.interval }}s</span
                      >
                    </div>
                  </div>
                  <div class="item-actions">
                    <el-button
                      type="primary"
                      link
                      @click="previewImage(element)"
                      class="action-icon"
                    >
                      <el-icon><View /></el-icon>
                    </el-button>
                    <el-button
                      type="danger"
                      link
                      @click="removeFromPlaylist(element.id)"
                      class="action-icon"
                    >
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                </div>
              </template>
            </draggable>
          </el-scrollbar>
        </div>
      </div>

      <!-- 右侧：目标设备 -->
      <div class="device-section glass-effect">
        <div class="section-header">
          <div class="header-left">
            <div class="section-icon device-icon">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="header-text">
              <h3 class="section-title">目标设备</h3>
              <div class="section-subtitle">
                已选择
                <span class="highlight">{{ selectedDevices.length }}</span>
                台设备
                <span class="separator">•</span>
                在线 <span class="highlight">{{ onlineDevicesCount }}</span> 台
              </div>
            </div>
          </div>
          <div class="device-actions">
            <el-button
              type="primary"
              @click="openDeviceDrawer"
              class="action-btn"
            >
              <el-icon><Plus /></el-icon>
              选择设备
            </el-button>
          </div>
        </div>

        <div class="device-content">
          <el-empty
            v-if="!selectedDevices.length"
            description="暂未选择设备"
            class="device-empty"
          >
            <template #image>
              <div class="empty-image">
                <el-icon class="empty-icon"><Monitor /></el-icon>
              </div>
            </template>
            <template #default>
              <div class="empty-actions">
                <el-button type="primary" @click="openDeviceDrawer">
                  <el-icon><Plus /></el-icon>
                  选择目标设备
                </el-button>
              </div>
            </template>
          </el-empty>

          <el-scrollbar v-else class="device-scroll" max-height="400px">
            <div class="device-list">
              <TransitionGroup name="device-list" tag="div">
                <div
                  v-for="device in selectedDevices"
                  :key="device.id"
                  class="device-item"
                  :class="{ 'is-online': device.status === 'online' }"
                >
                  <div class="device-main">
                    <div
                      class="device-status-dot"
                      :class="{ online: device.status === 'online' }"
                    ></div>
                    <div class="device-info">
                      <div class="device-name">{{ device.name }}</div>
                      <div class="device-location">{{ device.location }}</div>
                    </div>
                    <div class="device-type">{{ device.type }}</div>
                    <el-button
                      type="danger"
                      link
                      @click="removeDevice(device)"
                      class="remove-btn"
                    >
                      <el-icon><Close /></el-icon>
                    </el-button>
                  </div>
                </div>
              </TransitionGroup>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </div>

    <!-- 下方：播放策略和发布 -->
    <div class="bottom-section">
      <div class="strategy-publish glass-effect">
        <div class="strategy-header">
          <div class="header-left">
            <div class="section-icon strategy-icon">
              <el-icon><Setting /></el-icon>
            </div>
            <h3 class="section-title">播放策略与发布</h3>
          </div>
          <div class="header-right">
            <div class="publish-summary-inline">
              <div class="summary-item">
                <el-icon><VideoPlay /></el-icon>
                <span>{{ playlistImages.length }} 个内容</span>
              </div>
              <div class="summary-item">
                <el-icon><Monitor /></el-icon>
                <span>{{ selectedDevices.length }} 台设备</span>
              </div>
              <div class="summary-item">
                <el-icon><Timer /></el-icon>
                <span>{{ totalDuration }}s 总时长</span>
              </div>
            </div>
            <div class="header-actions">
              <el-button
                type="info"
                plain
                @click="previewPlaylist"
                class="preview-btn"
                size="default"
              >
                <el-icon><VideoPlay /></el-icon>
                预览播放
              </el-button>
              <el-button
                type="primary"
                size="default"
                :disabled="!canPublish"
                @click="publishToDevices"
                class="publish-btn"
              >
                <el-icon><Upload /></el-icon>
                立即发布
              </el-button>
            </div>
          </div>
        </div>

        <div class="strategy-body">
          <!-- 播放设置 -->
          <div class="settings-section">
            <el-form
              :model="playSettings"
              class="settings-form"
              label-position="top"
            >
              <div class="form-row">
                <el-form-item label="显示模式">
                  <el-select v-model="playSettings.mode" class="form-control">
                    <el-option label="横向显示" value="landscape">
                      <div class="option-content">
                        <el-icon><Monitor /></el-icon>
                        <span>横向显示</span>
                      </div>
                    </el-option>
                    <el-option label="纵向显示" value="portrait">
                      <div class="option-content">
                        <el-icon><Monitor /></el-icon>
                        <span>纵向显示</span>
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="播放顺序">
                  <el-select v-model="playSettings.order" class="form-control">
                    <el-option label="顺序播放" value="sequence">
                      <div class="option-content">
                        <el-icon><Sort /></el-icon>
                        <span>顺序播放</span>
                      </div>
                    </el-option>
                    <el-option label="随机播放" value="random">
                      <div class="option-content">
                        <el-icon><Switch /></el-icon>
                        <span>随机播放</span>
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="切换间隔">
                  <el-input-number
                    v-model="playSettings.interval"
                    :min="1"
                    :max="60"
                    class="form-control"
                    controls-position="right"
                  />
                </el-form-item>

                <el-form-item label="循环播放">
                  <el-switch
                    v-model="playSettings.loop"
                    active-color="#667eea"
                    size="large"
                  />
                </el-form-item>

                <el-form-item label="音效开关">
                  <el-switch
                    v-model="playSettings.sound"
                    active-color="#667eea"
                    size="large"
                  />
                </el-form-item>

                <el-form-item label="自动启动">
                  <el-switch
                    v-model="playSettings.autoStart"
                    active-color="#667eea"
                    size="large"
                  />
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>

        <!-- 发布状态 -->
        <div class="publish-status" v-if="publishStatus">
          <el-alert
            :title="publishStatus.title"
            :type="publishStatus.type"
            :description="publishStatus.description"
            show-icon
            :closable="false"
          />
        </div>
      </div>
    </div>

    <!-- 设备选择抽屉 -->
    <el-drawer
      v-model="deviceDrawerVisible"
      title="选择设备"
      size="70%"
      :destroy-on-close="false"
      class="device-drawer"
    >
      <template #header>
        <div class="drawer-header">
          <div class="drawer-title">
            <el-icon><Monitor /></el-icon>
            <span>选择目标设备</span>
          </div>
          <div class="drawer-actions">
            <el-button plain @click="clearDrawerSelection">清空选择</el-button>
            <el-button type="primary" @click="confirmDeviceSelection">
              确认选择 ({{ selectedDevicesInDrawer.length }})
            </el-button>
          </div>
        </div>
      </template>

      <div class="device-drawer-content">
        <!-- 左侧组织架构树 -->
        <div class="org-tree-section">
          <div class="tree-header">
            <h4 class="tree-title">
              <el-icon><OfficeBuilding /></el-icon>
              组织架构
            </h4>
          </div>
          <el-tree
            :data="organizationTree"
            :props="{ label: 'label' }"
            @node-click="handleNodeClick"
            default-expand-all
            highlight-current
            class="org-tree"
          >
            <template #default="{ node, data }">
              <div class="custom-tree-node">
                <el-icon v-if="!data.type" class="node-icon">
                  <OfficeBuilding />
                </el-icon>
                <el-icon v-else-if="data.type === 'region'" class="node-icon">
                  <Location />
                </el-icon>
                <el-icon v-else class="node-icon">
                  <Shop />
                </el-icon>
                <span class="node-label">{{ node.label }}</span>
              </div>
            </template>
          </el-tree>
        </div>

        <!-- 右侧设备列表 -->
        <div class="device-list-section">
          <div class="device-search">
            <el-input
              v-model="searchKeyword"
              placeholder="请输入设备名称或ID"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
              <template #append>
                <el-button @click="searchDevices">搜索</el-button>
              </template>
            </el-input>
          </div>

          <!-- 设备表格 -->
          <div class="device-tables">
            <div class="all-devices">
              <div class="table-header">
                <div class="header-info">
                  <span class="table-title">可选设备</span>
                  <el-tag type="info" size="small"
                    >{{ filteredDevices.length }} 台</el-tag
                  >
                </div>
                <el-button
                  type="primary"
                  plain
                  size="small"
                  @click="selectAllDevices"
                >
                  全选
                </el-button>
              </div>
              <el-table
                ref="deviceTableRef"
                :data="filteredDevices"
                style="width: 100%"
                @selection-change="handleDeviceSelect"
                height="calc(100% - 48px)"
                class="devices-table"
              >
                <el-table-column type="selection" width="55" />
                <el-table-column
                  prop="name"
                  label="设备名称"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="location"
                  label="位置"
                  show-overflow-tooltip
                />
                <el-table-column prop="deviceId" label="设备ID" width="120" />
                <el-table-column prop="status" label="状态" width="80">
                  <template #default="{ row }">
                    <el-tag
                      :type="row.status === 'online' ? 'success' : 'danger'"
                      effect="light"
                      size="small"
                    >
                      {{ row.status === 'online' ? '在线' : '离线' }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <div class="selected-devices">
              <div class="table-header">
                <div class="header-info">
                  <span class="table-title">已选设备</span>
                  <el-tag type="success" size="small"
                    >{{ selectedDevicesInDrawer.length }} 台</el-tag
                  >
                </div>
                <el-button
                  type="danger"
                  plain
                  size="small"
                  @click="clearDrawerSelection"
                >
                  清空
                </el-button>
              </div>
              <el-table
                :data="selectedDevicesInDrawer"
                style="width: 100%"
                height="calc(100% - 48px)"
                class="selected-table"
              >
                <el-table-column
                  prop="name"
                  label="设备名称"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="location"
                  label="位置"
                  show-overflow-tooltip
                />
                <el-table-column prop="deviceId" label="设备ID" width="120" />
                <el-table-column width="60" fixed="right">
                  <template #default="{ row }">
                    <el-button
                      type="danger"
                      link
                      @click="removeSelectedDevice(row)"
                      size="small"
                    >
                      移除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 素材库对话框 -->
    <el-dialog
      v-model="materialLibVisible"
      title="素材库"
      width="70%"
      class="material-lib-dialog"
    >
      <div class="material-lib-content">
        <el-empty description="素材库功能开发中..." />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, computed, nextTick } from 'vue';
  import { ElMessage } from 'element-plus';
  import draggable from 'vuedraggable';
  import {
    Upload,
    Select,
    VideoPlay,
    Delete,
    FolderOpened,
    Rank,
    Picture,
    Monitor,
    Setting,
    Sort,
    Switch,
    Document,
    Timer,
    Close,
    Plus,
    OfficeBuilding,
    Location,
    Shop,
    Search,
    List,
    Loading,
    View
  } from '@element-plus/icons-vue';

  // 导入图片资源
  import avatarImg from '@/assets/avatar.jpg';
  import wallpaper1 from '@/assets/wallpaper-01.jpg';
  import wallpaper2 from '@/assets/wallpaper-02.jpg';
  import wallpaper3 from '@/assets/wallpaper-03.jpg';
  import wallpaper4 from '@/assets/wallpaper-04.jpg';
  import loginBg from '@/assets/login-bg.png';

  // 上传相关
  const uploadRef = ref(null);
  const uploadedImages = ref([
    {
      id: 1,
      name: 'avatar.jpg',
      url: avatarImg,
      size: 2.5 * 1024 * 1024,
      date: '2024-03-01',
      selected: false
    },
    {
      id: 2,
      name: 'wallpaper-01.jpg',
      url: wallpaper1,
      size: 1.8 * 1024 * 1024,
      date: '2024-03-02',
      selected: false
    },
    {
      id: 3,
      name: 'wallpaper-02.jpg',
      url: wallpaper2,
      size: 3.2 * 1024 * 1024,
      date: '2024-03-03',
      selected: false
    },
    {
      id: 4,
      name: 'wallpaper-03.jpg',
      url: wallpaper3,
      size: 1.2 * 1024 * 1024,
      date: '2024-03-04',
      selected: false
    },
    {
      id: 5,
      name: 'wallpaper-04.jpg',
      url: wallpaper4,
      size: 2.8 * 1024 * 1024,
      date: '2024-03-05',
      selected: false
    },
    {
      id: 6,
      name: 'login-bg.png',
      url: loginBg,
      size: 1.5 * 1024 * 1024,
      date: '2024-03-06',
      selected: false
    }
  ]);

  const selectedImages = ref([]);

  // 设备列表
  const devices = ref([
    {
      id: 1,
      name: '大堂显示屏',
      location: '1号楼大堂',
      status: 'online',
      lastActive: '2024-03-10 14:30:00',
      type: 'LCD',
      resolution: '2560x1440'
    },
    {
      id: 2,
      name: '会议室屏幕',
      location: '2号楼3层',
      status: 'online',
      lastActive: '2024-03-10 14:28:00',
      type: 'LED',
      resolution: '2560x1440'
    }
  ]);

  const selectedDevices = ref([]);

  // 播放列表
  const playlistImages = ref([]);
  const playSettings = ref({
    mode: 'landscape',
    order: 'sequence',
    interval: 5,
    loop: true,
    sound: true,
    autoStart: true
  });

  // 素材库
  const materialLibVisible = ref(false);

  // 设备选择抽屉
  const deviceDrawerVisible = ref(false);
  const deviceTableRef = ref(null);
  const selectedDevicesInDrawer = ref([]);
  const searchKeyword = ref('');

  // 发布状态
  const publishStatus = ref(null);

  const organizationTree = ref([
    {
      id: 1,
      label: '首开创',
      children: [
        {
          id: 11,
          label: '广东',
          type: 'region'
        },
        {
          id: 12,
          label: '上海',
          type: 'region',
          children: [
            {
              id: 121,
              label: '上海浦东机场店',
              type: 'store'
            }
          ]
        }
      ]
    }
  ]);

  const currentOrg = ref(11);
  const deviceList = ref([
    {
      id: 1,
      name: '大堂显示屏A',
      location: '1号楼大堂',
      orgId: 11,
      deviceId: 'DEV001',
      resolution: '2560x1440',
      direction: '横向',
      status: 'online'
    },
    {
      id: 2,
      name: '会议室屏幕B',
      location: '2号楼3层',
      orgId: 11,
      deviceId: 'DEV002',
      resolution: '2560x1440',
      direction: '横向',
      status: 'online'
    },
    {
      id: 3,
      name: '休息区显示器C',
      location: '1号楼2层',
      orgId: 11,
      deviceId: 'DEV003',
      resolution: '1920x1080',
      direction: '横向',
      status: 'offline'
    }
  ]);

  // 计算属性
  const canPublish = computed(() => {
    return selectedDevices.value.length > 0 && playlistImages.value.length > 0;
  });

  const filteredDevices = computed(() => {
    if (!currentOrg.value) return [];
    return deviceList.value.filter((device) => {
      const matchOrg = device.orgId === currentOrg.value;
      const matchSearch = searchKeyword.value
        ? device.name.includes(searchKeyword.value) ||
          device.location.includes(searchKeyword.value) ||
          device.deviceId.includes(searchKeyword.value)
        : true;
      return matchOrg && matchSearch;
    });
  });

  const totalDuration = computed(() => {
    return playlistImages.value.length * playSettings.value.interval;
  });

  const onlineDevicesCount = computed(() => {
    return selectedDevices.value.filter((device) => device.status === 'online')
      .length;
  });

  // 方法
  function triggerUpload() {
    uploadRef.value?.$el?.querySelector('input')?.click();
  }

  function beforeUpload(file) {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      ElMessage.error('请上传图片文件');
      return false;
    }
    return true;
  }

  function handleUploadSuccess(response, file) {
    const url = URL.createObjectURL(file.raw);
    uploadedImages.value.push({
      id: Date.now(),
      name: file.name,
      url: url,
      size: file.size,
      date: new Date().toISOString().slice(0, 10),
      selected: false
    });
  }

  function toggleImageSelect(img) {
    // 这个函数暂时保留，以防其他地方调用
  }

  function clearImageSelection() {
    // 这个函数暂时保留，以防其他地方调用
  }

  function openDeviceDrawer() {
    deviceDrawerVisible.value = true;
    selectedDevicesInDrawer.value = [...selectedDevices.value];
  }

  function handleNodeClick(data) {
    if (data.type === 'region' || data.type === 'store') {
      currentOrg.value = data.id;
    }
  }

  function handleDeviceSelect(selection) {
    selectedDevicesInDrawer.value = selection;
  }

  function selectAllDevices() {
    deviceTableRef.value?.toggleAllSelection();
  }

  function clearDrawerSelection() {
    if (deviceTableRef.value) {
      deviceTableRef.value.clearSelection();
    }
    selectedDevicesInDrawer.value = [];
  }

  function removeSelectedDevice(device) {
    selectedDevicesInDrawer.value = selectedDevicesInDrawer.value.filter(
      (item) => item.id !== device.id
    );
    if (deviceTableRef.value) {
      deviceTableRef.value.toggleRowSelection(device, false);
    }
  }

  function confirmDeviceSelection() {
    selectedDevices.value = [...selectedDevicesInDrawer.value];

    // 添加选中的图片到播放列表
    const selectedImgs = uploadedImages.value.filter((img) => img.selected);
    if (selectedImgs.length > 0) {
      playlistImages.value.push(
        ...selectedImgs.map((img, index) => ({
          ...img,
          order: playlistImages.value.length + index + 1
        }))
      );
      clearImageSelection();
    }

    deviceDrawerVisible.value = false;
    ElMessage.success(`已选择 ${selectedDevices.value.length} 台设备`);
  }

  function removeDevice(device) {
    selectedDevices.value = selectedDevices.value.filter(
      (d) => d.id !== device.id
    );
  }

  function removeFromPlaylist(id) {
    playlistImages.value = playlistImages.value.filter((img) => img.id !== id);
  }

  function handleDragEnd() {
    // 更新排序
    playlistImages.value.forEach((img, index) => {
      img.order = index + 1;
    });
  }

  function searchDevices() {
    // 搜索逻辑
  }

  function openMaterialLib() {
    materialLibVisible.value = true;
  }

  function previewPlaylist() {
    ElMessage.info('预览功能开发中');
  }

  function publishToDevices() {
    // 设置发布状态
    publishStatus.value = {
      title: '发布成功',
      type: 'success',
      description: `已成功发布到 ${selectedDevices.value.length} 台设备，${onlineDevicesCount.value} 台在线设备将立即生效`
    };

    // 3秒后清除状态
    setTimeout(() => {
      publishStatus.value = null;
    }, 3000);

    ElMessage.success(`已成功发布到 ${selectedDevices.value.length} 台设备`);
  }

  function previewImage(image) {
    ElMessage.info(`预览图片: ${image.name}`);
  }

  function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  }
</script>

<style scoped>
  .epd-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 20px;
    height: calc(100vh - 40px);
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  }

  /* 玻璃效果 */
  .glass-effect {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.1);
    border-radius: 16px;
    overflow: hidden;
  }

  /* 中间区域 */
  .main-section {
    display: grid;
    grid-template-columns: 55% 43%;
    gap: 2%;
    flex: 1;
    min-height: 0;
  }

  .playlist-section,
  .device-section {
    display: flex;
    flex-direction: column;
    padding: 20px;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .section-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    color: white;
    font-size: 16px;
  }

  .playlist-icon {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  }

  .device-icon {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  }

  .strategy-icon {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #1a202c;
    margin: 0;
  }

  .section-subtitle {
    font-size: 12px;
    color: #718096;
    margin-top: 2px;
  }

  .highlight {
    color: #667eea;
    font-weight: 600;
  }

  .separator {
    margin: 0 6px;
    color: #cbd5e0;
  }

  .playlist-actions,
  .device-actions {
    display: flex;
    gap: 8px;
  }

  .action-btn {
    border-radius: 6px;
    font-size: 12px;
    padding: 6px 12px;
    transition: all 0.3s ease;
  }

  .action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .hidden-upload {
    display: none;
  }

  .playlist-content,
  .device-content {
    flex: 1;
    overflow: hidden;
  }

  .playlist-empty,
  .device-empty {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .empty-image {
    text-align: center;
    margin-bottom: 16px;
  }

  .empty-icon {
    font-size: 40px;
    color: #cbd5e0;
  }

  .empty-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
  }

  .playlist-scroll,
  .device-scroll {
    height: 100%;
  }

  /* 播放列表项 */
  .draggable-list {
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding: 4px;
  }

  .playlist-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
  }

  .playlist-item:hover {
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
    transform: translateY(-1px);
  }

  .playlist-item.is-active {
    border: 2px solid #48bb78;
    box-shadow: 0 3px 8px rgba(72, 187, 120, 0.2);
  }

  .playlist-item.is-active::before {
    content: '正在播放';
    position: absolute;
    top: -6px;
    left: 10px;
    background: #48bb78;
    color: white;
    padding: 1px 6px;
    border-radius: 3px;
    font-size: 10px;
  }

  .drag-handle {
    cursor: move;
    color: #a0aec0;
    font-size: 14px;
  }

  .item-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    background: #667eea;
    color: white;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
  }

  .item-preview {
    width: 50px;
    height: 38px;
    border-radius: 4px;
    overflow: hidden;
    flex-shrink: 0;
    position: relative;
  }

  .preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .playlist-item.is-active .preview-overlay {
    opacity: 1;
  }

  .item-info {
    flex: 1;
    min-width: 0;
  }

  .item-name {
    font-size: 13px;
    font-weight: 500;
    color: #2d3748;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .item-meta {
    display: flex;
    align-items: center;
    gap: 3px;
    color: #718096;
    font-size: 11px;
    margin-top: 2px;
  }

  .item-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .playlist-item:hover .item-actions {
    opacity: 1;
  }

  .action-icon {
    padding: 2px;
    font-size: 14px;
  }

  /* 设备列表 - 简约风格 */
  .device-list {
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding: 4px;
  }

  .device-item {
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
  }

  .device-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
  }

  .device-item.is-online {
    border-left: 4px solid #48bb78;
  }

  .device-main {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
  }

  .device-status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #cbd5e0;
    flex-shrink: 0;
  }

  .device-status-dot.online {
    background: #48bb78;
  }

  .device-info {
    flex: 1;
    min-width: 0;
  }

  .device-name {
    font-size: 14px;
    font-weight: 500;
    color: #2d3748;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .device-location {
    font-size: 12px;
    color: #718096;
    margin-top: 2px;
  }

  .device-type {
    font-size: 12px;
    color: #4a5568;
    background: #f7fafc;
    padding: 2px 6px;
    border-radius: 4px;
    flex-shrink: 0;
  }

  .remove-btn {
    opacity: 0;
    transition: opacity 0.3s ease;
    padding: 4px;
    font-size: 14px;
  }

  .device-item:hover .remove-btn {
    opacity: 1;
  }

  /* 下方区域 */
  .bottom-section {
    height: 180px;
    flex-shrink: 0;
  }

  .strategy-publish {
    display: flex;
    flex-direction: column;
    padding: 16px 20px;
  }

  .strategy-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .header-actions {
    display: flex;
    gap: 8px;
  }

  .publish-summary-inline {
    display: flex;
    gap: 20px;
  }

  .summary-item {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #4a5568;
    font-size: 13px;
  }

  .summary-item .el-icon {
    font-size: 16px;
    color: #667eea;
  }

  .strategy-body {
    display: flex;
    align-items: center;
    gap: 20px;
    flex: 1;
  }

  .settings-section {
    flex: 1;
  }

  .settings-form {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .form-row {
    display: flex;
    gap: 16px;
    align-items: flex-end;
  }

  .form-row .el-form-item {
    margin-bottom: 0;
  }

  .form-control {
    width: 140px;
  }

  .option-content {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .publish-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;
  }

  .preview-btn,
  .publish-btn {
    font-size: 14px;
    padding: 8px 16px;
    height: 32px;
  }

  .preview-btn {
    border-color: #d1d5db;
    color: #6b7280;
  }

  .publish-btn {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    border: none;
    box-shadow: 0 2px 4px rgba(72, 187, 120, 0.3);
  }

  .publish-status {
    margin-top: 12px;
  }

  /* 图片加载和错误状态 */
  .image-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #9ca3af;
    font-size: 12px;
  }

  .image-loading .loading-icon {
    font-size: 20px;
    margin-bottom: 4px;
    animation: rotate 1s linear infinite;
  }

  .image-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #a0aec0;
    font-size: 12px;
  }

  .image-error .el-icon {
    font-size: 20px;
    margin-bottom: 4px;
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  /* 设备选择抽屉样式 */
  .device-drawer {
    :deep(.el-drawer__header) {
      margin-bottom: 0;
      padding: 16px 20px;
      border-bottom: 1px solid #e2e8f0;
      background: #f7fafc;
    }

    :deep(.el-drawer__body) {
      padding: 0;
      background: #f7fafc;
    }
  }

  .drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .drawer-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
  }

  .drawer-actions {
    display: flex;
    gap: 12px;
  }

  .device-drawer-content {
    display: flex;
    height: 100%;
    gap: 20px;
    padding: 20px;
  }

  .org-tree-section {
    width: 260px;
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .tree-header {
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e2e8f0;
  }

  .tree-title {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
  }

  .org-tree {
    :deep(.el-tree-node__content) {
      padding: 6px 0;
      border-radius: 4px;
      margin-bottom: 2px;
    }

    :deep(.el-tree-node__content:hover) {
      background: #f7fafc;
    }

    :deep(.el-tree-node.is-current > .el-tree-node__content) {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
  }

  .custom-tree-node {
    display: flex;
    align-items: center;
    gap: 6px;
    width: 100%;
  }

  .node-icon {
    font-size: 14px;
  }

  .node-label {
    font-size: 13px;
  }

  .device-list-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .device-search {
    background: white;
    padding: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .device-tables {
    flex: 1;
    display: grid;
    grid-template-rows: 60% 40%;
    gap: 16px;
  }

  .all-devices,
  .selected-devices {
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
  }

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f7fafc;
    border-bottom: 1px solid #e2e8f0;
  }

  .header-info {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .table-title {
    font-size: 14px;
    font-weight: 500;
    color: #2d3748;
  }

  .devices-table,
  .selected-table {
    :deep(.el-table__header) {
      background: #f7fafc;
    }

    :deep(.el-table__row:hover) {
      background: #f7fafc;
    }
  }

  /* 动画效果 */
  .device-list-enter-active,
  .device-list-leave-active {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .device-list-enter-from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }

  .device-list-leave-to {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }

  .device-list-move {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Element Plus 组件样式调整 */
  :deep(.el-form-item) {
    margin-bottom: 8px;
  }

  :deep(.el-form-item__label) {
    font-size: 12px;
    color: #4a5568;
    font-weight: 500;
    line-height: 1.2;
    margin-bottom: 4px;
  }

  :deep(.el-form-item__content) {
    line-height: 1;
  }

  :deep(.el-input-number) {
    width: 100%;
  }

  :deep(.el-switch) {
    --el-switch-on-color: #667eea;
  }

  :deep(.form-row .el-form-item) {
    margin-bottom: 0;
  }

  :deep(.form-row .el-form-item__label) {
    height: 16px;
    line-height: 16px;
    margin-bottom: 4px;
  }

  :deep(.form-row .el-form-item__content) {
    display: flex;
    align-items: center;
    min-height: 32px;
  }

  /* 工具类 */
  .w-full {
    width: 100%;
  }
</style>
