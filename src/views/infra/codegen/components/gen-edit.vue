<!-- 编辑弹窗 -->
<template>
  <ele-drawer
    :size="1320"
    style="max-width: 100%"
    :append-to-body="true"
    :destroy-on-close="true"
    :body-style="{ padding: '16px 10px' }"
    :footer-style="{ display: 'flex', alignItems: 'center' }"
    :title="`修改[${data?.tableName}]生成配置`"
    :model-value="modelValue"
    @update:modelValue="updateModelValue"
    @opened="onOpened"
  >
    <ele-loading :loading="initLoading">
      <el-form
        ref="formRef"
        :model="form.table"
        :rules="rules"
        label-width="128px"
        @submit.prevent=""
      >
        <el-row :gutter="16">
          <el-col :sm="8" :xs="24">
            <el-form-item label="表名称" prop="tableName">
              <el-input
                clearable
                :maxlength="20"
                v-model="form.table.tableName"
                placeholder="请输入表名称"
              />
            </el-form-item>
          </el-col>
          <el-col :sm="8" :xs="24">
            <el-form-item label="表描述" prop="tableComment">
              <el-input
                clearable
                :maxlength="100"
                v-model="form.table.tableComment"
                placeholder="请输入表描述"
              />
            </el-form-item>
          </el-col>
          <el-col :sm="8" :xs="24">
            <el-form-item label="作者" prop="author">
              <el-input
                clearable
                :maxlength="20"
                v-model="form.table.author"
                placeholder="请输入作者"
              />
            </el-form-item>
          </el-col>
          <el-col :sm="8" :xs="24">
            <el-form-item label="生成模板" prop="templateType">
              <el-select v-model="form.table.templateType">
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.INFRA_CODEGEN_TEMPLATE_TYPE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="8" :xs="24">
            <el-form-item label="前端类型" prop="frontType">
              <el-select v-model="form.table.frontType">
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.INFRA_CODEGEN_FRONT_TYPE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="8" :xs="24">
            <el-form-item label="生成场景" prop="scene">
              <el-select v-model="form.table.scene">
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.INFRA_CODEGEN_SCENE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="8" :xs="24">
            <el-form-item>
              <template #label>
                <span>
                  上级菜单
                  <el-tooltip
                    content="分配到指定菜单下，例如 系统管理"
                    placement="top"
                  >
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </span>
              </template>
              <el-tree-select
                v-model="form.table.parentMenuId"
                :data="menus"
                :props="menuTreeProps"
                check-strictly
                node-key="id"
                placeholder="请选择系统菜单"
              />
            </el-form-item>
          </el-col>

          <el-col :sm="8" :xs="24">
            <el-form-item prop="moduleName">
              <template #label>
                <span>
                  模块名
                  <el-tooltip
                    content="模块名，即一级目录，例如 system、infra、tool 等等"
                    placement="top"
                  >
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </span>
              </template>
              <el-input
                clearable
                :maxlength="20"
                v-model="form.table.moduleName"
                placeholder="请输入生成模块名"
              />
            </el-form-item>
          </el-col>
          <el-col :sm="8" :xs="24">
            <el-form-item prop="businessName">
              <template #label>
                <span>
                  业务名
                  <el-tooltip
                    content="业务名，即二级目录，例如 user、permission、dict 等等"
                    placement="top"
                  >
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </span>
              </template>
              <el-input
                clearable
                :maxlength="20"
                v-model="form.table.businessName"
                placeholder="请输入生成业务名"
              />
            </el-form-item>
          </el-col>
          <el-col :sm="8" :xs="24">
            <el-form-item prop="className">
              <template #label>
                <span>
                  类名称
                  <el-tooltip
                    content="类名称（首字母大写），例如SysUser、SysMenu、SysDictData 等等"
                    placement="top"
                  >
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </span>
              </template>
              <el-input v-model="form.table.className" />
            </el-form-item>
          </el-col>
          <el-col :sm="8" :xs="24">
            <el-form-item prop="classComment">
              <template #label>
                <span>
                  类描述
                  <el-tooltip content="用作类描述，例如 用户" placement="top">
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </span>
              </template>
              <el-input v-model="form.table.classComment" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item v-if="form.table.genType == '1'">
          <template #label>
            <span>
              自定义路径
              <el-tooltip
                content="填写磁盘绝对路径，若不填写，则生成到当前Web项目下"
                placement="top"
              >
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </span>
          </template>
          <el-input
            clearable
            :maxlength="200"
            v-model="form.table.genPath"
            placeholder="请输入自定义路径"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            :rows="2"
            type="textarea"
            :maxlength="200"
            v-model="form.table.remark"
            placeholder="请输入备注"
          />
        </el-form-item>
        <!-- 树表信息 -->
        <el-row :gutter="16" v-if="form.table.templateType == 2">
          <el-col :span="12">
            <el-form-item prop="treeParentColumnId">
              <template #label>
                <span>
                  父编号字段
                  <el-tooltip
                    content="树显示的父编码字段名， 如：parent_Id"
                    placement="top"
                  >
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </span>
              </template>
              <el-select
                v-model="form.table.treeParentColumnId"
                placeholder="请选择"
              >
                <el-option
                  v-for="(column, index) in form.columns"
                  :key="index"
                  :label="column.columnName + '：' + column.columnComment"
                  :value="column.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="treeNameColumnId">
              <template #label>
                <span>
                  树名称字段
                  <el-tooltip
                    content="树节点的显示名称字段名， 如：dept_name"
                    placement="top"
                  >
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </span>
              </template>
              <el-select
                v-model="form.table.treeNameColumnId"
                placeholder="请选择"
              >
                <el-option
                  v-for="(column, index) in form.columns"
                  :key="index"
                  :label="column.columnName + '：' + column.columnComment"
                  :value="column.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 主表信息 -->
        <el-row :gutter="16" v-if="form.table.templateType == 15">
          <el-col :span="12">
            <el-form-item prop="masterTableId">
              <template #label>
                <span>
                  关联的主表
                  <el-tooltip
                    content="关联主表（父表）的表名， 如：system_user"
                    placement="top"
                  >
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </span>
              </template>
              <el-select
                v-model="form.table.masterTableId"
                placeholder="请选择"
                class="ele-fluid"
              >
                <el-option
                  v-for="(table0, index) in tables"
                  :key="index"
                  :label="table0.tableName + '：' + table0.tableComment"
                  :value="table0.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="subJoinColumnId">
              <template #label>
                <span>
                  子表关联的字段
                  <el-tooltip
                    content="子表关联的字段， 如：user_id"
                    placement="top"
                    class="ele-fluid"
                  >
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </span>
              </template>
              <el-select
                v-model="form.table.subJoinColumnId"
                placeholder="请选择"
              >
                <el-option
                  v-for="(column, index) in form.columns"
                  :key="index"
                  :label="column.columnName + '：' + column.columnComment"
                  :value="column.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="subJoinMany">
              <template #label>
                <span>
                  关联关系
                  <el-tooltip content="主表与子表的关联关系" placement="top">
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </span>
              </template>
              <el-radio-group
                v-model="form.table.subJoinMany"
                placeholder="请选择"
              >
                <el-radio :value="true">一对多</el-radio>
                <el-radio :value="false">一对一</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <sticky-table class="form-table" :table-style="{ minWidth: '1280px' }">
          <template #colgroup>
            <col width="48px" />
            <col />
            <col width="140px" />
            <col />
            <col width="120px" />
            <col width="120px" />
            <col width="28px" />
            <col width="28px" />
            <col width="28px" />
            <col width="28px" />
            <col width="128px" />
            <col width="48px" />
            <col width="128px" />
            <col width="128px" />
            <col width="128px" />
          </template>
          <template #thead>
            <tr>
              <th class="form-table-index"></th>
              <th>列名</th>
              <th>字段描述</th>
              <th>物理类型</th>
              <th>Java类型</th>
              <th>Java属性</th>
              <th>插入</th>
              <th>编辑</th>
              <th>列表</th>
              <th>查询</th>
              <th>查询方式</th>
              <th>允许空</th>
              <th>显示类型</th>
              <th>字典类型</th>
              <th>示例</th>
            </tr>
          </template>
          <template #tbody>
            <tr v-for="(row, index) in form.columns" :key="row.id">
              <td class="form-table-index">{{ index + 1 }}</td>
              <td>{{ row.columnName }}</td>
              <td>
                <el-form-item
                  label-width="0px"
                  class="form-error-popper"
                  style="margin-bottom: 0"
                >
                  <el-input v-model="row.columnComment" placeholder="请输入" />
                </el-form-item>
              </td>
              <td>{{ row.dataType }}</td>
              <td>
                <el-form-item
                  label-width="0px"
                  class="form-error-popper"
                  style="margin-bottom: 0"
                >
                  <el-select
                    v-model="row.javaType"
                    placeholder="请选择"
                    class="ele-fluid"
                  >
                    <el-option label="Long" value="Long" />
                    <el-option label="String" value="String" />
                    <el-option label="Integer" value="Integer" />
                    <el-option label="Double" value="Double" />
                    <el-option label="BigDecimal" value="BigDecimal" />
                    <el-option label="Date" value="Date" />
                    <el-option label="Boolean" value="Boolean" />
                    <el-option label="LocalDate" value="LocalDate" />
                    <el-option label="LocalTime" value="LocalTime" />
                    <el-option label="LocalDateTime" value="LocalDateTime" />
                  </el-select>
                </el-form-item>
              </td>
              <td>
                <el-form-item
                  label-width="0px"
                  class="form-error-popper"
                  style="margin-bottom: 0"
                >
                  <el-input v-model="row.javaField" placeholder="请输入" />
                </el-form-item>
              </td>
              <td>
                <el-form-item
                  label-width="0px"
                  class="form-error-popper"
                  style="margin-bottom: 0"
                >
                  <el-checkbox
                    v-model="row.createOperation"
                    true-value="true"
                    false-value="false"
                  />
                </el-form-item>
              </td>
              <td>
                <el-form-item
                  label-width="0px"
                  class="form-error-popper"
                  style="margin-bottom: 0"
                >
                  <el-checkbox
                    v-model="row.updateOperation"
                    true-value="true"
                    false-value="false"
                  />
                </el-form-item>
              </td>
              <td>
                <el-form-item
                  label-width="0px"
                  class="form-error-popper"
                  style="margin-bottom: 0"
                >
                  <el-checkbox
                    v-model="row.listOperationResult"
                    true-value="true"
                    false-value="false"
                  />
                </el-form-item>
              </td>
              <td>
                <el-form-item
                  label-width="0px"
                  class="form-error-popper"
                  style="margin-bottom: 0"
                >
                  <el-checkbox
                    v-model="row.listOperation"
                    true-value="true"
                    false-value="false"
                  />
                </el-form-item>
              </td>
              <td>
                <el-form-item
                  label-width="0px"
                  class="form-error-popper"
                  style="margin-bottom: 0"
                >
                  <el-select
                    v-model="row.listOperationCondition"
                    placeholder="请选择"
                    class="ele-fluid"
                  >
                    <el-option label="=" value="=" />
                    <el-option label="!=" value="!=" />
                    <el-option label=">" value=">" />
                    <el-option label=">=" value=">=" />
                    <el-option label="<" value="<>" />
                    <el-option label="<=" value="<=" />
                    <el-option label="LIKE" value="LIKE" />
                    <el-option label="BETWEEN" value="BETWEEN" />
                  </el-select>
                </el-form-item>
              </td>
              <td>
                <el-form-item
                  label-width="0px"
                  class="form-error-popper"
                  style="margin-bottom: 0"
                >
                  <el-checkbox
                    v-model="row.nullable"
                    true-value="true"
                    false-value="false"
                  />
                </el-form-item>
              </td>
              <td>
                <el-form-item
                  label-width="0px"
                  class="form-error-popper"
                  style="margin-bottom: 0"
                >
                  <el-select
                    v-model="row.htmlType"
                    placeholder="请选择"
                    class="ele-fluid"
                  >
                    <el-option label="文本框" value="input" />
                    <el-option label="文本域" value="textarea" />
                    <el-option label="下拉框" value="select" />
                    <el-option label="单选框" value="radio" />
                    <el-option label="复选框" value="checkbox" />
                    <el-option label="日期控件" value="datetime" />
                    <el-option label="图片上传" value="imageUpload" />
                    <el-option label="文件上传" value="fileUpload" />
                    <el-option label="富文本控件" value="editor" />
                  </el-select>
                </el-form-item>
              </td>
              <td>
                <el-form-item
                  label-width="0px"
                  class="form-error-popper"
                  style="margin-bottom: 0"
                >
                  <el-select
                    clearable
                    v-model="row.dictType"
                    placeholder="请选择"
                    class="ele-fluid"
                  >
                    <el-option
                      v-for="dict in dictOptions"
                      :key="dict.id"
                      :label="dict.name"
                      :value="dict.type"
                    />
                  </el-select>
                </el-form-item>
              </td>
              <td>
                <el-input v-model="row.example" />
              </td>
            </tr>
          </template>
        </sticky-table>
      </el-form>
    </ele-loading>
    <template #footer>
      <div style="flex: 1; text-align: left">
        <ele-text v-if="validMsg" type="danger" :icon="CloseCircleOutlined">
          <span>{{ validMsg }}</span>
        </ele-text>
      </div>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { ref, reactive, watch, computed } from 'vue';
  import { EleMessage } from 'sirius-platform-pro/es';
  import { CloseCircleOutlined } from '@/components/icons';
  import { useFormData } from '@/utils/use-form-data';
  import ColumnSelect from './column-select.vue';
  import * as DictDataApi from '@/api/system/dict/dict.type';
  import StickyTable from './sticky-table.vue';
  import { getGenTable, updateGen } from '@/api/tool/gen';
  import * as CodegenApi from '@/api/infra/codegen';
  import { handleTree } from '@/utils/tree';
  import * as MenuApi from '@/api/system/menu';

  import { QuestionFilled } from '@element-plus/icons-vue';

  const emit = defineEmits(['done', 'update:modelValue']);
  const menus = ref([]);
  const menuTreeProps = {
    label: 'name'
  };

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显数据 */
    data: Object
  });

  /** 字段下拉数据 */
  const cols = ref([]);

  /** 表下拉数据 */
  const tables = ref([]);

  /** 选中的表字段下拉数据 */
  const tableCols = computed(() => {
    const name = form.subTableName;
    if (!name) {
      return [];
    }
    return tables.value.find((t) => t.tableName == name)?.columns ?? [];
  });
  const { t } = useI18n(); // 国际化
  /** 字典类型下拉数据 */
  const dictOptions = ref([]);
  const message = useMessage(); // 消息弹窗

  /** 回显查询状态 */
  const initLoading = ref(true);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    table: {},
    columns: []
  });

  /** 表单验证规则 */
  const rules = reactive({
    tableName: [
      {
        required: true,
        message: '请输入表名称',
        type: 'string',
        trigger: 'blur'
      }
    ],
    tableComment: [
      {
        required: true,
        message: '请输入表描述',
        type: 'string',
        trigger: 'blur'
      }
    ],
    auth: [
      {
        required: true,
        message: '请输入作者',
        type: 'string',
        trigger: 'blur'
      }
    ],
    templateType: [
      {
        required: true,
        message: '请选择模版',
        trigger: 'blur'
      }
    ],
    frontType: [
      {
        required: true,
        message: '请选择前端类型',
        trigger: 'blur'
      }
    ],
    scene: [
      {
        required: true,
        message: '请选择生成场景',
        trigger: 'blur'
      }
    ],
    moduleName: [
      {
        required: true,
        message: '请输入生成模块名',
        type: 'string',
        trigger: 'blur'
      }
    ],
    businessName: [
      {
        required: true,
        message: '请输入生成业务名',
        type: 'string',
        trigger: 'blur'
      }
    ],
    className: [
      {
        required: true,
        message: '请输入类名',
        type: 'string',
        trigger: 'blur'
      }
    ],
    classComment: [
      {
        required: true,
        trigger: 'blur'
      }
    ],
    masterTableId: [
      {
        required: true,
        trigger: 'blur'
      }
    ],
    subJoinColumnId: [
      {
        required: true,
        trigger: 'blur'
      }
    ],
    subJoinMany: [
      {
        required: true,
        trigger: 'blur'
      }
    ],
    treeParentColumnId: [
      {
        required: true,
        trigger: 'blur'
      }
    ],
    treeNameColumnId: [
      {
        required: true,
        trigger: 'blur'
      }
    ]
  });

  /** 表单验证失败提示信息 */
  const validMsg = ref('');

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.(async (valid, obj) => {
      if (!valid) {
        const errors = obj ? Object.keys(obj).length : 0;
        validMsg.value = ` 共有 ${errors} 项校验不通过`;
        return;
      }
      validMsg.value = '';
      loading.value = true;
      try {
        // 提交请求
        await CodegenApi.updateCodegenTable({ ...form });
        message.success(t('common.updateSuccess'));
        updateModelValue(false);
      } finally {
        loading.value = false;
      }
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  /** 查询回显数据 */
  const onOpened = async () => {
    if (!props.data?.id) {
      return;
    }
    initLoading.value = true;
    try {
      const res = await CodegenApi.getCodegenTable(props.data.id);
      assignFields({
        ...res
      });
      const resp = await MenuApi.getSimpleMenusList();
      menus.value = handleTree(resp);
      tables.value = await CodegenApi.getCodegenTableList(
        res.table.dataSourceConfigId
      );
      dictOptions.value = await DictDataApi.getSimpleDictTypeList();
    } finally {
      initLoading.value = false;
    }
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (!modelValue) {
        resetFields();
        formRef.value?.clearValidate?.();
        validMsg.value = '';
      }
    }
  );
</script>

<style lang="scss" scoped>
  /* 表单验证气泡形式 */
  .form-table :deep(table) {
    table-layout: fixed;

    .el-form-item > .el-form-item__content {
      justify-content: center;

      & > .el-form-item__error {
        position: absolute;
        left: 0;
        top: calc(0px - 100% - 6px);
        width: max-content;
        color: #fff;
        font-size: 12px;
        background: var(--el-color-danger);
        transition: all 0.2s;
        padding: 10px;
        border-radius: 4px;
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        pointer-events: none;

        &:after {
          content: '';
          border: 6px solid transparent;
          border-top-color: var(--el-color-danger);
          position: absolute;
          left: 12px;
          bottom: -11px;
        }
      }

      &:hover > .el-form-item__error {
        opacity: 1;
        visibility: visible;
        pointer-events: all;
      }
    }

    tbody > tr:first-child .el-form-item > .el-form-item__content {
      & > .el-form-item__error {
        bottom: calc(0px - 100% - 6px);
        top: auto;

        &:after {
          top: -11px;
          bottom: auto;
          border-bottom-color: var(--el-color-danger);
          border-top-color: transparent;
        }
      }
    }

    tr > td,
    tr > th {
      padding-left: 4px;
      padding-right: 4px;
      text-align: center;
    }

    .form-table-index {
      position: sticky;
      left: 0;
      z-index: 998;
    }
  }
</style>
