<template>
  <ele-modal
    :width="880"
    title="导入表"
    :body-style="{ padding: '4px 16px' }"
    :destroy-on-close="true"
    :model-value="modelValue"
    @update:modelValue="updateModelValue"
  >
    <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
      <el-row :gutter="8">
        <el-col :lg="6" :md="6" :sm="24" :xs="24">
          <el-form-item label="数据源">
            <el-select
              v-model="form.dataSourceConfigId"
              class="!w-240px"
              placeholder="请选择数据源"
            >
              <el-option
                v-for="config in dataSourceConfigList"
                :key="config.id"
                :label="config.name"
                :value="config.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="6" :sm="24" :xs="24">
          <el-form-item label="表名称">
            <el-input clearable v-model.trim="form.name" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="6" :sm="24" :xs="24">
          <el-form-item label="表描述">
            <el-input
              clearable
              v-model.trim="form.comment"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="6" :sm="24" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="reload">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <ele-pro-table
      ref="tableRef"
      row-key="name"
      :columns="columns"
      :datasource="datasource"
      :show-overflow-tooltip="true"
      v-model:selections="selections"
      highlight-current-row
      :toolbar="false"
      :pagination="{ pageSize: 6, pageSizes: [6, 10, 20, 40, 100] }"
    />
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, watch } from 'vue';
  import { EleMessage } from 'sirius-platform-pro/es';
  import * as CodegenApi from '@/api/infra/codegen';
  import * as DataSourceConfigApi from '@/api/infra/dataSourceConfig';

  const emit = defineEmits(['update:modelValue', 'done']);
  const dataSourceConfigList = ref([]);

  const props = defineProps({
    /** 是否显示 */
    modelValue: Boolean
  });

  /** 提交状态 */
  const loading = ref(false);

  /** 表格实例 */
  const tableRef = ref(null);
  const [form, resetFields] = useFormData({
    name: '',
    comment: '',
    dataSourceConfigId: 0
  });
  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'name',
      label: '表名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'comment',
      label: '表描述',
      align: 'center',
      minWidth: 110
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 表格数据源 */
  const datasource = ({ pages, where }) => {
    return CodegenApi.getSchemaTableList({ ...where, ...pages, ...form });
  };

  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...form } });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  /** 导入 */
  const save = () => {
    if (!selections.value.length) {
      EleMessage.error('请选择要导入的表');
      return;
    }
    loading.value = true;
    const tables = selections.value.map((d) => d.name);
    CodegenApi.createCodegenList({
      tableNames: tables,
      dataSourceConfigId: form.dataSourceConfigId
    })
      .then((msg) => {
        loading.value = false;
        EleMessage.success('导入成功!');
        updateModelValue(false);
        emit('done');
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error('导入失败!');
      });
  };
  /**  重置 */
  const reset = () => {
    resetFields();
    search();
  };
  watch(
    () => props.modelValue,
    async (modelValue) => {
      if (modelValue) {
        const res = await DataSourceConfigApi.getDataSourceConfigList();
        dataSourceConfigList.value = res;
        reload();
      } else {
        selections.value = [];
        resetFields();
      }
    }
  );
</script>
