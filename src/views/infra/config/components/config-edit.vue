<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="460"
    :model-value="modelValue"
    :title="isUpdate ? '修改参数' : '添加参数'"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent=""
    >
      <el-form-item label="参数分类" prop="category">
        <el-input
          clearable
          v-model="form.category"
          placeholder="请输入参数分类"
        />
      </el-form-item>
      <el-form-item label="参数名称" prop="name">
        <el-input clearable v-model="form.name" placeholder="请输入参数名称" />
      </el-form-item>
      <el-form-item label="参数键名" prop="key">
        <el-input clearable v-model="form.key" placeholder="请输入参数键名" />
      </el-form-item>
      <el-form-item label="参数键值" prop="value">
        <el-input clearable v-model="form.value" placeholder="请输入参数键值" />
      </el-form-item>
      {{ form.type }}
      <el-form-item label="是否可见" prop="visible">
        <el-radio-group v-model="form.visible">
          <el-radio
            v-for="dict in getBoolDictOptions(DICT_TYPE.INFRA_BOOLEAN_STRING)"
            :key="dict.value"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注">
        <el-input
          :rows="4"
          type="textarea"
          v-model="form.remark"
          placeholder="请输入备注"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, watch } from 'vue';
  import { EleMessage } from 'sirius-platform-pro/es';
  import { useFormData } from '@/utils/use-form-data';
  import * as ConfigApi from '@/api/system/config';
  import { DICT_TYPE, getBoolDictOptions } from '@/utils/dict';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });
  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗
  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    category: '',
    name: '',
    key: '',
    value: '',
    visible: true,
    remark: ''
  });

  /** 表单验证规则 */
  const rules = reactive({
    category: [
      { required: true, message: '参数分类不能为空', trigger: 'blur' }
    ],
    name: [{ required: true, message: '参数名称不能为空', trigger: 'blur' }],
    key: [{ required: true, message: '参数键名不能为空', trigger: 'blur' }],
    value: [{ required: true, message: '参数键值不能为空', trigger: 'blur' }],
    visible: [{ required: true, message: '是否可见不能为空', trigger: 'blur' }]
  });

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.(async (valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      try {
        if (isUpdate.value) {
          await ConfigApi.updateConfig(form);
          message.success(t('common.updateSuccess'));
        } else {
          await ConfigApi.createConfig(form);
          message.success(t('common.createSuccess'));
        }
        updateModelValue(false);
        emit('done');
      } finally {
        loading.value = false;
      }
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          assignFields(props.data);
          isUpdate.value = true;
        } else {
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>
