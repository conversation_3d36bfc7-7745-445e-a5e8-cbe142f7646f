<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="640"
    :model-value="modelValue"
    :body-style="{ paddingLeft: '8px' }"
    :title="title"
    @update:modelValue="updateModelValue"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent=""
    >
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="任务名称" prop="name">
            <el-input
              clearable
              :maxlength="20"
              v-model="form.name"
              placeholder="请输入任务名称"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="处理器名称" prop="handlerName">
            <el-input
              clearable
              v-model="form.handlerName"
              placeholder="请输入处理器的名称"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="处理器的参数" prop="handlerParam">
        <el-input
          clearable
          :maxlength="2000"
          v-model="form.handlerParam"
          placeholder="请输入处理器的参数"
        />
      </el-form-item>
      <el-form-item label="cron表达式" prop="cronExpression">
        <el-input
          clearable
          :maxlength="200"
          v-model="form.cronExpression"
          placeholder="请输入cron执行表达式"
        >
          <template #append>
            <el-button class="ele-btn-icon" @click="openCron">
              <span>生成表达式</span>
              <el-icon style="margin: -1px -4px 0 4px">
                <ClockCircleOutlined />
              </el-icon>
            </el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item prop="retryCount">
            <template #label>
              <ele-tooltip>
                <el-icon
                  :size="15"
                  style="align-self: center; margin-right: 2px; cursor: help"
                >
                  <QuestionCircleOutlined style="opacity: 0.6" />
                </el-icon>
                <template #content>
                  <div>请输入重试次数。设置为 0 时，不进行重试</div>
                </template>
              </ele-tooltip>
              <span>重试次数</span>
            </template>
            <el-input-number
              clearable
              :min="0"
              class="ele-fluid"
              v-model="form.retryCount"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item prop="retryInterval">
            <template #label>
              <ele-tooltip>
                <el-icon
                  :size="15"
                  style="align-self: center; margin-right: 2px; cursor: help"
                >
                  <QuestionCircleOutlined style="opacity: 0.6" />
                </el-icon>
                <template #content>
                  <div>请输入重试间隔，单位：毫秒。设置为 0 时，无需间隔</div>
                </template>
              </ele-tooltip>
              <span>重试间隔</span>
            </template>
            <el-input-number
              clearable
              :min="0"
              class="ele-fluid"
              v-model="form.retryInterval"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item prop="monitorTimeout">
        <template #label>
          <ele-tooltip>
            <el-icon
              :size="15"
              style="align-self: center; margin-right: 2px; cursor: help"
            >
              <QuestionCircleOutlined style="opacity: 0.6" />
            </el-icon>
            <template #content>
              <div>请输入监控超时时间，单位：毫秒</div>
            </template>
          </ele-tooltip>
          <span>超时时间</span>
        </template>
        <el-input-number
          clearable
          :min="0"
          class="ele-fluid"
          v-model="form.monitorTimeout"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loadding="loadding" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, watch } from 'vue';
  import { EleMessage } from 'sirius-platform-pro/es';
  import {
    QuestionCircleOutlined,
    ClockCircleOutlined
  } from '@/components/icons';
  import { useFormData } from '@/utils/use-form-data';
  import * as JobApi from '@/api/infra/job';

  const emit = defineEmits(['done', 'update:modelValue']);
  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗
  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  /** 是否是修改 */
  const isUpdate = ref(false);
  const title = ref(''); // 弹窗的标题
  /** 提交状态 */
  const loadding = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    name: '',
    handlerName: '',
    handlerParam: '',
    cronExpression: '',
    retryCount: undefined,
    retryInterval: undefined,
    monitorTimeout: undefined
  });

  /** 表单验证规则 */
  const rules = reactive({
    name: [{ required: true, message: '任务名称不能为空', trigger: 'blur' }],
    handlerName: [
      { required: true, message: '处理器的名字不能为空', trigger: 'blur' }
    ],
    cronExpression: [
      { required: true, message: 'CRON 表达式不能为空', trigger: 'blur' }
    ],
    retryCount: [
      { required: true, message: '重试次数不能为空', trigger: 'blur' }
    ],
    retryInterval: [
      { required: true, message: '重试间隔不能为空', trigger: 'blur' }
    ]
  });

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.(async (valid) => {
      if (!valid) {
        return;
      }
      loadding.value = true;
      try {
        if (isUpdate.value) {
          await JobApi.updateJob(form);
          message.success(t('common.updateSuccess'));
        } else {
          await JobApi.createJob(form);
          message.success(t('common.createSuccess'));
        }
        updateModelValue(false);
        emit('done');
      } finally {
        loadding.value = false;
      }
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  /** 打开cron表达式生成 */
  const openCron = () => {
    window.open('https://www.matools.com/app/cron?embed');
  };
  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loadding.value = true;
    try {
      if (props.data) {
        const result = await JobApi.getJob(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loadding.value = false;
    }
  };
</script>

<style lang="scss" scoped>
  @media screen and (max-width: 460px) {
    .policy-radio-group :deep(.el-radio-button__inner) {
      padding-left: 6px;
      padding-right: 6px;
    }
  }
</style>
