<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <job-search @search="reload" />
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        cache-key="monitorJobTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            class="ele-btn-icon"
            :icon="DownloadOutlined"
            @click="exportData"
          >
            导出
          </el-button>
          <el-button
            class="ele-btn-icon"
            :icon="LogOutlined"
            @click="openLog()"
          >
            执行日志
          </el-button>
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.INFRA_JOB_STATUS"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link type="primary" :underline="false" @click="openEdit(row)">
            修改
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" :underline="false" @click="editStatus(row)">
            {{ row.status === InfraJobStatusEnum.STOP ? '开启' : '暂停' }}
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="danger" :underline="false" @click="removeBatch(row)">
            删除
          </el-link>
          <el-divider direction="vertical" />
          <ele-dropdown
            :items="[
              { title: '执行一次', command: 'execute' },
              { title: '查看详情', command: 'detail' },
              { title: '调度日志', command: 'log' }
            ]"
            style="display: inline"
            @command="(key) => dropClick(key, row)"
          >
            <el-link type="primary" :underline="false">
              <span>更多</span>
              <el-icon
                :size="12"
                style="vertical-align: -1px; margin-left: 2px"
              >
                <ArrowDown />
              </el-icon>
            </el-link>
          </ele-dropdown>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <job-edit v-model="showEdit" :data="current" @done="reload" />
    <!-- 详情弹窗 -->
    <job-detail v-model="showInfo" :data="current" />
    <!-- 调度日志弹窗 -->
    <job-log v-model="showLog" :data="current" />
  </ele-page>
</template>

<script setup>
  import { ref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'sirius-platform-pro/es';
  import {
    PlusOutlined,
    DownloadOutlined,
    LogOutlined,
    ArrowDown
  } from '@/components/icons';
  import JobSearch from './components/job-search.vue';
  import JobEdit from './components/job-edit.vue';
  import JobDetail from './components/job-detail.vue';
  import JobLog from './components/job-log.vue';
  import * as JobApi from '@/api/infra/job';
  import { InfraJobStatusEnum } from '@/utils/constants';
  import { DICT_TYPE } from '@/utils/dict';

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗
  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'name',
      label: '任务名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'status',
      label: '任务状态',
      width: 80,
      align: 'center',
      slot: 'status'
    },
    {
      prop: 'handlerName',
      label: '处理器名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'handlerParam',
      label: '处理器的参数',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'cronExpression',
      label: 'cron执行表达式',
      align: 'center',
      minWidth: 140
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 220,
      align: 'center',
      slot: 'action',
      hideInPrint: true
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 是否显示查看弹窗 */
  const showInfo = ref(false);

  /** 是否显示调度日志弹窗 */
  const showLog = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where }) => {
    return JobApi.getJobPage({ ...where, ...pages });
  };

  /** 搜索 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 详情 */
  const openDetail = (row) => {
    current.value = row;
    showInfo.value = true;
  };

  /** 打开调度日志弹窗 */
  const openLog = (row) => {
    current.value = row ?? null;
    showLog.value = true;
  };

  /** 批量删除 */
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await JobApi.deleteJob(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };

  /** 修改状态 */
  const editStatus = async (row) => {
    try {
      // 修改状态的二次确认
      const text = row.status === InfraJobStatusEnum.STOP ? '开启' : '关闭';
      await message.confirm(
        '确认要' + text + '此定时任务吗?',
        t('common.reminder')
      );
      const status =
        row.status === InfraJobStatusEnum.STOP
          ? InfraJobStatusEnum.NORMAL
          : InfraJobStatusEnum.STOP;
      await JobApi.updateJobStatus(row.id, status);
      message.success(text + '成功');
      // 刷新列表
      await getList();
    } catch {}
  };

  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data = await JobApi.exportJob(queryParams);
      download.excel(data, '定时任务.xls');
    } catch {
    } finally {
      exportLoading.value = false;
    }
  };

  /** 下拉菜单点击事件 */
  const dropClick = (key, row) => {
    if (key === 'execute') {
      ElMessageBox.confirm(`确认要立即执行一次任务吗?`, '系统提示', {
        type: 'warning',
        draggable: true
      })
        .then(() => {
          const loading = EleMessage.loading('请求中..');
          JobApi.runJob(row.id)
            .then(() => {
              loading.close();
              EleMessage.success('执行成功');
              reload();
            })
            .catch((e) => {
              loading.close();
              EleMessage.error(e.message);
            });
        })
        .catch(() => {});
    } else if (key === 'detail') {
      openDetail(row);
    } else if (key === 'log') {
      openLog(row);
    }
  };
</script>

<script>
  export default {
    name: 'MonitorJob'
  };
</script>
