<template>
  <el-form
    ref="formLogin"
    size="large"
    :model="loginData.loginForm"
    :rules="LoginRules"
    class="login-form"
  >
    <el-form-item
      prop="tenantName"
      v-if="loginData.tenantEnable === 'true'"
      class="form-field"
    >
      <el-input
        clearable
        v-model="loginData.loginForm.tenantName"
        placeholder="请输入租户名称"
        :prefix-icon="House"
        class="form-input"
      />
    </el-form-item>

    <el-form-item prop="username" class="form-field">
      <el-input
        clearable
        v-model="loginData.loginForm.username"
        placeholder="请输入用户名"
        :prefix-icon="User"
        class="form-input"
      />
    </el-form-item>

    <el-form-item prop="password" class="form-field">
      <el-input
        show-password
        v-model="loginData.loginForm.password"
        placeholder="请输入密码"
        :prefix-icon="Lock"
        class="form-input"
      />
    </el-form-item>

    <div class="form-extras">
      <el-checkbox
        v-model="loginData.loginForm.rememberMe"
        class="remember-checkbox"
      >
        <span class="checkbox-text">记住密码</span>
      </el-checkbox>
      <a href="#" class="forgot-link" @click.prevent="$emit('showReset')">
        忘记密码?
      </a>
    </div>

    <el-form-item class="form-submit">
      <el-button
        type="primary"
        :loading="loginLoading"
        @click="getCode"
        class="login-button"
      >
        <span v-if="!loginLoading">登录</span>
        <span v-else>登录中...</span>
      </el-button>
    </el-form-item>
    <Verify
      ref="verify"
      :captchaType="captchaType"
      :imgSize="{ width: '400px', height: '200px' }"
      mode="pop"
      @success="getSmsCode"
    />
  </el-form>
</template>

<script setup lang="ts">
  import { ref, reactive, unref } from 'vue';
  import { useRouter } from 'vue-router';
  import { House, User, Lock } from '@element-plus/icons-vue';
  import { usePageTab } from '@/utils/use-page-tab';
  import { login, getTenantIdByName } from '@/api/login';
  import * as authUtil from '@/utils/auth';
  import { useFormValid } from '../useLogin';
  import { Verify } from '@/components/Verifition';

  const captchaType = ref('blockPuzzle'); // blockPuzzle 滑块 clickWord 点击文字
  const loginData = reactive({
    isShowPassword: false,
    captchaEnable: import.meta.env.VITE_APP_CAPTCHA_ENABLE,
    tenantEnable: import.meta.env.VITE_APP_TENANT_ENABLE,
    loginForm: {
      tenantName: import.meta.env.VITE_APP_DEFAULT_LOGIN_TENANT || '',
      username: '',
      password: '',
      captchaVerification: '',
      rememberMe: true
    }
  });
  const getLoginFormCache = () => {
    const loginForm = authUtil.getLoginForm();
    if (loginForm) {
      loginData.loginForm = {
        ...loginData.loginForm,
        username: loginForm.username
          ? loginForm.username
          : loginData.loginForm.username,
        password: loginForm.password
          ? loginForm.password
          : loginData.loginForm.password,
        rememberMe: loginForm.rememberMe,
        tenantName: loginForm.tenantName
          ? loginForm.tenantName
          : loginData.loginForm.tenantName
      };
    }
  };

  // 登录表单验证规则
  const LoginRules = {
    tenantName: [
      { required: true, message: '请输入租户名称', trigger: 'blur' }
    ],
    username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
    ]
  };

  // Emits
  const emit = defineEmits(['showReset', 'showVerify']);

  // Router and utils
  const { currentRoute } = useRouter();
  const { goHomeRoute, cleanPageTabs } = usePageTab();

  // Refs
  const formLogin = ref();
  const verify = ref();
  const loginLoading = ref(false);

  // Form validation
  const { validForm } = useFormValid(formLogin);

  // Methods
  const getTenantId = async () => {
    if (loginData.tenantEnable === 'true') {
      const res = await getTenantIdByName(loginData.loginForm.tenantName);
      authUtil.setTenantId(res);
    }
  };

  const handleLogin = async (params) => {
    loginLoading.value = true;
    try {
      await getTenantId();
      const data = await validForm();
      if (!data) {
        return;
      }
      const loginDataLoginForm = { ...loginData.loginForm };
      loginDataLoginForm.captchaVerification = params.captchaVerification;
      const res = await login(loginDataLoginForm);
      if (!res) {
        return;
      }
      if (loginDataLoginForm.rememberMe) {
        authUtil.setLoginForm(loginDataLoginForm);
      } else {
        authUtil.removeLoginForm();
      }
      authUtil.setToken(res);
      //cleanPageTabs();
      goHome();
    } finally {
      loginLoading.value = false;
    }
  };

  const goHome = () => {
    const { query } = unref(currentRoute);
    goHomeRoute(query.from);
  };

  const getCode = async () => {
    // 情况一，未开启：则直接登录
    if (loginData.captchaEnable === 'false') {
      await handleLogin({});
    } else {
      // 情况二，已开启：则展示验证码；只有完成验证码的情况，才进行登录
      verify.value.show();
    }
  };

  // 验证码成功回调
  const getSmsCode = async (params) => {
    await handleLogin(params);
  };

  onMounted(() => {
    getLoginFormCache();
  });
  // Expose form ref and methods for parent component
  defineExpose({
    formLogin,
    verify,
    handleLogin,
    getSmsCode,
    loginLoading
  });
</script>

<style lang="scss" scoped>
  .login-form {
    width: 100%;
    max-width: 380px;

    .form-field {
      margin-bottom: 24px;

      .form-input {
        :deep(.el-input__wrapper) {
          height: 48px;
          border-radius: 8px;
          border: 1px solid #e2e8f0;
          background: #f8fafc;
          box-shadow: none;
          transition: all 0.3s ease;
          padding: 0 16px;

          &:hover {
            border-color: #cbd5e0;
            background: #ffffff;
          }

          &.is-focus {
            border-color: #3b82f6;
            background: #ffffff;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }
        }

        :deep(.el-input__inner) {
          height: 46px;
          font-size: 15px;
          color: #2d3748;
          font-weight: 400;
          line-height: 46px;

          &::placeholder {
            color: #a0aec0;
            font-weight: 400;
          }
        }

        :deep(.el-input__prefix) {
          left: 16px;

          .el-input__prefix-inner {
            .el-icon {
              color: #9ca3af;
              font-size: 18px;
            }
          }
        }

        :deep(.el-input__suffix) {
          right: 16px;
        }
      }
    }

    .form-extras {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 32px;
      font-size: 14px;

      .remember-checkbox {
        :deep(.el-checkbox__label) {
          font-size: 14px;
          color: #6b7280;
        }

        .checkbox-text {
          color: #6b7280;
        }
      }

      .forgot-link {
        color: #3b82f6;
        text-decoration: none;
        font-size: 14px;
        transition: color 0.3s ease;

        &:hover {
          color: #1d4ed8;
          text-decoration: underline;
        }
      }
    }

    .form-submit {
      margin-bottom: 32px;

      .login-button {
        width: 100%;
        height: 48px;
        border-radius: 8px;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        border: none;
        font-size: 16px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);

        &:hover:not(:disabled) {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        &:active:not(:disabled) {
          transform: translateY(0px);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }
      }
    }
  }
</style>
