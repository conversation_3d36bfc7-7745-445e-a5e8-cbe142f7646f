<template>
  <el-form
    ref="formReset"
    size="large"
    :model="resetPasswordData"
    :rules="ResetRules"
    class="login-form"
  >
    <el-form-item prop="tenantName" class="form-field">
      <el-input
        v-model="resetPasswordData.tenantName"
        v-if="resetPasswordData.tenantEnable === 'true'"
        placeholder="请输入租户"
        :prefix-icon="House"
        class="form-input"
      />
    </el-form-item>

    <el-form-item prop="mobile" class="form-field">
      <el-input
        v-model="resetPasswordData.mobile"
        placeholder="请输入手机号"
        :prefix-icon="User"
        class="form-input"
      />
    </el-form-item>

    <el-form-item prop="code" class="form-field">
      <div class="code-input-group">
        <el-input
          v-model="resetPasswordData.code"
          placeholder="请输入验证码"
          clearable
          class="form-input code-input"
        />
        <el-button
          class="code-button"
          :disabled="mobileCodeTimer > 0"
          @click="getCode"
        >
          {{ mobileCodeTimer > 0 ? `${mobileCodeTimer}s` : '获取验证码' }}
        </el-button>
      </div>
    </el-form-item>

    <el-form-item prop="password" class="form-field">
      <el-input
        v-model="resetPasswordData.password"
        type="password"
        show-password
        placeholder="请输入新密码"
        :prefix-icon="Lock"
        class="form-input"
      />
    </el-form-item>

    <el-form-item prop="check_password" class="form-field">
      <el-input
        v-model="resetPasswordData.check_password"
        type="password"
        show-password
        placeholder="请确认新密码"
        :prefix-icon="Lock"
        class="form-input"
      />
    </el-form-item>

    <el-form-item class="form-submit">
      <el-button
        type="primary"
        :loading="resetLoading"
        class="login-button"
        @click="resetPassword"
      >
        重置密码
      </el-button>
    </el-form-item>

    <div class="form-extras">
      <a href="#" class="forgot-link" @click.prevent="$emit('showLogin')">
        返回登录
      </a>
    </div>

    <Verify
      ref="verify"
      :captchaType="captchaType"
      :imgSize="{ width: '400px', height: '200px' }"
      mode="pop"
      @success="getSmsCode"
    />
  </el-form>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { House, User, Lock } from '@element-plus/icons-vue';
  import { getTenantIdByName } from '@/api/login';
  import { useFormValid } from '../useLogin';
  import { Verify } from '@/components/Verifition';
  import { sendSmsCode, smsResetPassword } from '@/api/login';
  import { useMessage } from '@/hooks/web/useMessage';
  import * as authUtil from '@/utils/auth';
  import { ElLoading } from 'element-plus';

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗
  const captchaType = ref('blockPuzzle'); // blockPuzzle 滑块 clickWord 点击文字
  const mobileCodeTimer = ref(0);
  const loginLoading = ref(false);
  // 重置密码表单数据
  const resetPasswordData = reactive({
    captchaEnable: import.meta.env.VITE_APP_CAPTCHA_ENABLE,
    tenantEnable: import.meta.env.VITE_APP_TENANT_ENABLE,
    tenantName: import.meta.env.VITE_APP_DEFAULT_LOGIN_TENANT || '',
    username: '',
    password: '',
    check_password: '',
    mobile: '',
    code: ''
  });
  // 重置密码表单验证规则
  const ResetRules = {
    tenantName: [
      { required: true, message: '请输入租户名称', trigger: 'blur' }
    ],
    mobile: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: '请输入正确的手机号',
        trigger: 'blur'
      }
    ],
    code: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
    password: [
      { required: true, message: '请输入新密码', trigger: 'blur' },
      { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
    ],
    check_password: [
      { required: true, message: '请确认新密码', trigger: 'blur' },
      {
        validator: (_rule, value, callback) => {
          if (value === '') {
            callback(new Error('请再次输入密码'));
          } else if (value !== resetPasswordData.password) {
            callback(new Error('两次输入密码不一致!'));
          } else {
            callback();
          }
        },
        trigger: 'blur'
      }
    ]
  };

  // Emits
  const emit = defineEmits(['resetSuccess']);

  // Refs
  const formReset = ref();
  const verify = ref();
  const resetLoading = ref(false);

  // Form validation
  const { validForm } = useFormValid(formReset);

  // Methods
  const getTenantId = async () => {
    if (resetPasswordData.tenantEnable === 'true') {
      const res = await getTenantIdByName(resetPasswordData.tenantName);
      if (res == null) {
        message.error(t('login.invalidTenantName'));
        throw t('login.invalidTenantName');
      }
      authUtil.setTenantId(res);
    }
  };
  // 获取验证码
  const getCode = async () => {
    // 情况一，未开启：则直接发送验证码
    if (resetPasswordData.captchaEnable === 'false') {
      await getSmsCode({});
    } else {
      // 情况二，已开启：则展示验证码；只有完成验证码的情况，才进行发送验证码
      // 弹出验证码
      verify.value.show();
    }
  };
  const smsVO = reactive({
    tenantName: '',
    mobile: '',
    captchaVerification: '',
    scene: 23
  });
  // 发送验证码
  const getSmsCode = async (params) => {
    if (resetPasswordData.tenantEnable === 'true') {
      await getTenantId();
    }
    smsVO.captchaVerification = params.captchaVerification;
    smsVO.mobile = resetPasswordData.mobile;
    await sendSmsCode(smsVO).then(async () => {
      message.success(t('login.SmsSendMsg'));
      // 设置倒计时
      mobileCodeTimer.value = 60;
      let msgTimer = setInterval(() => {
        mobileCodeTimer.value = mobileCodeTimer.value - 1;
        if (mobileCodeTimer.value <= 0) {
          clearInterval(msgTimer);
        }
      }, 1000);
    });
  };

  // 重置密码
  const resetPassword = async () => {
    const data = await validForm();
    if (!data) return;
    await getTenantId();
    loginLoading.value = true;
    await smsResetPassword(resetPasswordData)
      .then(async () => {
        message.success(t('login.resetPasswordSuccess'));
        emit('resetSuccess');
      })
      .catch(() => {})
      .finally(() => {
        loginLoading.value = false;
        setTimeout(() => {
          const loadingInstance = ElLoading.service();
          loadingInstance.close();
        }, 400);
      });
  };
</script>

<style lang="scss" scoped>
  .login-form {
    width: 100%;
    max-width: 380px;

    .form-field {
      margin-bottom: 24px;

      .form-input {
        :deep(.el-input__wrapper) {
          height: 48px;
          border-radius: 8px;
          border: 1px solid #e2e8f0;
          background: #f8fafc;
          box-shadow: none;
          transition: all 0.3s ease;
          padding: 0 16px;

          &:hover {
            border-color: #cbd5e0;
            background: #ffffff;
          }

          &.is-focus {
            border-color: #3b82f6;
            background: #ffffff;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }
        }

        :deep(.el-input__inner) {
          height: 46px;
          font-size: 15px;
          color: #2d3748;
          font-weight: 400;
          line-height: 46px;

          &::placeholder {
            color: #a0aec0;
            font-weight: 400;
          }
        }

        :deep(.el-input__prefix) {
          left: 16px;

          .el-input__prefix-inner {
            .el-icon {
              color: #9ca3af;
              font-size: 18px;
            }
          }
        }

        :deep(.el-input__suffix) {
          right: 16px;
        }
      }

      // 验证码输入组
      .code-input-group {
        display: flex;
        gap: 12px;
        width: 100%;

        .code-input {
          flex: 1;

          :deep(.el-input__wrapper) {
            height: 48px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            background: #f8fafc;
            box-shadow: none;
            transition: all 0.3s ease;
            padding: 0 16px;

            &:hover {
              border-color: #cbd5e0;
              background: #ffffff;
            }

            &.is-focus {
              border-color: #3b82f6;
              background: #ffffff;
              box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }
          }

          :deep(.el-input__inner) {
            height: 46px;
            font-size: 15px;
            color: #2d3748;
            font-weight: 400;
            line-height: 46px;

            &::placeholder {
              color: #a0aec0;
              font-weight: 400;
            }
          }
        }

        .code-button {
          width: 100px;
          height: 48px;
          border-radius: 8px;
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          border: none;
          color: white;
          font-size: 13px;
          font-weight: 500;
          transition: all 0.3s ease;
          flex-shrink: 0;

          &:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(59, 130, 246, 0.3);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
          }
        }
      }
    }

    .form-extras {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 16px;
      font-size: 14px;

      .forgot-link {
        color: #3b82f6;
        text-decoration: none;
        font-size: 14px;
        transition: color 0.3s ease;

        &:hover {
          color: #1d4ed8;
          text-decoration: underline;
        }
      }
    }

    .form-submit {
      margin-bottom: 32px;

      .login-button {
        width: 100%;
        height: 48px;
        border-radius: 8px;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        border: none;
        font-size: 16px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);

        &:hover:not(:disabled) {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        &:active:not(:disabled) {
          transform: translateY(0px);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }
      }
    }
  }
</style>
