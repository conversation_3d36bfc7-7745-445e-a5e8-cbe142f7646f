<template>
  <div class="login-container">
    <!-- 左侧展示区 -->
    <div class="left-section">
      <!-- 品牌标识 -->
      <div class="brand-header">
        <div class="brand-logo">
          <img src="@/assets/logo-1.png" alt="Logo" />
        </div>
        <div class="brand-info">
          <h1 class="brand-title">{{ appName }}</h1>
          <p class="brand-subtitle">{{ companyName }} </p>
        </div>
      </div>

      <!-- 3D插图 -->
      <div class="illustration-container">
        <img
          src="@/assets/svgs/login-box-bg.svg"
          alt="3D插图"
          class="main-illustration"
        />
      </div>

      <!-- 波浪分割线 -->
      <div class="wave-separator">
        <svg viewBox="0 0 250 800" preserveAspectRatio="none">
          <path
            d="M0,0 C150,150 150,650 0,800 L250,800 L250,0 Z"
            fill="white"
          />
        </svg>
      </div>
    </div>

    <!-- 右侧登录区 -->
    <div class="right-section">
      <!-- 登录标题 -->
      <div class="login-header">
        <h2 class="login-title">{{
          isResetMode ? '重置密码' : '欢迎登录系统'
        }}</h2>
        <div class="title-underline"></div>
      </div>

      <!-- 登录表单 -->
      <LoginForm
        v-if="!isResetMode"
        ref="loginFormRef"
        @show-reset="showResetForm"
      />

      <!-- 重置密码表单 -->
      <ResetPasswordForm
        v-if="isResetMode"
        ref="resetFormRef"
        @show-login="showLoginForm"
        @reset-success="handleResetSuccess"
      />

      <!-- 底部备案信息 -->
      <div class="footer-info">
        <p class="company-name">
          <img src="@/assets/batb.png" alt="公安备案" class="beian-icon" />
          <a
            href="http://www.beian.gov.cn/portal/registerSystemInfo"
            target="_blank"
            class="beian-link"
          >
            {{ beianNumber }}
          </a>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, unref, onMounted, onUnmounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { ElMessage } from 'element-plus';
  import {
    House,
    User,
    Lock,
    ArrowDown,
    ChatDotRound
  } from '@element-plus/icons-vue';
  import { usePageTab } from '@/utils/use-page-tab';
  import * as authUtil from '@/utils/auth';
  import {
    PROJECT_NAME,
    PROJECT_COMPANY,
    BEIAN_NUMBER
  } from '@/config/setting';
  import { LoginForm, ResetPasswordForm } from './components';

  const loginFormRef = ref();
  const resetFormRef = ref();

  // 表单模式控制
  const isResetMode = ref(false);

  // 应用名称
  const appName = PROJECT_NAME;
  const companyName = PROJECT_COMPANY;
  const beianNumber = BEIAN_NUMBER;

  // ResetRules 已移到 ResetPasswordForm 组件中

  // 显示重置密码表单
  const showResetForm = () => {
    isResetMode.value = true;
  };

  // 显示登录表单
  const showLoginForm = () => {
    isResetMode.value = false;
    if (resetFormRef.value) {
      resetFormRef.value.clearForm();
    }
  };

  // 处理重置密码成功
  const handleResetSuccess = () => {
    showLoginForm();
  };
</script>

<style lang="scss" scoped>
  // 防止页面滚动的全局样式
  :global(html, body) {
    margin: 0;
    padding: 0;
    height: 100%;
    overflow: hidden;
    position: fixed;
    width: 100%;
  }

  :global(#app) {
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
  }

  // 全局变量
  :root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --text-primary: #1a202c;
    --text-secondary: #4a5568;
    --text-light: rgba(255, 255, 255, 0.9);
  }

  // 主容器
  .login-container {
    height: 100vh;
    width: 100vw;
    display: flex;
    overflow: hidden;
    font-family:
      'Inter',
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      sans-serif;
    margin: 0;
    padding: 0;
  }

  // 左侧展示区
  .left-section {
    flex: 1;
    background: linear-gradient(135deg, #8b9cf7 0%, #9bb5ff 100%);
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 60px 60px 60px 80px;
    overflow: hidden;

    // 品牌标识
    .brand-header {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 60px;

      .brand-logo {
        width: 65px;
        height: 65px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);

        img {
          width: 60px;
          height: 60px;
        }
      }

      .brand-info {
        .brand-title {
          font-size: 24px;
          font-weight: 700;
          color: white;
          margin: 0 0 4px 0;
          line-height: 1.2;
        }

        .brand-subtitle {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.7);
          margin: 0;
          font-weight: 400;
        }
      }
    }

    // 3D插图容器
    .illustration-container {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 2;

      .main-illustration {
        max-width: 85%;
        max-height: 350px;
        height: auto;
        filter: drop-shadow(0 15px 35px rgba(0, 0, 0, 0.12));
        transform: translateX(-20px);
      }
    }

    // 优雅分割线
    .wave-separator {
      position: absolute;
      top: 0;
      right: -80px;
      width: 250px;
      height: 100%;
      z-index: 1;
      pointer-events: none;

      svg {
        width: 100%;
        height: 100%;
      }

      path {
        fill: white;
      }
    }
  }

  // 右侧登录区
  .right-section {
    flex: 1;
    background: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 80px 60px;
    position: relative;

    // 登录标题
    .login-header {
      text-align: center;
      margin-bottom: 48px;

      .login-title {
        font-size: 28px;
        font-weight: 600;
        color: #1a202c;
        margin: 0 0 12px 0;
        line-height: 1.2;
      }

      .title-underline {
        width: 60px;
        height: 3px;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        border-radius: 2px;
        margin: 0 auto;
      }
    }

    .login-form {
      width: 100%;
      max-width: 380px;
    }

    // 底部公司信息
    .footer-info {
      position: absolute;
      bottom: 40px;
      left: 50%;
      transform: translateX(-50%);
      text-align: center;

      .company-name {
        font-size: 13px;
        color: #9ca3af;
        margin: 0;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        flex-wrap: wrap;

        .beian-icon {
          width: 14px;
          height: 14px;
          vertical-align: middle;
        }

        .beian-link {
          color: #9ca3af;
          text-decoration: none;
          transition: color 0.3s ease;

          &:hover {
            color: #6b7280;
            text-decoration: underline;
          }
        }
      }
    }
  }

  // 登录表单样式
  .login-form {
    .form-field {
      margin-bottom: 24px;

      // 验证码输入组
      .code-input-group {
        display: flex;
        gap: 12px;
        width: 100%;

        .code-input {
          flex: 1;

          :deep(.el-input__wrapper) {
            height: 48px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            background: #f8fafc;
            box-shadow: none;
            transition: all 0.3s ease;
            padding: 0 16px;

            &:hover {
              border-color: #cbd5e0;
              background: #ffffff;
            }

            &.is-focus {
              border-color: #3b82f6;
              background: #ffffff;
              box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }
          }

          :deep(.el-input__inner) {
            height: 46px;
            font-size: 15px;
            color: #2d3748;
            font-weight: 400;
            line-height: 46px;

            &::placeholder {
              color: #a0aec0;
              font-weight: 400;
            }
          }
        }

        .code-button {
          width: 100px;
          height: 48px;
          border-radius: 8px;
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          border: none;
          color: white;
          font-size: 13px;
          font-weight: 500;
          transition: all 0.3s ease;
          flex-shrink: 0;

          &:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(59, 130, 246, 0.3);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
          }
        }
      }

      .form-input {
        :deep(.el-input__wrapper) {
          height: 48px;
          border-radius: 8px;
          border: 1px solid #e2e8f0;
          background: #f8fafc;
          box-shadow: none;
          transition: all 0.3s ease;
          padding: 0 16px;

          &:hover {
            border-color: #cbd5e0;
            background: #ffffff;
          }

          &.is-focus {
            border-color: #3b82f6;
            background: #ffffff;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }
        }

        :deep(.el-input__inner) {
          height: 46px;
          font-size: 15px;
          color: #2d3748;
          font-weight: 400;
          line-height: 46px;

          &::placeholder {
            color: #a0aec0;
            font-weight: 400;
          }
        }

        :deep(.el-input__prefix) {
          left: 16px;

          .el-input__prefix-inner {
            .el-icon {
              color: #9ca3af;
              font-size: 18px;
            }
          }
        }

        :deep(.el-input__suffix) {
          right: 16px;
        }
      }
    }

    .form-extras {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 32px;

      .remember-checkbox {
        :deep(.el-checkbox__label) {
          .checkbox-text {
            color: #4a5568;
            font-size: 14px;
            font-weight: 400;
          }
        }

        :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
          background: #667eea;
          border-color: #667eea;
        }

        :deep(.el-checkbox__inner) {
          border-radius: 4px;
          border-width: 1px;
          border-color: #d1d5db;

          &:hover {
            border-color: #667eea;
          }
        }
      }

      .forgot-link {
        color: #667eea;
        text-decoration: none;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s ease;

        &:hover {
          color: #5a67d8;
          text-decoration: underline;
        }
      }
    }

    .form-submit {
      margin-bottom: 32px;

      .login-button {
        width: 100%;
        height: 48px;
        border-radius: 8px;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        border: none;
        font-size: 16px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);

        &:hover:not(:disabled) {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        &:active:not(:disabled) {
          transform: translateY(0px);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }
      }
    }
  }

  // 第三方登录
  .third-party-login {
    text-align: center;
    margin-top: 24px;

    .divider {
      font-size: 12px;
      color: #9ca3af;
      margin-bottom: 16px;
      position: relative;

      &::before,
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        width: 60px;
        height: 1px;
        background: #e5e7eb;
      }

      &::before {
        left: 0;
      }

      &::after {
        right: 0;
      }
    }

    .social-buttons {
      display: flex;
      justify-content: center;

      .wechat-btn {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: #07c160;
        border: none;
        color: white;
        transition: all 0.3s ease;

        &:hover {
          background: #06ad56;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(7, 193, 96, 0.3);
        }

        .el-icon {
          font-size: 14px;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .login-container {
      flex-direction: column;
    }

    .left-section {
      flex: none;
      height: 35vh;
      padding: 30px 40px;

      .brand-header {
        margin-bottom: 30px;

        .brand-info {
          .brand-title {
            font-size: 20px;
          }

          .brand-subtitle {
            font-size: 12px;
          }
        }
      }

      .illustration-container {
        display: none;
      }

      .wave-separator {
        display: none;
      }
    }

    .right-section {
      flex: none;
      height: 65vh;
      padding: 40px 30px;

      .login-header {
        margin-bottom: 32px;

        .login-title {
          font-size: 24px;
        }
      }

      .footer-info {
        bottom: 20px;
      }
    }
  }
</style>
