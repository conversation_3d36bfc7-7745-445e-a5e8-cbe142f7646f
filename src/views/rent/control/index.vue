<template>
  <ele-page class="rent-control-page" flex-table>
    <!-- 楼栋总览列表 -->
    <ele-card flex-table>
      <!-- 头部工具栏 -->
      <ele-toolbar title="楼栋列表" :title-props="{ size: 'md' }">
        <template #tools>
          <div style="display: flex; align-items: center">
            <div style="width: 200px">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索楼栋..."
                :prefix-icon="Search"
                clearable
              >
                <template #append>
                  <el-button
                    :icon="Search"
                    @click="getRentDashboardPage"
                    class="ele-btn-icon"
                  />
                </template>
              </el-input>
            </div>
          </div>
        </template>
      </ele-toolbar>

      <!-- 数据列表 -->
      <div
        style="flex: 1; display: flex; flex-direction: column; overflow: hidden"
      >
        <ele-loading
          :loading="loading"
          :spinner-style="{ background: 'none' }"
          style="flex: 1; overflow-y: auto; min-height: 0"
        >
          <div v-for="building in buildings" :key="building.id">
            <div class="list-item">
              <div class="list-item-avatar">
                <el-image
                  :src="getBuildingImage(building)"
                  fit="cover"
                  style="
                    width: 60px;
                    height: 60px;
                    border-radius: 6px;
                    cursor: pointer;
                  "
                  :preview-src-list="[getBuildingImage(building)]"
                  :initial-index="0"
                  preview-teleported
                />
                <div class="list-item-avatar-extra">
                  <div style="margin-bottom: 2px">{{
                    building.roomNo || building.name
                  }}</div>
                  <ele-text size="sm" type="placeholder">
                    {{ building.roomNum || building.address }}
                  </ele-text>
                </div>
              </div>
              <div class="list-item-floors">
                <div class="list-item-title">楼层数</div>
                <ele-text size="sm" type="placeholder"
                  >{{
                    building.totalFloors || building.floorCount || 0
                  }}层</ele-text
                >
              </div>
              <div class="list-item-rooms">
                <div class="list-item-title">房间数</div>
                <ele-text size="sm" type="placeholder"
                  >{{
                    building.totalRooms || building.roomCount || 0
                  }}间</ele-text
                >
              </div>
              <div class="list-item-progress" style="display: block">
                <el-progress
                  :percentage="building.occupancyRate || building.rentRate || 0"
                  :color="
                    getProgressColor(
                      building.occupancyRate || building.rentRate || 0
                    )
                  "
                  :stroke-width="8"
                />
              </div>
              <div class="list-item-tools">
                <el-link
                  type="primary"
                  underline="never"
                  @click.stop="openBuildingDrawer(building)"
                >
                  查看详情
                </el-link>
              </div>
            </div>
            <el-divider style="margin: 0; opacity: 0.6" />
          </div>
        </ele-loading>

        <div style="flex-shrink: 0">
          <ele-pagination
            :total="count"
            v-model:page-size="limit"
            v-model:current-page="page"
            :pageSizes="[8, 20, 100]"
            layout="prev, pager, next, sizes, jumper"
            small="true"
            style="justify-content: center"
            @current-change="getRentDashboardPage"
            @size-change="getRentDashboardPage"
          />
        </div>
      </div>
    </ele-card>

    <!-- 楼层详情抽屉 -->
    <el-drawer
      v-model="drawerVisible"
      :title="`${selectedBuilding?.name || ''} - 楼层详情`"
      direction="rtl"
      size="80%"
      class="building-drawer"
    >
      <div v-if="selectedBuilding" class="drawer-content">
        <div class="building-info">
          <div class="info-header">
            <img
              :src="selectedBuilding.image"
              :alt="selectedBuilding.name"
              class="building-image-large"
            />
            <div class="info-details">
              <div class="title-section">
                <div class="building-title">
                  <h2>{{ selectedBuilding.name }}</h2>
                  <div class="address">{{ selectedBuilding.address }}</div>
                </div>
              </div>
              <div class="stats">
                <div class="stat-item">
                  <span class="label">总楼层</span>
                  <span class="value"
                    >{{ selectedBuilding.totalFloors }}层</span
                  >
                </div>
                <div class="stat-item">
                  <span class="label">总房间</span>
                  <span class="value">{{ selectedBuilding.totalRooms }}间</span>
                </div>
                <div class="stat-item">
                  <span class="label">已出租</span>
                  <span class="value"
                    >{{ selectedBuilding.occupiedRooms }}间</span
                  >
                </div>
                <div class="stat-item">
                  <span class="label">出租率</span>
                  <span class="value"
                    >{{ selectedBuilding.occupancyRate }}%</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="floors-layout">
          <div class="floors-main">
            <!-- 左侧楼层列表 -->
            <div class="floors-list">
              <div class="floors-list-header">
                <div class="header-icon">🏢</div>
                <h4>楼层列表</h4>
              </div>
              <div class="floors-list-content">
                <div
                  v-for="floor in selectedBuilding.floors"
                  :key="floor.id"
                  class="floor-item"
                  :class="{ active: selectedFloor?.id === floor.id }"
                  @click="selectFloor(floor)"
                >
                  <div class="floor-avatar"> {{ floor.number }}F </div>
                  <div class="floor-content">
                    <div class="floor-title">{{ floor.number }}F 楼层</div>
                    <div class="floor-desc">
                      共 {{ floor.rooms?.length || 0 }} 间房，出租率
                      {{ getFloorOccupancyRate(floor) }}%
                    </div>
                  </div>
                  <div class="floor-stats">
                    <div
                      class="occupancy-rate"
                      :class="
                        getOccupancyRateClass(getFloorOccupancyRate(floor))
                      "
                    >
                      {{ getFloorOccupancyRate(floor) }}%
                    </div>
                    <div class="room-count">
                      {{ floor.rooms?.length || 0 }} 间房
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 右侧房间信息 -->
            <div class="floor-rooms">
              <div class="floor-rooms-header">
                <div class="header-top">
                  <h4>{{
                    selectedFloor
                      ? `${selectedFloor.number} 房间信息`
                      : '请选择楼层'
                  }}</h4>
                  <div class="layout-legend">
                    <div class="legend-item">
                      <span class="legend-color vacant"></span>
                      <span>待出租</span>
                    </div>
                    <div class="legend-item">
                      <span class="legend-color occupied"></span>
                      <span>出租中</span>
                    </div>
                    <div class="legend-item">
                      <span class="legend-color maintenance"></span>
                      <span>退租中</span>
                    </div>
                  </div>
                </div>
                <div v-if="selectedFloor" class="floor-stats">
                  <span>总房间: {{ selectedFloor.rooms?.length || 0 }}间</span>
                  <span
                    >已出租: {{ getOccupiedRoomsCount(selectedFloor) }}间</span
                  >
                  <span
                    >出租率: {{ getFloorOccupancyRate(selectedFloor) }}%</span
                  >
                </div>
              </div>
              <div class="floor-rooms-content">
                <div
                  v-if="selectedFloor && selectedFloor.rooms?.length"
                  class="rooms-grid"
                >
                  <div
                    v-for="room in selectedFloor.rooms"
                    :key="room.id"
                    class="room-item"
                    :class="getRoomClass(room.status)"
                    @click="handleRoomClick(room)"
                  >
                    {{ room.number }}
                  </div>
                </div>
                <div v-else-if="selectedFloor" class="empty-rooms">
                  <el-empty description="该楼层暂无房间信息" />
                </div>
                <div v-else class="select-floor-tip">
                  <el-empty description="请从左侧选择楼层查看房间信息" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 房间详情弹窗 -->
    <el-dialog
      v-model="roomDialogVisible"
      :title="`房间 ${selectedRoom?.number} 详情`"
      width="400px"
    >
      <div v-if="selectedRoom" class="room-details">
        <div class="detail-row">
          <span class="label">房间号:</span>
          <span class="value">{{ selectedRoom.number }}</span>
        </div>
        <div class="detail-row">
          <span class="label">楼层:</span>
          <span class="value">{{ selectedRoom.floor }}F</span>
        </div>
        <div class="detail-row">
          <span class="label">状态:</span>
          <el-tag :type="getRoomStatusType(selectedRoom.status)">
            {{ getRoomStatusText(selectedRoom.status) }}
          </el-tag>
        </div>
        <div v-if="selectedRoom.tenant" class="detail-row">
          <span class="label">租户:</span>
          <span class="value">{{ selectedRoom.tenant }}</span>
        </div>
        <div v-if="selectedRoom.rentStartDate" class="detail-row">
          <span class="label">租期开始:</span>
          <span class="value">{{ selectedRoom.rentStartDate }}</span>
        </div>
        <div v-if="selectedRoom.rentEndDate" class="detail-row">
          <span class="label">租期结束:</span>
          <span class="value">{{ selectedRoom.rentEndDate }}</span>
        </div>
      </div>

      <template #footer>
        <el-button @click="roomDialogVisible = false">关闭</el-button>
        <el-button v-if="selectedRoom?.status === 'vacant'" type="primary">
          出租管理
        </el-button>
        <el-button v-else type="warning"> 合同管理 </el-button>
      </template>
    </el-dialog>
  </ele-page>
</template>

<script setup>
  import { ref, onMounted, watch } from 'vue';
  import { Search, ArrowDown } from '@element-plus/icons-vue';
  import { ElMessageBox } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus';
  import * as RentControlApi from '@/api/lease/rentcontrol';
  import defaultBuildingImage from '@/assets/imgs/总体.png';
  /** 第几页 */
  const page = ref(1);

  /** 每页多少条 */
  const limit = ref(8);

  /** 总数量 */
  const count = ref(0);

  // 响应式数据
  const searchKeyword = ref('');
  const selectedBuilding = ref(null);
  const loading = ref(false);
  const selectedFloor = ref(null);
  const drawerVisible = ref(false);
  const roomDialogVisible = ref(false);
  const selectedRoom = ref(null);

  //接口查询buildings
  const getRentDashboardPage = async () => {
    try {
      loading.value = true;
      const res = await RentControlApi.getRentDashboardPage({
        pageNo: page.value,
        pageSize: limit.value,
        roomNo: searchKeyword.value
      });
      buildings.value = res.list;
      count.value = res.total;
    } finally {
      loading.value = false;
    }
  };
  //进入页面加载数据
  onMounted(() => {
    getRentDashboardPage();
  });

  // 监听搜索关键词变化
  watch(searchKeyword, () => {
    page.value = 1; // 重置页码
    getRentDashboardPage();
  });

  // 楼栋数据
  const buildings = ref([]);

  // 获取楼栋图片
  function getBuildingImage(building) {
    if (building.atributeVarchar1 && building.atributeVarchar1.trim() !== '') {
      return building.atributeVarchar1;
    }
    // 使用默认图片
    return defaultBuildingImage;
  }

  // 打开楼栋抽屉
  function openBuildingDrawer(building) {
    selectedBuilding.value = building;
    selectedFloor.value = null; // 重置选中的楼层
    drawerVisible.value = true;
  }

  // 选择楼层
  function selectFloor(floor) {
    selectedFloor.value = floor;
  }

  // 处理房间点击
  function handleRoomClick(room) {
    selectedRoom.value = room;
    roomDialogVisible.value = true;
  }

  // 获取楼层出租率
  function getFloorOccupancyRate(floor) {
    if (!floor.rooms || floor.rooms.length === 0) return 0;
    const occupiedCount = floor.rooms.filter(
      (room) => room.status === 'occupied'
    ).length;
    return Math.round((occupiedCount / floor.rooms.length) * 100);
  }

  // 获取出租率颜色类
  function getOccupancyRateClass(rate) {
    if (rate >= 80) return 'high';
    if (rate >= 60) return 'medium';
    return 'low';
  }

  // 获取楼层已出租房间数
  function getOccupiedRoomsCount(floor) {
    if (!floor.rooms) return 0;
    return floor.rooms.filter((room) => room.status === 'occupied').length;
  }

  // 获取房间样式类
  function getRoomClass(status) {
    return {
      'room-vacant': status === 'vacant',
      'room-occupied': status === 'occupied',
      'room-maintenance': status === 'maintenance'
    };
  }

  // 获取进度条颜色
  function getProgressColor(rate) {
    if (rate >= 90) return '#67c23a';
    if (rate >= 80) return '#e6a23c';
    if (rate >= 70) return '#f56c6c';
    return '#909399';
  }

  // 获取房间状态类型
  function getRoomStatusType(status) {
    switch (status) {
      case 'occupied':
        return 'success';
      case 'vacant':
        return 'info';
      case 'maintenance':
        return 'warning';
      default:
        return 'info';
    }
  }

  // 获取房间状态文本
  function getRoomStatusText(status) {
    switch (status) {
      case 'occupied':
        return '已出租';
      case 'vacant':
        return '待出租';
      case 'maintenance':
        return '退租中';
      default:
        return '未知';
    }
  }

  // 下拉菜单点击事件
  function dropClick(key, building) {
    if (key === 'remove') {
      // 删除
      ElMessageBox.confirm(`确定删除"${building.name}"吗?`, '系统提示', {
        type: 'warning',
        draggable: true
      })
        .then(() => {
          EleMessage.success({ message: '点击了删除', plain: true });
        })
        .catch(() => {});
    } else if (key === 'edit') {
      EleMessage.success({ message: '点击了编辑', plain: true });
    }
  }
</script>

<style lang="scss" scoped>
  /* 列表样式 */
  .list-item {
    display: flex;
    align-items: center;
    padding: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 8px;

    &:hover {
      background-color: #f5f7fa;
    }

    .list-item-avatar {
      flex: 1;
      display: flex;
      align-items: center;

      :deep(.el-avatar) {
        flex-shrink: 0;
      }

      .list-item-avatar-extra {
        flex: 1;
        padding-left: 12px;
        box-sizing: border-box;
      }
    }

    & > div + div {
      margin-left: 20px;
      flex-shrink: 0;
    }

    .list-item-floors {
      width: 120px;
    }

    .list-item-rooms {
      width: 120px;
    }

    .list-item-progress {
      width: 180px;
    }

    .list-item-title {
      margin-bottom: 4px;
    }

    .list-item-tools {
      display: flex;
      align-items: center;
    }
  }

  /* 响应式 */
  @media screen and (max-width: 1340px) {
    .list-item {
      & > div + div {
        margin-left: 10px;
      }

      .list-item-floors {
        width: 70px;
      }

      .list-item-rooms {
        width: 70px;
      }

      .list-item-progress {
        width: 100px;
      }
    }
  }

  @media screen and (max-width: 1100px) {
    .list-item {
      display: block;

      .list-item-floors,
      .list-item-rooms,
      .list-item-progress {
        width: 100%;
        margin: 8px 0 0 0;
        display: flex;
        align-items: center;
      }

      .list-item-title {
        margin: 0;
        width: 80px;
      }

      .list-item-tools {
        margin-top: 8px;
        justify-content: flex-end;
      }
    }
  }

  // 全局动画
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes pulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.02);
    }
  }

  .rent-control-page {
    height: 100vh;

    :deep(.ele-card) {
      display: flex;
      flex-direction: column;
      height: 100%;

      .ele-card__body {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }
    }

    // 抽屉样式
    :deep(.building-drawer) {
      .el-drawer {
        border-radius: 12px 0 0 12px;
        overflow: hidden;
      }

      .el-drawer__header {
        padding: 16px 20px;
        background: white;
        color: #2d3748;
        border-bottom: 1px solid #e2e8f0;
        margin-bottom: 0;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 20px;
          right: 20px;
          height: 2px;
          background: linear-gradient(90deg, #e2e8f0 0%, #cbd5e0 100%);
        }

        .el-drawer__title {
          color: #2d3748;
          font-weight: 600;
          font-size: 18px;
        }

        .el-drawer__close-btn {
          color: #718096;
          font-size: 18px;

          &:hover {
            color: #4a5568;
          }
        }
      }

      .el-drawer__body {
        padding: 0;
        background: #f8fafc;
      }
    }

    .drawer-content {
      height: 100%;
      display: flex;
      flex-direction: column;

      .building-info {
        padding: 20px 24px 16px 24px;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-bottom: none;
        position: relative;
        overflow: hidden;
        border-radius: 16px 16px 0 0;
        margin: 0 -1px;
        box-shadow:
          0 -2px 8px rgba(0, 0, 0, 0.02),
          0 1px 0 rgba(255, 255, 255, 0.8);

        // 背景装饰 - 更加柔和
        &::before {
          content: '';
          position: absolute;
          top: -30%;
          right: -15%;
          width: 160px;
          height: 160px;
          background: radial-gradient(
            circle,
            rgba(59, 130, 246, 0.03) 0%,
            transparent 70%
          );
          border-radius: 50%;
        }

        &::after {
          content: '';
          position: absolute;
          bottom: -20%;
          left: -8%;
          width: 120px;
          height: 120px;
          background: radial-gradient(
            circle,
            rgba(16, 185, 129, 0.03) 0%,
            transparent 70%
          );
          border-radius: 50%;
        }

        .info-header {
          display: flex;
          gap: 20px;
          align-items: center;
          position: relative;
          z-index: 2;

          .building-image-large {
            width: 88px;
            height: 88px;
            border-radius: 16px;
            object-fit: cover;
            flex-shrink: 0;
            position: relative;
            overflow: hidden;

            // 多层阴影效果
            box-shadow:
              0 4px 6px -1px rgba(0, 0, 0, 0.1),
              0 2px 4px -1px rgba(0, 0, 0, 0.06),
              0 0 0 1px rgba(255, 255, 255, 0.05);

            // 光泽效果
            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(
                135deg,
                rgba(255, 255, 255, 0.2) 0%,
                rgba(255, 255, 255, 0.1) 50%,
                rgba(255, 255, 255, 0.05) 100%
              );
              z-index: 1;
            }

            // 边框光效
            &::after {
              content: '';
              position: absolute;
              top: -1px;
              left: -1px;
              right: -1px;
              bottom: -1px;
              background: linear-gradient(
                135deg,
                rgba(59, 130, 246, 0.3) 0%,
                rgba(16, 185, 129, 0.3) 50%,
                rgba(59, 130, 246, 0.3) 100%
              );
              border-radius: 17px;
              z-index: -1;
              opacity: 0.6;
            }

            img {
              position: relative;
              z-index: 2;
            }
          }

          .info-details {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 88px;

            .title-section {
              .building-title {
                display: flex;
                align-items: center;
                gap: 12px;
                margin-bottom: 6px;

                h2 {
                  margin: 0;
                  color: #1a202c;
                  font-size: 24px;
                  font-weight: 800;
                  letter-spacing: -0.025em;
                  line-height: 1.2;
                }

                .address {
                  color: #64748b;
                  font-size: 13px;
                  font-weight: 500;
                  display: flex;
                  align-items: center;
                  gap: 4px;
                  padding: 4px 8px;
                  background: rgba(59, 130, 246, 0.08);
                  border-radius: 12px;
                  border: 1px solid rgba(59, 130, 246, 0.1);

                  &::before {
                    content: '📍';
                    font-size: 11px;
                  }
                }
              }
            }

            .stats {
              display: grid;
              grid-template-columns: repeat(4, 1fr);
              gap: 8px;
              margin-top: auto;

              .stat-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 8px 10px;
                background: rgba(255, 255, 255, 0.8);
                backdrop-filter: blur(10px);
                border-radius: 10px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                text-align: center;
                position: relative;
                overflow: hidden;

                // 背景光效
                &::before {
                  content: '';
                  position: absolute;
                  top: 0;
                  left: -100%;
                  width: 100%;
                  height: 100%;
                  background: linear-gradient(
                    90deg,
                    transparent 0%,
                    rgba(59, 130, 246, 0.1) 50%,
                    transparent 100%
                  );
                  transition: left 0.5s ease;
                }

                &:hover {
                  transform: translateY(-2px) scale(1.02);
                  box-shadow:
                    0 8px 25px -5px rgba(59, 130, 246, 0.2),
                    0 4px 6px -2px rgba(0, 0, 0, 0.05);
                  border-color: rgba(59, 130, 246, 0.3);
                  background: rgba(255, 255, 255, 0.95);

                  &::before {
                    left: 100%;
                  }
                }

                .label {
                  color: #64748b;
                  font-size: 10px;
                  font-weight: 600;
                  margin-bottom: 3px;
                  line-height: 1.2;
                  text-transform: uppercase;
                  letter-spacing: 0.025em;
                }

                .value {
                  color: #2563eb;
                  font-weight: 800;
                  font-size: 14px;
                  line-height: 1.2;
                }
              }
            }
          }
        }
      }

      .floors-layout {
        flex: 1;
        padding: 16px 24px 20px 24px;
        overflow-y: auto;
        min-height: 0; // 确保flex子项可以收缩
        background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);
        border-radius: 0 0 16px 16px;
        margin: 0 -1px -1px -1px;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 24px;
          right: 24px;
          height: 1px;
          background: linear-gradient(
            90deg,
            transparent 0%,
            rgba(226, 232, 240, 0.5) 20%,
            rgba(226, 232, 240, 0.8) 50%,
            rgba(226, 232, 240, 0.5) 80%,
            transparent 100%
          );
        }

        .floors-main {
          display: flex;
          gap: 16px;
          height: calc(100vh - 280px);
          min-height: 600px;

          // 左侧楼层列表
          .floors-list {
            width: 320px;
            background: white;
            border-radius: 16px;
            border: 1px solid #e2e8f0;
            display: flex;
            flex-direction: column;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            overflow: hidden;

            .floors-list-header {
              padding: 16px 18px;
              background: #f8fafc;
              border-bottom: 1px solid #e2e8f0;
              display: flex;
              align-items: center;
              gap: 8px;

              .header-icon {
                font-size: 16px;
              }

              h4 {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
                color: #374151;
              }
            }

            .floors-list-content {
              flex: 1;
              overflow-y: auto;
              padding: 8px;

              &::-webkit-scrollbar {
                width: 6px;
              }

              &::-webkit-scrollbar-track {
                background: #f1f5f9;
                border-radius: 3px;
              }

              &::-webkit-scrollbar-thumb {
                background: #cbd5e0;
                border-radius: 3px;

                &:hover {
                  background: #a0aec0;
                }
              }

              .floor-item {
                display: flex;
                align-items: center;
                padding: 12px;
                margin-bottom: 8px;
                background: white;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.2s ease;
                border: 1px solid #f1f5f9;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

                &:hover {
                  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                  transform: translateY(-1px);
                }

                &.active {
                  border-color: #3b82f6;
                  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
                }

                .floor-avatar {
                  width: 48px;
                  height: 48px;
                  background: linear-gradient(135deg, #4285f4 0%, #1976d2 100%);
                  border-radius: 8px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: white;
                  font-weight: 700;
                  font-size: 14px;
                  margin-right: 12px;
                  flex-shrink: 0;
                }

                .floor-content {
                  flex: 1;
                  min-width: 0;

                  .floor-title {
                    font-size: 14px;
                    font-weight: 600;
                    color: #1f2937;
                    margin-bottom: 2px;
                  }

                  .floor-desc {
                    font-size: 12px;
                    color: #6b7280;
                    line-height: 1.4;
                  }
                }

                .floor-stats {
                  display: flex;
                  flex-direction: column;
                  align-items: flex-end;
                  gap: 2px;

                  .occupancy-rate {
                    font-size: 16px;
                    font-weight: 700;

                    &.high {
                      color: #10b981;
                    }

                    &.medium {
                      color: #f59e0b;
                    }

                    &.low {
                      color: #ef4444;
                    }
                  }

                  .room-count {
                    font-size: 11px;
                    color: #9ca3af;
                    font-weight: 500;
                  }
                }
              }
            }
          }

          // 右侧房间信息
          .floor-rooms {
            flex: 1;
            background: white;
            border-radius: 8px;
            border: 1px solid #ebeef5;
            display: flex;
            flex-direction: column;

            .floor-rooms-header {
              padding: 14px 16px;
              border-bottom: 1px solid #ebeef5;
              background: #f8f9fa;
              border-radius: 8px 8px 0 0;

              .header-top {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;

                h4 {
                  margin: 0;
                  font-size: 16px;
                  font-weight: 600;
                  color: #303133;
                }

                .layout-legend {
                  display: flex;
                  gap: 12px;

                  .legend-item {
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    font-size: 12px;
                    color: #4a5568;
                    font-weight: 500;
                    padding: 4px 8px;
                    border-radius: 12px;
                    background: white;
                    transition: all 0.3s ease;
                    border: 1px solid #e2e8f0;

                    &:hover {
                      transform: translateY(-1px);
                      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                    }

                    .legend-color {
                      width: 12px;
                      height: 12px;
                      border-radius: 3px;
                      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                      position: relative;

                      &::after {
                        content: '';
                        position: absolute;
                        top: 1px;
                        left: 1px;
                        right: 1px;
                        bottom: 1px;
                        border-radius: 2px;
                        background: linear-gradient(
                          135deg,
                          rgba(255, 255, 255, 0.1) 0%,
                          rgba(255, 255, 255, 0.05) 100%
                        );
                      }

                      &.vacant {
                        background: linear-gradient(
                          135deg,
                          #909399 0%,
                          #718096 100%
                        );
                      }

                      &.occupied {
                        background: linear-gradient(
                          135deg,
                          #67c23a 0%,
                          #48bb78 100%
                        );
                      }

                      &.maintenance {
                        background: linear-gradient(
                          135deg,
                          #f56c6c 0%,
                          #e53e3e 100%
                        );
                      }
                    }
                  }
                }
              }

              .floor-stats {
                display: flex;
                gap: 12px;
                font-size: 11px;
                color: #606266;

                span {
                  padding: 2px 6px;
                  background: #f0f2f5;
                  border-radius: 4px;
                }
              }
            }

            .floor-rooms-content {
              flex: 1;
              padding: 16px;
              overflow-y: auto;

              .rooms-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
                gap: 16px;
                padding: 4px;

                .room-item {
                  aspect-ratio: 1;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  border-radius: 12px;
                  font-size: 13px;
                  font-weight: 700;
                  cursor: pointer;
                  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                  border: 2px solid transparent;
                  position: relative;
                  overflow: hidden;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

                  &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(
                      135deg,
                      rgba(255, 255, 255, 0.2) 0%,
                      rgba(255, 255, 255, 0.05) 100%
                    );
                    z-index: 1;
                  }

                  &::after {
                    content: '';
                    position: absolute;
                    top: 2px;
                    left: 2px;
                    right: 2px;
                    bottom: 2px;
                    border-radius: 10px;
                    background: linear-gradient(
                      135deg,
                      rgba(255, 255, 255, 0.1) 0%,
                      rgba(255, 255, 255, 0.05) 100%
                    );
                    z-index: 2;
                  }

                  span {
                    position: relative;
                    z-index: 3;
                  }

                  &:hover {
                    transform: translateY(-4px) scale(1.05);
                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                    border-color: rgba(255, 255, 255, 0.3);
                  }

                  &.room-vacant {
                    background: linear-gradient(
                      135deg,
                      #909399 0%,
                      #718096 100%
                    );
                    color: white;

                    &:hover {
                      box-shadow: 0 8px 25px rgba(144, 147, 153, 0.3);
                    }
                  }

                  &.room-occupied {
                    background: linear-gradient(
                      135deg,
                      #67c23a 0%,
                      #48bb78 100%
                    );
                    color: white;

                    &:hover {
                      box-shadow: 0 8px 25px rgba(103, 194, 58, 0.3);
                    }
                  }

                  &.room-maintenance {
                    background: linear-gradient(
                      135deg,
                      #f56c6c 0%,
                      #e53e3e 100%
                    );
                    color: white;

                    &:hover {
                      box-shadow: 0 8px 25px rgba(245, 108, 108, 0.3);
                    }
                  }
                }
              }

              .empty-rooms,
              .select-floor-tip {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 300px;
              }
            }
          }
        }
      }
    }

    // 房间详情弹窗
    :deep(.el-dialog) {
      border-radius: 16px;
      overflow: hidden;

      .el-dialog__header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px 24px;

        .el-dialog__title {
          color: white;
          font-weight: 700;
          font-size: 18px;
        }

        .el-dialog__headerbtn {
          .el-dialog__close {
            color: white;
            font-size: 18px;

            &:hover {
              color: rgba(255, 255, 255, 0.8);
            }
          }
        }
      }

      .el-dialog__body {
        padding: 24px;
        background: #f8fafc;
      }

      .el-dialog__footer {
        background: white;
        padding: 16px 24px;
        border-top: 1px solid #e2e8f0;
      }
    }

    .room-details {
      .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        margin-bottom: 12px;
        background: white;
        border-radius: 12px;
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
          border-color: #cbd5e0;
        }

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #718096;
          font-size: 14px;
          font-weight: 500;
          display: flex;
          align-items: center;
          gap: 8px;

          &::before {
            content: '•';
            color: #667eea;
            font-weight: bold;
          }
        }

        .value {
          color: #2d3748;
          font-weight: 600;
          font-size: 15px;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .rent-control-page {
      padding: 16px;

      .drawer-content {
        .building-info {
          .info-header {
            flex-direction: column;
            text-align: center;

            .building-image-large {
              width: 100px;
              height: 100px;
              align-self: center;
            }

            .info-details {
              .stats {
                grid-template-columns: 1fr;
              }
            }
          }
        }

        .floors-layout {
          .layout-legend {
            flex-wrap: wrap;
            gap: 12px;
          }

          .floors-main {
            flex-direction: column;
            height: auto;

            .floors-list {
              width: 100%;
              max-height: 200px;

              .floors-list-content {
                .floor-item {
                  padding: 8px 12px;

                  .floor-number {
                    font-size: 11px;
                    padding: 3px 6px;
                  }

                  .floor-info {
                    .room-count,
                    .occupancy-rate {
                      font-size: 11px;
                    }
                  }
                }
              }
            }

            .floor-rooms {
              .floor-rooms-content {
                .rooms-grid {
                  grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
                  gap: 8px;

                  .room-item {
                    font-size: 11px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  /* Element Plus 图片预览大小控制 */
  :global(.el-image-viewer__wrapper .el-image-viewer__img) {
    max-width: 50vw !important;
    max-height: 50vh !important;
    width: auto !important;
    height: auto !important;
    object-fit: contain !important;
  }
</style>
