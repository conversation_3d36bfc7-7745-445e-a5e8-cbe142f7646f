<template>
  <div class="floor-layout">
    <div class="floor-header">
      <div class="floor-info">
        <span class="floor-number">{{ floor.number }}</span>
        <div class="floor-stats">
          <span class="room-info">房间号</span>
          <span class="occupancy-info">出租率: {{ occupancyRate }}%</span>
        </div>
      </div>
    </div>
    
    <div class="rooms-grid">
      <div 
        v-for="room in floor.rooms" 
        :key="room.id"
        class="room-item"
        :class="getRoomClass(room.status)"
        @click="$emit('room-click', room)"
      >
        {{ room.number }}
      </div>
    </div>
  </div>
</template>

<script setup>
  import { computed } from 'vue';

  const props = defineProps({
    floor: {
      type: Object,
      required: true
    }
  });

  const emit = defineEmits(['room-click']);

  const occupancyRate = computed(() => {
    if (!props.floor.rooms || props.floor.rooms.length === 0) return 0;
    const occupiedCount = props.floor.rooms.filter(room => room.status === 'occupied').length;
    return Math.round((occupiedCount / props.floor.rooms.length) * 100);
  });

  function getRoomClass(status) {
    return {
      'room-vacant': status === 'vacant',
      'room-occupied': status === 'occupied', 
      'room-maintenance': status === 'maintenance'
    };
  }
</script>

<style lang="scss" scoped>
  .floor-layout {
    background: white;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #ebeef5;
    margin-bottom: 16px;

    .floor-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f2f5;

      .floor-info {
        display: flex;
        align-items: center;
        gap: 16px;

        .floor-number {
          background: #909399;
          color: white;
          padding: 4px 12px;
          border-radius: 4px;
          font-weight: 600;
          font-size: 14px;
        }

        .floor-stats {
          display: flex;
          flex-direction: column;
          gap: 2px;

          .room-info {
            font-size: 12px;
            color: #909399;
          }

          .occupancy-info {
            font-size: 12px;
            color: #606266;
            font-weight: 500;
          }
        }
      }
    }

    .rooms-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 8px;

      .room-item {
        aspect-ratio: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        font-size: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        border: 2px solid transparent;

        &:hover {
          transform: scale(1.05);
          border-color: #409eff;
        }

        &.room-vacant {
          background: #909399;
          color: white;
        }

        &.room-occupied {
          background: #67c23a;
          color: white;
        }

        &.room-maintenance {
          background: #f56c6c;
          color: white;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .floor-layout {
      .rooms-grid {
        grid-template-columns: repeat(3, 1fr);
      }
    }
  }

  @media (max-width: 480px) {
    .floor-layout {
      .rooms-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }
</style>
