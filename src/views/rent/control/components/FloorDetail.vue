<template>
  <div class="floor-detail">
    <div class="floor-header">
      <div class="floor-info">
        <span class="floor-number">{{ floor.floor }}F</span>
        <div class="floor-stats">
          <span class="occupied">{{ floor.occupiedRooms }}/{{ floor.totalRooms }}</span>
          <span class="rate">{{ Math.round((floor.occupiedRooms / floor.totalRooms) * 100) }}%</span>
        </div>
      </div>
      <el-progress 
        :percentage="Math.round((floor.occupiedRooms / floor.totalRooms) * 100)"
        :color="getProgressColor(Math.round((floor.occupiedRooms / floor.totalRooms) * 100))"
        :stroke-width="6"
        :show-text="false"
        class="floor-progress"
      />
    </div>
    
    <div class="rooms-grid">
      <div 
        v-for="room in floor.rooms" 
        :key="room.id"
        class="room-item"
        :class="`room-item--${room.status}`"
        @click="showRoomDetail(room)"
      >
        <div class="room-number">{{ room.number }}</div>
        <div class="room-status">
          <el-icon v-if="room.status === 'occupied'"><User /></el-icon>
          <el-icon v-else><House /></el-icon>
        </div>
      </div>
    </div>
    
    <!-- 房间详情弹窗 -->
    <el-dialog 
      v-model="roomDetailVisible" 
      :title="`${floor.floor}F-${selectedRoom?.number}号房间`"
      width="400px"
      :show-close="true"
    >
      <div v-if="selectedRoom" class="room-detail-content">
        <div class="detail-item">
          <label>房间状态:</label>
          <el-tag 
            :type="selectedRoom.status === 'occupied' ? 'success' : 'info'"
            effect="light"
          >
            {{ selectedRoom.status === 'occupied' ? '已出租' : '空置' }}
          </el-tag>
        </div>
        
        <div v-if="selectedRoom.tenant" class="detail-item">
          <label>租户信息:</label>
          <span>{{ selectedRoom.tenant }}</span>
        </div>
        
        <div class="detail-item">
          <label>房间编号:</label>
          <span>{{ floor.floor }}F-{{ selectedRoom.number }}</span>
        </div>
        
        <div class="detail-item">
          <label>楼层信息:</label>
          <span>第{{ floor.floor }}层</span>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="roomDetailVisible = false">关闭</el-button>
        <el-button 
          v-if="selectedRoom?.status === 'vacant'" 
          type="primary"
          @click="handleRent"
        >
          出租管理
        </el-button>
        <el-button 
          v-else 
          type="warning"
          @click="handleContract"
        >
          合同管理
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { User, House } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

defineProps({
  floor: {
    type: Object,
    required: true
  }
})

const roomDetailVisible = ref(false)
const selectedRoom = ref(null)

// 获取进度条颜色
function getProgressColor(rate) {
  if (rate >= 90) return '#52c41a'
  if (rate >= 80) return '#1890ff'
  if (rate >= 70) return '#faad14'
  return '#ff4d4f'
}

// 显示房间详情
function showRoomDetail(room) {
  selectedRoom.value = room
  roomDetailVisible.value = true
}

// 处理出租
function handleRent() {
  ElMessage.success('跳转到出租管理页面')
  roomDetailVisible.value = false
}

// 处理合同
function handleContract() {
  ElMessage.success('跳转到合同管理页面')
  roomDetailVisible.value = false
}
</script>

<style lang="scss" scoped>
.floor-detail {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #d9d9d9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .floor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    .floor-info {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .floor-number {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
        min-width: 30px;
      }
      
      .floor-stats {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
        
        .occupied {
          color: #52c41a;
          font-weight: 500;
        }
        
        .rate {
          color: #8c8c8c;
        }
      }
    }
    
    .floor-progress {
      flex: 1;
      margin-left: 16px;
    }
  }
  
  .rooms-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
    
    .room-item {
      aspect-ratio: 1;
      border-radius: 6px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      position: relative;
      
      &--occupied {
        background: #f6ffed;
        border: 1px solid #b7eb8f;
        color: #52c41a;
        
        &:hover {
          background: #d9f7be;
          transform: scale(1.05);
        }
      }
      
      &--vacant {
        background: #f0f0f0;
        border: 1px solid #d9d9d9;
        color: #8c8c8c;
        
        &:hover {
          background: #e6f7ff;
          border-color: #91d5ff;
          color: #1890ff;
          transform: scale(1.05);
        }
      }
      
      .room-number {
        font-size: 12px;
        font-weight: 500;
        margin-bottom: 2px;
      }
      
      .room-status {
        .el-icon {
          font-size: 14px;
        }
      }
    }
  }
}

.room-detail-content {
  .detail-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    
    label {
      width: 80px;
      font-weight: 500;
      color: #262626;
    }
    
    span {
      color: #595959;
    }
  }
}

// 响应式适配
@media (max-width: 480px) {
  .floor-detail {
    .rooms-grid {
      grid-template-columns: repeat(4, 1fr);
    }
  }
}
</style>
