<template>
  <div class="occupancy-distribution-chart">
    <v-chart 
      class="chart" 
      :option="chartOption" 
      autoresize
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components'
import VChart from 'vue-echarts'

use([
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TitleComponent,
  TooltipComponent,
  LegendComponent
])

const props = defineProps({
  data: {
    type: Array,
    required: true
  }
})

const chartOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderColor: '#e6e6e6',
    borderWidth: 1,
    textStyle: {
      color: '#333'
    },
    formatter: function(params) {
      return `
        <div style="padding: 8px;">
          <div style="font-weight: 600; margin-bottom: 4px;">${params.name}</div>
          <div style="display: flex; align-items: center;">
            <span style="display: inline-block; width: 10px; height: 10px; background: ${params.color}; border-radius: 50%; margin-right: 8px;"></span>
            出租率: <strong>${params.value}%</strong>
          </div>
          <div style="color: #8c8c8c; font-size: 12px; margin-top: 4px;">
            占比: ${params.percent}%
          </div>
        </div>
      `
    }
  },
  legend: {
    orient: 'horizontal',
    bottom: '5%',
    left: 'center',
    textStyle: {
      color: '#8c8c8c',
      fontSize: 12
    },
    itemWidth: 12,
    itemHeight: 12
  },
  series: [
    {
      name: '楼栋出租率',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '45%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 8,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: true,
        position: 'outside',
        formatter: function(params) {
          return `${params.name}\n${params.value}%`
        },
        fontSize: 12,
        color: '#595959'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 14,
          fontWeight: 'bold'
        },
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.2)'
        }
      },
      labelLine: {
        show: true,
        lineStyle: {
          color: '#d9d9d9'
        }
      },
      data: props.data.map((item, index) => ({
        value: item.value,
        name: item.name,
        itemStyle: {
          color: getColor(index)
        }
      }))
    }
  ]
}))

// 获取颜色
function getColor(index) {
  const colors = [
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#667eea' },
        { offset: 1, color: '#764ba2' }
      ]
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#f093fb' },
        { offset: 1, color: '#f5576c' }
      ]
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#4facfe' },
        { offset: 1, color: '#00f2fe' }
      ]
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#43e97b' },
        { offset: 1, color: '#38f9d7' }
      ]
    }
  ]
  return colors[index % colors.length]
}
</script>

<style lang="scss" scoped>
.occupancy-distribution-chart {
  width: 100%;
  height: 300px;
  
  .chart {
    width: 100%;
    height: 100%;
  }
}
</style>
