<template>
  <div
    class="building-card"
    :class="{ 'building-card--active': active }"
    @click="$emit('click')"
  >
    <div class="building-card__header">
      <div class="building-info">
        <h4 class="building-name">{{ building.name }}</h4>
        <div class="building-stats">
          <span class="stat-item">
            <el-icon><OfficeBuilding /></el-icon>
            {{ building.totalFloors }}层
          </span>
          <span class="stat-item">
            <el-icon><House /></el-icon>
            {{ building.totalRooms }}间
          </span>
        </div>
      </div>
      <div class="occupancy-rate" :class="getRateClass(building.occupancyRate)">
        {{ building.occupancyRate }}%
      </div>
    </div>

    <div class="building-card__body">
      <div class="progress-section">
        <div class="progress-info">
          <span class="occupied">已出租: {{ building.occupiedRooms }}</span>
          <span class="vacant"
            >空置: {{ building.totalRooms - building.occupiedRooms }}</span
          >
        </div>
        <el-progress
          :percentage="building.occupancyRate"
          :color="getProgressColor(building.occupancyRate)"
          :stroke-width="8"
          :show-text="false"
        />
      </div>

      <div class="building-card__footer">
        <el-tag
          :type="getStatusType(building.occupancyRate)"
          size="small"
          effect="light"
        >
          {{ getStatusText(building.occupancyRate) }}
        </el-tag>
        <span class="view-details">点击查看详情</span>
      </div>
    </div>

    <!-- 激活状态指示器 -->
    <div v-if="active" class="active-indicator"></div>
  </div>
</template>

<script setup>
  import { OfficeBuilding, House } from '@element-plus/icons-vue';

  defineProps({
    building: {
      type: Object,
      required: true
    },
    active: {
      type: Boolean,
      default: false
    }
  });

  defineEmits(['click']);

  // 获取出租率等级样式
  function getRateClass(rate) {
    if (rate >= 90) return 'rate-excellent';
    if (rate >= 80) return 'rate-good';
    if (rate >= 70) return 'rate-normal';
    return 'rate-low';
  }

  // 获取进度条颜色
  function getProgressColor(rate) {
    if (rate >= 90) return '#52c41a';
    if (rate >= 80) return '#1890ff';
    if (rate >= 70) return '#faad14';
    return '#ff4d4f';
  }

  // 获取状态标签类型
  function getStatusType(rate) {
    if (rate >= 90) return 'success';
    if (rate >= 80) return 'primary';
    if (rate >= 70) return 'warning';
    return 'danger';
  }

  // 获取状态文本
  function getStatusText(rate) {
    if (rate >= 90) return '出租率优秀';
    if (rate >= 80) return '出租率良好';
    if (rate >= 70) return '出租率一般';
    return '出租率偏低';
  }
</script>

<style lang="scss" scoped>
  .building-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    border: 2px solid transparent;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    min-height: 200px;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      border-color: #1890ff;
    }

    &--active {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);

      .active-indicator {
        position: absolute;
        top: 0;
        right: 0;
        width: 0;
        height: 0;
        border-left: 20px solid transparent;
        border-top: 20px solid #1890ff;

        &::after {
          content: '✓';
          position: absolute;
          top: -18px;
          right: -8px;
          color: white;
          font-size: 10px;
          font-weight: bold;
        }
      }
    }

    &__header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;

      .building-info {
        flex: 1;

        .building-name {
          margin: 0 0 8px 0;
          font-size: 18px;
          font-weight: 600;
          color: #262626;
        }

        .building-stats {
          display: flex;
          gap: 16px;

          .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #8c8c8c;

            .el-icon {
              font-size: 14px;
            }
          }
        }
      }

      .occupancy-rate {
        font-size: 24px;
        font-weight: bold;
        padding: 8px 12px;
        border-radius: 8px;

        &.rate-excellent {
          background: #f6ffed;
          color: #52c41a;
        }

        &.rate-good {
          background: #e6f7ff;
          color: #1890ff;
        }

        &.rate-normal {
          background: #fffbe6;
          color: #faad14;
        }

        &.rate-low {
          background: #fff2f0;
          color: #ff4d4f;
        }
      }
    }

    &__body {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .progress-section {
        margin-bottom: 16px;

        .progress-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          font-size: 12px;

          .occupied {
            color: #52c41a;
          }

          .vacant {
            color: #ff4d4f;
          }
        }
      }
    }

    &__footer {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .view-details {
        font-size: 12px;
        color: #8c8c8c;
        opacity: 0;
        transition: opacity 0.3s ease;
      }
    }

    &:hover {
      .view-details {
        opacity: 1;
      }
    }
  }

  // 列表视图样式
  .view-list .building-card {
    display: flex;
    align-items: center;
    padding: 16px 20px;

    &__header {
      margin-bottom: 0;
      margin-right: 24px;

      .building-info {
        .building-stats {
          margin-top: 4px;
        }
      }
    }

    &__body {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 24px;

      .progress-section {
        flex: 1;
        margin-bottom: 0;

        .progress-info {
          margin-bottom: 4px;
        }
      }
    }

    &__footer {
      margin-left: 24px;
    }
  }
</style>
