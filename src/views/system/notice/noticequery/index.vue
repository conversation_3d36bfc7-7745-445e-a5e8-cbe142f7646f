<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <NoticeSearch @search="reload" />
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        :highlight-current-row="true"
        :export-config="{ fileName: '通知公告查询' }"
        cache-key="cuxExpertTable"
      >
        <template #toolbar>
          <el-button
            class="ele-btn-icon"
            :icon="DownloadOutlined"
            @click="exportData"
          >
            导出
          </el-button>
        </template>
        <template #avatar="{ row }">
          <el-image :src="row.avatar" class="h-38px w-38px mr-10px rounded" />
        </template>
        <template #approveMethod="{ row }">
          <dict-data
            :code="DICT_TYPE.APPROVE_METHODS"
            type="tag"
            :model-value="row.approveMethod"
          />
        </template>
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.COMMON_APPROVE_STATUS"
            type="tag"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link type="primary" :underline="false" @click="openEdit(row)">
            详情
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <NoticeDetail v-model="showDetail" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { ref } from 'vue';
  import { DownloadOutlined } from '@/components/icons';
  import { DICT_TYPE } from '@/utils/dict';

  import { EleMessage } from 'sirius-platform-pro/es';
  import NoticeSearch from './components/NoticeSearch.vue';
  import NoticeDetail from './components/NoticeDetail.vue';

  import { NoticeApi } from '@/api/system/notice/noticemanager';
  import { dateFormatter } from '@/utils/formatTime';

  defineOptions({ name: 'NoticeQuery' });

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'title',
      label: '标题',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'content',
      label: '内容',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'typeDesc',
      label: '类型',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'approveMethod',
      label: '审批方式',
      align: 'center',
      minWidth: 110,
      slot: 'approveMethod'
    },
    {
      prop: 'status',
      label: '状态',
      align: 'center',
      minWidth: 110,
      slot: 'status'
    },
    {
      prop: 'message',
      label: '消息',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '创建日期',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示详情弹窗 */
  const showDetail = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where }) => {
    return NoticeApi.getNoticePageQuery({ ...where, ...pages });
  };

  /** 搜索 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showDetail.value = true;
  };

  /** 导出数据 */
  const exportData = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ where }) => {
      NoticeApi.exportNoticeQuery(where)
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };
</script>
