<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <NoticeTypeAssignRoleSearch @search="reload" />
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        :highlight-current-row="true"
        :export-config="{ fileName: '公告类型' }"
        cache-key="noticeTypeAssignTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            新增
          </el-button>
          <el-button
            class="ele-btn-icon"
            :icon="DownloadOutlined"
            @click="exportData"
          >
            导出
          </el-button>
        </template>
        <template #avatar="{ row }">
          <el-image :src="row.avatar" class="h-38px w-38px mr-10px rounded" />
        </template>
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.COMMON_STATUS"
            type="tag"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link type="primary" :underline="false" @click="openEdit(row)">
            修改
          </el-link>
          <el-divider v-if="moreItems.length" direction="vertical" />
          <ele-dropdown
            v-if="moreItems.length"
            :items="moreItems"
            style="display: inline"
            @command="(key) => dropClick(key, row)"
          >
            <el-link type="primary" :underline="false">
              <span>更多</span>
              <el-icon
                :size="12"
                style="vertical-align: -1px; margin-left: 2px"
              >
                <ArrowDown />
              </el-icon>
            </el-link>
          </ele-dropdown>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <NoticeTypeAssignForm v-model="showEdit" :data="current" @done="reload" />
    <!-- 角色弹窗 -->
    <NoticeTypeAssignRole v-model="showRole" :data="current" @done="reload" />
    <!-- 用户弹窗 -->
    <NoticeTypeAssignUser v-model="showUser" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { PlusOutlined, DownloadOutlined } from '@/components/icons';
  import { DICT_TYPE } from '@/utils/dict';

  import { EleMessage } from 'sirius-platform-pro/es';
  import NoticeTypeAssignRoleSearch from './components/NoticeTypeAssignSearch.vue';
  import NoticeTypeAssignForm from './components/NoticeTypeAssignForm.vue';
  import NoticeTypeAssignRole from './components/NoticeTypeAssignRole.vue';
  import NoticeTypeAssignUser from './components/NoticeTypeAssignUser.vue';

  import { NoticeTypeAssignApi } from '@/api/system/notice/noticetypeassign';
  import { dateFormatter } from '@/utils/formatTime';
  import { ElMessageBox } from 'element-plus/es';

  defineOptions({ name: 'NoticeTypeAssign' });

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'typeName',
      label: '公告类型名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'typeValue',
      label: '公告类型值',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'status',
      label: '公告状态',
      align: 'center',
      minWidth: 110,
      slot: 'status'
    },
    {
      prop: 'createTime',
      label: '创建日期',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 是否显示角色弹窗 */
  const showRole = ref(false);
  /** 是否显示用户弹窗 */
  const showUser = ref(false);
  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where }) => {
    return NoticeTypeAssignApi.getNoticeTypeAssignPage({ ...where, ...pages });
  };

  /** 搜索 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 导出数据 */
  const exportData = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ where }) => {
      NoticeTypeAssignApi.exportNoticeTypeAssign(where)
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };

  /** 批量删除 */
  const removeBatch = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      `是否确认删除公告类型为"${rows.map((d) => d.typeName).join()}"的数据项?`,
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading({
          message: '请求中..',
          plain: true
        });
        NoticeTypeAssignApi.deleteNoticeTypeAssign(rows.map((d) => d.id))
          .then(() => {
            loading.close();
            EleMessage.success('删除成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
  /** 操作列更多下拉菜单 */
  const moreItems = computed(() => {
    const items = [];
    items.push({ title: '分配角色', command: 'role' });
    items.push({ title: '分配用户', command: 'user' });
    items.push({ title: '删除', command: 'delete' });
    return items;
  });
  /** 下拉菜单点击事件 */
  const dropClick = (key, row) => {
    if (key === 'role') {
      current.value = row ?? null;
      showRole.value = true;
    } else if (key === 'user') {
      current.value = row ?? null;
      showUser.value = true;
    } else if (key === 'delete') {
      removeBatch(row);
    }
  };
</script>
