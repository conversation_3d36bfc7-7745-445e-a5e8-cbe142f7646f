<template>
  <ele-drawer
    form
    :size="'50%'"
    v-model="visible"
    :title="isUpdate ? '修改' : '新增'"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent=""
    >
      <el-form-item label="公告类型名称" prop="name">
        <el-input
          clearable
          v-model="form.typeName"
          placeholder="公告类型名称"
        />
      </el-form-item>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="公告类型编码" prop="typeValue">
            <el-input
              clearable
              v-model="form.typeValue"
              placeholder="公告类型编码"
              :readonly="isUpdate"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                :key="dict.value"
                :label="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';

  import { ref, reactive, nextTick } from 'vue';
  import { EleMessage } from 'sirius-platform-pro/es';
  import { useFormData } from '@/utils/use-form-data';
  import { NoticeTypeAssignApi } from '@/api/system/notice/noticetypeassign';

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const emit = defineEmits(['done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    typeName: '',
    typeValue: '',
    status: 0 // 默认状态为开启，假设开启的状态值为 1
  });

  /** 表单验证规则 */
  const rules = reactive({
    typeName: [
      { required: true, message: '公告类型名称不能为空', trigger: 'blur' }
    ],
    typeValue: [
      { required: true, message: '公告类型编码不能为空', trigger: 'blur' }
    ],
    status: [{ required: true, message: '需求状态不能为空', trigger: 'blur' }]
  });

  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false;
  };

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.(async (valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      const saveOrUpdate = isUpdate.value
        ? NoticeTypeAssignApi.updateNoticeTypeAssign
        : NoticeTypeAssignApi.createNoticeTypeAssign;
      try {
        let requst = { ...form };
        await saveOrUpdate(requst);
        EleMessage.success('修改成功');
        handleCancel();
        emit('done');
      } finally {
        loading.value = false;
      }
    });
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    if (props.data) {
      const res = await NoticeTypeAssignApi.getNoticeTypeAssign(props.data.id);
      assignFields(res);
      isUpdate.value = true;
    } else {
      resetFields();
      isUpdate.value = false;
    }
    nextTick(() => {
      nextTick(() => {
        formRef.value?.clearValidate?.();
      });
    });
  };
</script>
