<template>
  <ele-modal
    :width="640"
    title="分配用户"
    :body-style="{ padding: '4px 16px 8px 16px' }"
    :destroy-on-close="true"
    :model-value="modelValue"
    @update:modelValue="updateModelValue"
  >
    <ele-pro-table
      ref="tableRef"
      row-key="id"
      :columns="columns"
      :datasource="datasource"
      :show-overflow-tooltip="true"
      v-model:selections="selections"
      highlight-current-row
      :pagination="false"
      :toolbar="false"
      :empty-props="false"
    />
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, watch } from 'vue';
  import { EleMessage } from 'sirius-platform-pro/es';
  import { getSimpleUserList } from '@/api/system/user';
  import { NoticeTypeAssignApi } from '@/api/system/notice/noticetypeassign';

  const emit = defineEmits(['update:modelValue']);

  const props = defineProps({
    /** 是否显示 */
    modelValue: Boolean,
    /** 用户 */
    data: Object
  });

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'nickname',
      label: '名称',
      minWidth: 100
    },
    {
      prop: 'deptName',
      label: '部门',
      minWidth: 110
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 表格数据源 */
  const datasource = ref([]);

  /** 提交状态 */
  const loading = ref(false);

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  /** 保存编辑 */
  const save = async () => {
    loading.value = true;
    const userIds = selections.value.map((d) => d.id);
    try {
      await NoticeTypeAssignApi.setNoticeTypeAssignUserListByNoticeTypeAssignId(
        {
          id: props.data?.id,
          userIds
        }
      );
      loading.value = false;
      EleMessage.success('分配成功');
      updateModelValue(false);
    } finally {
      loading.value = false;
    }
  };

  /** 查询 */
  const query = async () => {
    console.log('查询');
    const result = await getSimpleUserList();
    datasource.value = result;
    const checked =
      await NoticeTypeAssignApi.getNoticeTypeAssignUserListByNoticeTypeAssignId(
        props.data.id
      );
    const userIds = new Set(checked.map((item) => item.userId)); // 使用Set可以提高查找效率
    tableRef.value?.setSelectedRows?.(
      result.filter((user) => userIds.has(user.id))
    );
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue && props.data) {
        query();
      } else {
        selections.value = [];
      }
    }
  );
</script>
