<template>
  <ele-modal
    :width="640"
    title="分配角色"
    :body-style="{ padding: '4px 16px 8px 16px' }"
    :destroy-on-close="true"
    :model-value="modelValue"
    @update:modelValue="updateModelValue"
  >
    <ele-pro-table
      ref="tableRef"
      row-key="id"
      :columns="columns"
      :datasource="datasource"
      :show-overflow-tooltip="true"
      v-model:selections="selections"
      highlight-current-row
      :pagination="false"
      :toolbar="false"
      :empty-props="false"
    />
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, watch } from 'vue';
  import { EleMessage } from 'sirius-platform-pro/es';
  import { getSimpleRoleList } from '@/api/system/role';
  import { dateFormatter } from '@/utils/formatTime';
  import { NoticeTypeAssignApi } from '@/api/system/notice/noticetypeassign';

  const emit = defineEmits(['update:modelValue']);

  const props = defineProps({
    /** 是否显示 */
    modelValue: Boolean,
    /** 用户 */
    data: Object
  });

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'code',
      label: '角色编码',
      minWidth: 100
    },
    {
      prop: 'name',
      label: '角色名称',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      width: 180,
      formatter: dateFormatter
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 表格数据源 */
  const datasource = ref([]);

  /** 提交状态 */
  const loading = ref(false);

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  /** 保存编辑 */
  const save = async () => {
    loading.value = true;
    const roleIds = selections.value.map((d) => d.id);
    try {
      await NoticeTypeAssignApi.setNoticeTypeAssignRoleListByNoticeTypeAssignId(
        {
          id: props.data?.id,
          roleIds
        }
      );
      loading.value = false;
      EleMessage.success('分配成功');
      updateModelValue(false);
    } finally {
      loading.value = false;
    }
  };

  /** 查询 */
  const query = async () => {
    const result = await getSimpleRoleList();
    datasource.value = result;
    const checked =
      await NoticeTypeAssignApi.getNoticeTypeAssignRoleListByNoticeTypeAssignId(
        props.data.id
      );
    const userRoleIds = new Set(checked.map((item) => item.roleId)); // 使用Set可以提高查找效率
    tableRef.value?.setSelectedRows?.(
      result.filter((role) => userRoleIds.has(role.id))
    );
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue && props.data) {
        query();
      } else {
        selections.value = [];
      }
    }
  );
</script>
