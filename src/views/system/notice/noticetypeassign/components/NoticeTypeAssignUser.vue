<template>
  <ele-drawer
    :size="980"
    style="max-width: 100%"
    title="分配用户"
    :append-to-body="true"
    :destroy-on-close="true"
    :model-value="modelValue"
    @update:modelValue="updateModelValue"
  >
    <ele-pro-table
      ref="tableRef"
      row-key="id"
      :columns="columns"
      :datasource="datasource"
      :show-overflow-tooltip="true"
      v-model:selections="selections"
      highlight-current-row
    >
      <template #toolbar>
        <el-button
          type="primary"
          class="ele-btn-icon"
          :icon="PlusOutlined"
          @click="openEdit()"
        >
          添加用户
        </el-button>
        <el-button
          type="danger"
          class="ele-btn-icon"
          :icon="DeleteOutlined"
          @click="removeBatch()"
        >
          批量取消
        </el-button>
      </template>
    </ele-pro-table>
  </ele-drawer>
  <NoticeTypeAssignUserAdd
    v-model="showAddUserEdit"
    :data="current"
    @done="reload"
  />
</template>

<script setup>
  import { ref, watch } from 'vue';
  import { PlusOutlined } from '@/components/icons';
  import { NoticeTypeAssignApi } from '@/api/system/notice/noticetypeassign';
  import { dateFormatter } from '@/utils/formatTime';
  import NoticeTypeAssignUserAdd from './NoticeTypeAssignUserAdd.vue';
  import { EleMessage } from 'sirius-platform-pro/es';
  import { ElMessageBox } from 'element-plus/es';

  const emit = defineEmits(['update:modelValue']);

  const props = defineProps({
    /** 是否显示 */
    modelValue: Boolean,
    /** 角色 */
    data: Object
  });

  /** 表格实例 */
  const tableRef = ref(null);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'userName',
      label: '用户名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'userDepartmentName',
      label: '用户部门',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'userPhone',
      label: '用户电话',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      width: 168,
      formatter: dateFormatter
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 是否显示编辑弹窗 */
  const showAddUserEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where }) => {
    return NoticeTypeAssignApi.getNoticeTypeAssignUserPageByNoticeTypeAssignId({
      ...where,
      ...pages,
      noticeTypeAssignId: props.data?.id
    });
  };

  /** 搜索 */
  const reload = (where) => {
    tableRef.value?.reload?.({ where });
  };

  /** 添加用户 */
  const openEdit = () => {
    current.value = props.data;
    showAddUserEdit.value = true;
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue && props.data) {
        current.value = props.data;
        reload();
      } else {
        selections.value = [];
      }
    }
  );

  /** 批量删除 */
  const removeBatch = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      `是否确认删除用户名为"${rows.map((d) => d.userName).join()}"的数据项?`,
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading({
          message: '请求中..',
          plain: true
        });
        const userIds = selections.value.map((d) => d.userId);
        NoticeTypeAssignApi.deleteNoticeTypeAssignUser({
          id: props.data?.id,
          userIds
        })
          .then(() => {
            loading.close();
            EleMessage.success('删除成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
</script>
