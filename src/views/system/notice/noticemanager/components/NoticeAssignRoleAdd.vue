<template>
  <ele-modal
    :width="880"
    title="选择角色"
    :body-style="{ padding: '4px 16px 8px 16px' }"
    :destroy-on-close="true"
    :model-value="modelValue"
    @update:modelValue="updateModelValue"
  >
    <role-search @search="reload" />
    <ele-pro-table
      ref="tableRef"
      row-key="id"
      :columns="columns"
      :datasource="datasource"
      :show-overflow-tooltip="true"
      v-model:selections="selections"
      highlight-current-row
    />
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, watch } from 'vue';
  import { EleMessage } from 'sirius-platform-pro/es';
  import { dateFormatter } from '@/utils/formatTime';
  import { NoticeApi } from '@/api/system/notice/noticemanager';
  import RoleSearch from './role-search.vue';

  const emit = defineEmits(['update:modelValue', 'done']);

  const props = defineProps({
    /** 是否显示 */
    modelValue: Boolean,
    /** 用户 */
    data: Object
  });

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'code',
      label: '角色编码',
      minWidth: 100
    },
    {
      prop: 'name',
      label: '角色名称',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      width: 180,
      formatter: dateFormatter
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 提交状态 */
  const loading = ref(false);

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  /** 搜索 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 表格数据源 */
  const datasource = ({ pages, where }) => {
    return NoticeApi.getSimpleRoleList({
      ...where,
      ...pages,
      noticeId: props.data?.id
    });
  };

  /** 保存编辑 */
  const save = async () => {
    loading.value = true;
    const roleIds = selections.value.map((d) => d.id);
    try {
      await NoticeApi.addNoticeAssignRole({
        id: props.data?.id,
        roleIds
      });
      loading.value = false;
      EleMessage.success('分配成功');
      updateModelValue(false);
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue && props.data) {
        reload();
      } else {
        selections.value = [];
      }
    }
  );
</script>
