<template>
  <ele-modal
    form
    :width="860"
    top="40px"
    v-model="visible"
    :title="isUpdate ? '修改' : '新增'"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent=""
    >
      <el-form-item label="标题" prop="title">
        <el-input clearable v-model="form.title" placeholder="标题" />
      </el-form-item>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="公告类型">
            <el-select v-model="form.type" placeholder="请选择">
              <el-option
                v-for="item in noticeTypeList"
                :key="item.typeValue"
                :label="item.typeName"
                :value="item.typeValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="内容">
        <tinymce-editor ref="editorRef" :init="config" v-model="form.content" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, nextTick } from 'vue';
  import { EleMessage } from 'sirius-platform-pro/es';
  import { useFormData } from '@/utils/use-form-data';
  import { NoticeApi } from '@/api/system/notice/noticemanager';
  import { NoticeTypeAssignApi } from '@/api/system/notice/noticetypeassign';
  import TinymceEditor from '@/components/TinymceEditor/index.vue';

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const emit = defineEmits(['done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  // 消息类型列表
  const noticeTypeList = ref([]);

  /** 编辑器配置 */
  const config = ref({
    height: 380
  });

  const editorRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    title: '',
    type: '',
    content: ''
  });

  /** 表单验证规则 */
  const rules = reactive({
    typeName: [
      { required: true, message: '公告类型名称不能为空', trigger: 'blur' }
    ],
    typeValue: [
      { required: true, message: '公告类型编码不能为空', trigger: 'blur' }
    ],
    status: [{ required: true, message: '需求状态不能为空', trigger: 'blur' }]
  });

  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false;
  };

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.(async (valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      const saveOrUpdate = isUpdate.value
        ? NoticeApi.updateNotice
        : NoticeApi.createNotice;
      try {
        let requst = { ...form };
        await saveOrUpdate(requst);
        EleMessage.success('修改成功');
        handleCancel();
        emit('done');
      } finally {
        loading.value = false;
      }
    });
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    if (props.data) {
      const res = await NoticeApi.getNotice(props.data.id);
      assignFields(res);
      isUpdate.value = true;
      // 加载公告类型列表
      noticeTypeList.value =
        await NoticeTypeAssignApi.getNoticeTypeAssignSimple();
    } else {
      resetFields();
      // 加载公告类型列表
      noticeTypeList.value =
        await NoticeTypeAssignApi.getNoticeTypeAssignSimple();
      isUpdate.value = false;
    }
    nextTick(() => {
      nextTick(() => {
        formRef.value?.clearValidate?.();
      });
    });
  };
</script>
