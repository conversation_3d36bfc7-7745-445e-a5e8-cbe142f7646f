<!-- 编辑弹窗 -->
<template>
  <ele-modal
    destroy-on-close
    form
    :width="680"
    position="center"
    :model-value="modelValue"
    :body-style="{ paddingLeft: '0px' }"
    title="分配部门"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="108px"
      @submit.prevent=""
    >
      <el-form-item label="标题" prop="name">
        <el-tag>{{ form.title }}</el-tag>
      </el-form-item>
      <el-form-item label="类型" prop="code">
        <el-tag>{{ form.typeDesc }}</el-tag>
      </el-form-item>
      <el-form-item label="部门权限">
        <ele-loading class="notice-company-tree">
          <div style="line-height: 1; padding: 0 6px 0 6px">
            全选/全不选:
            <el-switch
              v-model="treeNodeAll"
              active-text="是"
              inactive-text="否"
              inline-prompt
              @change="handleCheckedTreeNodeAll"
            />
            全部展开/折叠:
            <el-switch
              v-model="companyExpand"
              active-text="展开"
              inactive-text="折叠"
              inline-prompt
              @change="handleCheckedTreeExpand"
            />
          </div>
          <div style="height: 260px; overflow: auto; padding: 0 6px">
            <el-tree
              ref="treeRef"
              :data="companyOptions"
              :props="defaultProps"
              empty-text="加载中，请稍候"
              node-key="id"
              show-checkbox
            />
          </div>
        </ele-loading>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, watch } from 'vue';
  import { EleMessage } from 'sirius-platform-pro/es';
  import { useFormData } from '@/utils/use-form-data';
  import { getSimpleDeptList } from '@/api/system/dept';
  import { defaultProps, handleTree } from '@/utils/tree';
  const emit = defineEmits(['done', 'update:modelValue']);
  import { NoticeApi } from '@/api/system/notice/noticemanager';

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);
  const companyOptions = ref([]); // 部门树形结构
  const companyExpand = ref(false); // 展开/折叠
  const treeRef = ref(); // 菜单树组件 Ref
  const treeNodeAll = ref(false); // 全选/全不选
  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    title: '',
    typeDesc: '',
    companyIds: []
  });

  /** 表单验证规则 */
  const rules = reactive({});

  /** 保存编辑 */
  const save = async () => {
    loading.value = true;
    try {
      const data = {
        id: form.id,
        companyIds: [
          ...treeRef.value.getCheckedKeys(false), // 获得当前选中节点
          ...treeRef.value.getHalfCheckedKeys() // 获得半选中的父节点
        ]
      };
      await NoticeApi.addNoticeAssignCompany(data);
      loading.value = false;
      EleMessage.success('保存成功');
      updateModelValue(false);
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
    if (!value) {
      // 关闭弹窗时重置展开状态
      companyExpand.value = false;
      // 关闭弹窗时重置全选状态
      treeNodeAll.value = false;
    }
  };

  watch(
    () => props.modelValue,
    async (modelValue) => {
      resetFields();
      if (modelValue) {
        companyOptions.value = handleTree(await getSimpleDeptList());
        assignFields(props.data);
        loading.value = true;
        try {
          const companyList =
            await NoticeApi.getNoticeAssignCompanyListByNoticeId(props.data.id);
          form.companyIds = companyList.map((item) => item.companyId);
          // 设置选中
          form.companyIds.forEach((companyId) => {
            treeRef.value.setChecked(companyId, true, false);
          });
        } finally {
          loading.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
  /** 全选/全不选 */
  const handleCheckedTreeNodeAll = () => {
    treeRef.value.setCheckedNodes(
      treeNodeAll.value ? companyOptions.value : []
    );
  };

  /** 展开/折叠全部 */
  const handleCheckedTreeExpand = () => {
    const nodes = treeRef.value?.store.nodesMap;
    for (let node in nodes) {
      if (nodes[node].expanded === companyExpand.value) {
        continue;
      }
      nodes[node].expanded = companyExpand.value;
    }
  };
</script>

<style lang="scss" scoped>
  .notice-company-tree {
    width: 100%;
    padding: 6px 0;
    border: 1px solid var(--el-border-color);
    border-radius: var(--el-border-radius-base);
    box-sizing: border-box;
    overflow: hidden;
  }
</style>
