<template>
  <ele-modal
    form
    :width="800"
    title="分配数据权限"
    :model-value="modelValue"
    @update:modelValue="updateModelValue"
  >
    <el-form ref="formRef" :model="form" label-width="80px" @submit.prevent="">
      <el-form-item label="角色名称" prop="name">
        <el-tag>{{ form.name }}</el-tag>
      </el-form-item>
      <el-form-item label="角色标识" prop="code">
        <el-tag>{{ form.code }}</el-tag>
      </el-form-item>
      <el-form-item label="权限范围">
        <el-select v-model="form.dataScope">
          <el-option
            v-for="item in getIntDictOptions(DICT_TYPE.SYSTEM_DATA_SCOPE)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="form.dataScope === SystemDataScopeEnum.DEPT_CUSTOM"
        label="权限范围"
      >
        <ele-loading :loading="treeLoading" class="role-auth-tree">
          <div style="line-height: 1; padding: 0 6px 0 6px">
            全选/全不选:
            <el-switch
              v-model="treeNodeAll"
              active-text="是"
              inactive-text="否"
              inline-prompt
              @change="handleCheckedTreeNodeAll()"
            />
            全部展开/折叠:
            <el-switch
              v-model="deptExpand"
              active-text="展开"
              inactive-text="折叠"
              inline-prompt
              @change="handleCheckedTreeExpand"
            />
            父子联动(选中父节点，自动选择子节点):
            <el-switch
              v-model="checkStrictly"
              active-text="是"
              inactive-text="否"
              inline-prompt
            />
          </div>
          <div style="height: 260px; overflow: auto; padding: 0 6px">
            <el-tree
              ref="treeRef"
              :check-strictly="!checkStrictly"
              :data="deptOptions"
              :props="defaultProps"
              default-expand-all
              empty-text="加载中，请稍后"
              node-key="id"
              show-checkbox
            />
          </div>
        </ele-loading>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, watch } from 'vue';
  import { EleMessage } from 'sirius-platform-pro/es';
  import { setDataScope } from '@/api/system/role';
  import { SystemDataScopeEnum } from '@/utils/constants';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { defaultProps, handleTree } from '@/utils/tree';
  import { useFormData } from '@/utils/use-form-data';
  import * as DeptApi from '@/api/system/dept';
  import * as PermissionApi from '@/api/system/permission';

  const emit = defineEmits(['done', 'update:modelValue']);
  /** 权限范围 */
  const dataScope = ref('');

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });
  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    name: '',
    code: '',
    dataScope: undefined,
    dataScopeDeptIds: []
  });
  /** 树组件 */
  const treeRef = ref(null);
  const deptOptions = ref([]); // 部门树形结构
  const deptExpand = ref(true); // 展开/折叠
  const treeNodeAll = ref(false); // 全选/全不选
  const checkStrictly = ref(true); // 是否严格模式，即父子不关联

  /** 提交状态 */
  const loading = ref(false);

  /** 获取选中节点id */
  const getCheckedIds = () => {
    if (dataScope.value != 2) {
      return [];
    }
    const ids = treeRef.value?.getCheckedKeys?.() ?? [];
    const ids2 = treeRef.value?.getHalfCheckedKeys?.() ?? [];
    return ids.concat(ids2);
  };

  /** 保存编辑 */
  const save = () => {
    loading.value = true;
    PermissionApi.assignRoleDataScope({
      roleId: props.data?.id,
      dataScope: dataScope.value,
      dataScopeDeptIds: getCheckedIds()
    })
      .then(() => {
        loading.value = false;
        EleMessage.success('修改成功');
        updateModelValue(false);
        emit('done');
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };
  /** 全选/全不选 */
  const handleCheckedTreeNodeAll = () => {
    treeRef.value.setCheckedNodes(treeNodeAll.value ? deptOptions.value : []);
  };

  /** 展开/折叠全部 */
  const handleCheckedTreeExpand = () => {
    const nodes = treeRef.value?.store.nodesMap;
    for (let node in nodes) {
      if (nodes[node].expanded === deptExpand.value) {
        continue;
      }
      nodes[node].expanded = deptExpand.value;
    }
  };
  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    async (modelValue) => {
      resetFields();
      if (modelValue) {
        deptOptions.value = handleTree(await DeptApi.getSimpleDeptList());
        assignFields(props.data);
        loading.value = true;
        await nextTick();
        try {
          row.dataScopeDeptIds?.forEach((deptId) => {
            treeRef.value.setChecked(deptId, true, false);
          });
        } finally {
          loading.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>

<style lang="scss" scoped>
  .role-auth-tree {
    width: 100%;
    padding: 6px 0;
    border: 1px solid var(--el-border-color);
    border-radius: var(--el-border-radius-base);
    box-sizing: border-box;
    overflow: hidden;
  }
</style>
