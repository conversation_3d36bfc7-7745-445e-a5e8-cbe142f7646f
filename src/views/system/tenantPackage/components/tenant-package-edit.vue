<!-- 用户编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="640"
    :model-value="modelValue"
    :title="isUpdate ? '修改' : '新建'"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent=""
    >
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="权限名" prop="name">
            <el-input
              clearable
              v-model="form.name"
              placeholder="请输入权限名"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                :key="dict.value"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :sm="24" :xs="24">
          <el-form-item label="菜单权限">
            <ele-loading :loading="menuLoading" class="tenant-menu-tree">
              <div style="line-height: 1; padding: 0 6px 0 6px">
                全选/全不选:
                <el-switch
                  v-model="treeNodeAll"
                  active-text="是"
                  inactive-text="否"
                  inline-prompt
                  @change="handleCheckedTreeNodeAll"
                />
                全部展开/折叠:
                <el-switch
                  v-model="menuExpand"
                  active-text="展开"
                  inactive-text="折叠"
                  inline-prompt
                  @change="handleCheckedTreeExpand"
                />
              </div>
              <div style="height: 260px; overflow: auto; padding: 0 6px">
                <el-tree
                  ref="treeRef"
                  show-checkbox
                  node-key="id"
                  :data="menuData"
                  :props="defaultProps"
                />
              </div>
            </ele-loading>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :sm="24" :xs="24">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, watch } from 'vue';
  import { EleMessage } from 'sirius-platform-pro/es';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import * as MenuApi from '@/api/system/menu';
  import { defaultProps, handleTree } from '@/utils/tree';
  import * as TenantPackageApi from '@/api/system/tenantPackage';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });
  /** 菜单数据请求状态 */
  const menuLoading = ref(false);
  /** 菜单树组件 */
  const treeRef = ref(null);
  /** 菜单回显选中 */
  const checkedKeys = ref([]);
  /** 菜单数据 */
  const menuData = ref([]);
  /** 是否是修改 */
  const isUpdate = ref(false);
  const menuExpand = ref(false); // 展开/折叠
  const treeNodeAll = ref(false); // 全选/全不选
  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: null,
    name: null,
    remark: null,
    menuIds: [],
    status: 0
  });
  /** 全选/全不选 */
  const handleCheckedTreeNodeAll = () => {
    treeRef.value.setCheckedNodes(treeNodeAll.value ? menuData.value : []);
  };

  /** 展开/折叠全部 */
  const handleCheckedTreeExpand = () => {
    const nodes = treeRef.value?.store.nodesMap;
    for (let node in nodes) {
      if (nodes[node].expanded === menuExpand.value) {
        continue;
      }
      nodes[node].expanded = menuExpand.value;
    }
  };
  /** 表单验证规则 */
  const rules = reactive({
    name: [{ required: true, message: '权限名不能为空', trigger: 'blur' }]
  });
  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      const menuIds = [
        ...treeRef.value.getCheckedKeys(false), // 获得当前选中节点
        ...treeRef.value.getHalfCheckedKeys() // 获得半选中的父节点
      ];
      loading.value = true;
      const saveOrUpdate = isUpdate.value
        ? TenantPackageApi.updateTenantPackage
        : TenantPackageApi.createTenantPackage;
      saveOrUpdate({ ...form, menuIds })
        .then((msg) => {
          loading.value = false;
          EleMessage.success('保存成功');
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
        });
    });
  };
  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  /** 查询菜单数据 */
  const query = async () => {
    menuData.value = [];
    checkedKeys.value = [];
    menuLoading.value = true;
    menuData.value = handleTree(await MenuApi.getSimpleMenusList());
    if (props.data?.id) {
      try {
        const res = await TenantPackageApi.getTenantPackage(props.data?.id);
        assignFields({ ...res });
        res.menuIds.forEach((menuId) => {
          treeRef.value.setChecked(menuId, true, false);
        });
      } finally {
        menuLoading.value = false;
      }
    } else {
      menuLoading.value = false;
    }
  };
  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          isUpdate.value = true;
        } else {
          isUpdate.value = false;
        }
        query();
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>

<style lang="scss" scoped>
  .tenant-menu-tree {
    width: 100%;
    padding: 6px 0;
    border: 1px solid var(--el-border-color);
    border-radius: var(--el-border-radius-base);
    box-sizing: border-box;
    overflow: hidden;
  }
</style>
