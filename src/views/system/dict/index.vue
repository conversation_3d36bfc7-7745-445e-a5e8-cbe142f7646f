<template>
  <ele-page
    flex-table
    :multi-card="false"
    hide-footer
    style="min-height: 420px"
  >
    <el-row :gutter="16" style="height: 100%">
      <el-col :md="12" :sm="24" :xs="24" style="height: 100%">
        <ele-card flex-table style="height: 100%">
          <el-form
            style="margin-bottom: -14px"
            label-width="70px"
            @keyup.enter="reload"
          >
            <el-row :gutter="8">
              <el-col :lg="8" :md="8" :sm="12" :xs="24">
                <el-form-item label="字典名称">
                  <el-input clearable v-model.trim="queryParams.name" />
                </el-form-item>
              </el-col>
              <el-col :lg="8" :md="8" :sm="12" :xs="24">
                <el-form-item label="字典类型">
                  <el-input clearable v-model.trim="queryParams.type" />
                </el-form-item>
              </el-col>
              <el-col :lg="8" :md="8" :sm="12" :xs="24">
                <el-form-item label-width="16px">
                  <el-button type="primary" @click="reload">查询</el-button>
                  <el-button @click="reset">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <ele-pro-table
            ref="tableRef"
            row-key="id"
            :columns="columns"
            :datasource="datasource"
            :show-overflow-tooltip="true"
            v-model:current="current"
            highlight-current-row
            :row-style="{ cursor: 'pointer' }"
            :footer-style="{ paddingBottom: '16px' }"
          >
            <template #toolbar>
              <el-button
                type="primary"
                class="ele-btn-icon"
                :icon="PlusOutlined"
                @click="openEdit()"
              >
                新建
              </el-button>
            </template>
            <template #status="{ row }">
              <dict-data
                type="tag"
                :code="DICT_TYPE.COMMON_STATUS"
                :model-value="row.status"
              />
            </template>
            <template #action="{ row }">
              <el-link
                type="primary"
                :underline="false"
                @click.stop="openEdit(row)"
                v-permission="['system:dict:update']"
              >
                编辑
              </el-link>
              <el-divider
                direction="vertical"
                v-permission="['system:dict:delete']"
              />
              <el-link
                type="danger"
                :underline="false"
                @click.stop="remove(row)"
                v-permission="['system:dict:delete']"
              >
                删除
              </el-link>
            </template>
          </ele-pro-table>
        </ele-card>
      </el-col>
      <el-col :md="12" :sm="24" :xs="24" style="height: 100%">
        <dict-data-list :dict-type="current.type" />
      </el-col>
    </el-row>
    <!-- 编辑弹窗 -->
    <dict-edit v-model="showEdit" :data="editData" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { ref, nextTick, watch } from 'vue';
  import { PlusOutlined } from '@/components/icons';
  import { useMobile } from '@/utils/use-mobile';
  import { DICT_TYPE } from '@/utils/dict';
  import DictDataList from './components/dict-data-list.vue';
  import DictEdit from './components/dict-edit.vue';
  import * as DictTypeApi from '@/api/system/dict/dict.type';
  defineOptions({ name: 'SystemDict' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    name: '',
    type: ''
  });
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = ref([
    {
      prop: 'name',
      label: '字典名称',
      align: 'left',
      minWidth: 120
    },
    {
      prop: 'type',
      label: '字典类型',
      align: 'left',
      minWidth: 150
    },
    {
      prop: 'status',
      label: '状态',
      align: 'center',
      minWidth: 58,
      slot: 'status'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 120,
      fixed: 'right',
      align: 'center',
      slot: 'action',
      hideInPrint: true
    }
  ]);
  /** 选中数据 */
  const current = ref({ type: 'undefined' });
  /** 表格数据源 */
  const datasource = async ({ pages, where, filters }) => {
    const res = await DictTypeApi.getDictTypePage({
      ...where,
      ...filters,
      ...pages
    });
    return res;
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({
      page: 1,
      where: { ...queryParams }
    });
  };
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 编辑回显数据 */
  const editData = ref(null);

  /** 选择数据 */
  const onNodeClick = (row) => {
    console.log(row);
    // if (row && row.id) {
    //   current.value = row;
    // } else {
    //   current.value = null;
    // }
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    editData.value = row ?? null;
    showEdit.value = true;
  };

  /** 删除 */
  const remove = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await DictTypeApi.deleteDictType(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
</script>
