<!-- 字典数据编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="460"
    :title="title"
    destroy-on-close
    v-model="visible"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent=""
    >
      <el-form-item label="数据标签" prop="label">
        <el-input
          clearable
          :maxlength="20"
          v-model="form.label"
          placeholder="请输入数据标签"
        />
      </el-form-item>
      <el-form-item label="数据键值" prop="value">
        <el-input
          clearable
          :maxlength="20"
          v-model="form.value"
          placeholder="请输入数据键值"
        />
      </el-form-item>
      <el-form-item label="样式属性" prop="cssClass">
        <el-input
          clearable
          :maxlength="200"
          v-model="form.cssClass"
          placeholder="请输入样式属性"
        />
      </el-form-item>
      <el-form-item label="显示排序" prop="sort">
        <el-input-number
          :min="0"
          :max="9999"
          v-model="form.sort"
          placeholder="请输入显示排序"
          controls-position="right"
          class="ele-fluid"
        />
      </el-form-item>
      <el-form-item label="回显样式" prop="colorType">
        <el-select
          clearable
          v-model="form.colorType"
          placeholder="请选择回显样式"
          class="ele-fluid"
        >
          <el-option value="default" label="默认" />
          <el-option value="primary" label="主要" />
          <el-option value="success" label="成功" />
          <el-option value="info" label="信息" />
          <el-option value="warning" label="警告" />
          <el-option value="danger" label="危险" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注">
        <el-input
          :rows="4"
          type="textarea"
          :maxlength="200"
          v-model="form.remark"
          placeholder="请输入备注"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { ref, reactive, watch } from 'vue';
  import { EleMessage } from 'sirius-platform-pro/es';
  import { useFormData } from '@/utils/use-form-data';
  import * as DictDataApi from '@/api/system/dict/dict.data';

  const emit = defineEmits(['done', 'update:modelValue']);
  const title = ref(''); // 弹窗的标题

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object,
    /** 字典类型 */
    dictType: String
  });
  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });
  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    dictCode: undefined,
    label: undefined,
    value: undefined,
    cssClass: undefined,
    sort: undefined,
    colorType: undefined,
    status: 0,
    remark: undefined
  });
  /** 表单验证规则 */
  const rules = reactive({
    label: [
      {
        required: true,
        message: '请输入数据标签',
        type: 'string',
        trigger: 'blur'
      }
    ],
    value: [
      {
        required: true,
        message: '请输入数据键值',
        type: 'string',
        trigger: 'blur'
      }
    ],
    sort: [
      {
        required: true,
        message: '请输入显示排序',
        type: 'number',
        trigger: 'blur'
      }
    ]
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };
  /** 保存编辑 */
  const save = async () => {
    // 校验表单
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      const data = {
        ...form,
        dictType: props.dictType
      };
      if (!isUpdate.value) {
        await DictDataApi.createDictData(data);
        message.success(t('common.createSuccess'));
      } else {
        await DictDataApi.updateDictData(data);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };
  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    resetFields();
    try {
      if (props.data) {
        const result = await DictDataApi.getDictData(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
