<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <code-search @search="reload" />
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        cache-key="systemCodeTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            v-permission="['system:code:create']"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            新建
          </el-button>
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.COMMON_STATUS"
            :model-value="row.isEnable"
          />
        </template>
        <template #action="{ row }">
          <el-link
            type="primary"
            v-permission="['system:code:update']"
            :underline="false"
            @click="openEdit(row)"
          >
            修改
          </el-link>
          <el-divider
            v-permission="['system:code:update']"
            direction="vertical"
          />
          <el-link
            type="primary"
            v-permission="['system:code:update']"
            :underline="false"
            @click="openRuleFormEdit(row)"
          >
            编码规则
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" :underline="false" @click="testCode(row)">
            测试
          </el-link>
          <el-divider
            v-permission="['system:code:delete']"
            direction="vertical"
          />
          <el-link
            v-permission="['system:code:delete']"
            type="danger"
            :underline="false"
            @click="removeBatch(row)"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <code-edit v-model="showEdit" :data="current" @done="reload" />
    <!-- 规则明细 -->
    <code-rule-form v-model="showRuleFrom" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { ref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'sirius-platform-pro/es';
  import { PlusOutlined, DownloadOutlined } from '@/components/icons';
  import CodeSearch from './components/code-search.vue';
  import CodeEdit from './components/code-edit.vue';
  import CodeRuleForm from './components/code-rule-form.vue';
  import { CodeApi } from '@/api/system/code';
  import { dateFormatter } from '@/utils/formatTime';
  import { DICT_TYPE } from '@/utils/dict';
  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'code',
      label: '编码',
      align: 'center',
      minWidth: 100
    },
    {
      prop: 'description',
      label: '描述信息',
      align: 'center',
      minWidth: 150
    },
    {
      prop: 'isEnable',
      label: '状态',
      width: 90,
      align: 'center',
      minWidth: 50,
      slot: 'status'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 110,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 280,
      align: 'center',
      slot: 'action',
      hideInPrint: true
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 是否显示编码规则弹窗 */
  const showRuleFrom = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, orders }) => {
    return CodeApi.getCodePage({ ...where, ...orders, ...pages });
  };

  /** 搜索 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 打开规则明细弹窗 */
  const openRuleFormEdit = (row) => {
    current.value = row ?? null;
    showRuleFrom.value = true;
  };
  const testCode = async (row) => {
    const code = await CodeApi.testCode(row.code);
    EleMessage.success('生成成功，单号为：' + code);
  };
  /** 批量删除 */
  const removeBatch = (row) => {
    ElMessageBox.confirm(`是否确认删除当前数据项?`, '系统提示', {
      type: 'warning',
      draggable: true
    }).then(async () => {
      const loading = EleMessage.loading('正在删除..');
      try {
        await CodeApi.deleteCode(row.id);
        reload();
      } finally {
        loading.close();
      }
    });
  };
</script>
<script>
  export default {
    name: 'SystemCode'
  };
</script>
