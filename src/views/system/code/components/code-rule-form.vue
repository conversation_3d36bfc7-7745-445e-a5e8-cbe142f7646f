<template>
  <ele-drawer
    :size="1200"
    style="max-width: 100%"
    title="编码规则明细"
    :append-to-body="true"
    :destroy-on-close="true"
    :model-value="modelValue"
    @update:modelValue="updateModelValue"
    @open="open"
  >
    <el-form ref="formRef" :model="form" label-width="0px" @submit.prevent="">
      <div style="padding: 6px 0 12px 0">
        <el-button
          :icon="Plus"
          type="primary"
          class="ele-btn-icon"
          @click="add"
        >
          新增
        </el-button>
        <el-button
          :icon="Refresh"
          type="primary"
          class="ele-btn-icon"
          @click="getList"
        >
          刷新
        </el-button>
      </div>
      <ele-data-table
        row-key="index"
        :showHeader="true"
        :columns="columns"
        :data="form.list"
        cell-class-name="editable-table-cell"
        class="editable-table"
      >
        <template #sort="{ row, $index }">
          <el-form-item
            :prop="'list.' + $index + '.sort'"
            :rules="{
              required: true,
              message: '请输入排序号',
              type: 'number',
              trigger: 'blur'
            }"
            class="form-error-popper"
            style="margin-bottom: 0"
          >
            <el-input-number
              v-if="row.isEdit"
              clearable
              controls-position="right"
              :min="1"
              v-model="row.sort"
            />
            <div v-else class="editable-cell-text">{{ row.sort }}</div>
          </el-form-item>
        </template>
        <template #segmentType="{ row, $index }">
          <el-form-item
            :prop="'list.' + $index + '.segmentType'"
            :rules="{
              required: true,
              message: '请选择类型',
              type: 'string',
              trigger: 'change'
            }"
            class="form-error-popper"
            style="margin-bottom: 0"
          >
            <el-select
              v-if="row.isEdit"
              clearable
              v-model="row.segmentType"
              class="ele-fluid"
              @change="segmentTypeChange(row)"
            >
              <el-option
                v-for="(item, index) in segOptions"
                :key="index"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
            <div v-else class="editable-cell-text">
              <!-- segmentType翻译成描述 -->
              {{
                segOptions.find((item) => item.value == row.segmentType)?.label
              }}
            </div>
          </el-form-item>
        </template>
        <template #segmentValue="{ row, $index }">
          <el-form-item
            :prop="'list.' + $index + '.segmentValue'"
            :rules="{
              required: row.segmentType == 'CONSTANT',
              message: '请输入值',
              message: '请输入',
              type: 'string',
              trigger: 'change'
            }"
            class="form-error-popper"
            style="margin-bottom: 0"
          >
            <el-input
              v-if="row.isEdit && row.segmentType == 'CONSTANT'"
              clearable
              v-model="row.segmentValue"
            />
            <div v-else class="editable-cell-text">{{ row.segmentValue }}</div>
          </el-form-item>
        </template>
        <template #dateFormat="{ row, $index }">
          <el-form-item
            :prop="'list.' + $index + '.dateFormat'"
            :rules="{
              required: row.segmentType == 'DATE',
              message: '请选择日期格式',
              type: 'string',
              trigger: 'change'
            }"
            class="form-error-popper"
            style="margin-bottom: 0"
          >
            <el-select
              v-if="row.isEdit && row.segmentType == 'DATE'"
              clearable
              v-model="row.dateFormat"
              class="ele-fluid"
            >
              <el-option
                v-for="(item, index) in dateOptions"
                :key="index"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
            <div v-else class="editable-cell-text">
              {{
                dateOptions.find((item) => item.value == row.dateFormat)?.label
              }}
            </div>
          </el-form-item>
        </template>
        <template #length="{ row, $index }">
          <el-form-item
            :prop="'list.' + $index + '.length'"
            :rules="{
              required:
                row.segmentType == 'SEQUENCE' || row.segmentType == 'UUID',
              message: '请输入位数',
              type: 'number',
              trigger: 'change'
            }"
            class="form-error-popper"
            style="margin-bottom: 0"
          >
            <el-input-number
              v-if="
                row.isEdit &&
                (row.segmentType == 'SEQUENCE' || row.segmentType == 'UUID')
              "
              clearable
              controls-position="right"
              :min="1"
              v-model="row.length"
            />
            <div v-else class="editable-cell-text">{{ row.length }}</div>
          </el-form-item>
        </template>
        <template #incremental="{ row, $index }">
          <el-form-item
            :prop="'list.' + $index + '.incremental'"
            :rules="{
              required: row.segmentType == 'SEQUENCE',
              message: '请输入步长',
              type: 'number',
              trigger: 'change'
            }"
            class="form-error-popper"
            style="margin-bottom: 0"
          >
            <el-input-number
              v-if="row.isEdit && row.segmentType == 'SEQUENCE'"
              clearable
              controls-position="right"
              :min="1"
              v-model="row.incremental"
            />
            <div v-else class="editable-cell-text">{{ row.incremental }}</div>
          </el-form-item>
        </template>
        <template #resetFrequence="{ row, $index }">
          <el-form-item
            :prop="'list.' + $index + '.resetFrequence'"
            :rules="{
              required: row.segmentType == 'SEQUENCE',
              message: '请选择重置频率',
              type: 'string',
              trigger: 'change'
            }"
            class="form-error-popper"
            style="margin-bottom: 0"
          >
            <el-select
              v-if="row.isEdit && row.segmentType == 'SEQUENCE'"
              clearable
              v-model="row.resetFrequence"
              class="ele-fluid"
            >
              <el-option
                v-for="(item, index) in resetOptions"
                :key="index"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
            <div v-else class="editable-cell-text">
              {{
                resetOptions.find((item) => item.value == row.resetFrequence)
                  ?.label
              }}
            </div>
          </el-form-item>
        </template>
        <template #startValue="{ row, $index }">
          <el-form-item
            :prop="'list.' + $index + '.startValue'"
            :rules="{
              required: row.segmentType == 'SEQUENCE',
              message: '请输入步长',
              type: 'number',
              trigger: 'change'
            }"
            class="form-error-popper"
            style="margin-bottom: 0"
          >
            <el-input-number
              v-if="row.isEdit && row.segmentType == 'SEQUENCE'"
              clearable
              controls-position="right"
              :min="1"
              v-model="row.startValue"
            />
            <div v-else class="editable-cell-text"> {{ row.startValue }}</div>
          </el-form-item>
        </template>
        <template #action="{ row, $index }">
          <el-link
            v-if="row.isEdit"
            type="success"
            :underline="false"
            @click="saveLine(row)"
          >
            保存
          </el-link>
          <el-link v-else type="primary" :underline="false" @click="edit(row)">
            编辑
          </el-link>
          <el-divider direction="vertical" />
          <el-link
            type="danger"
            :underline="false"
            @click="remove(row, $index)"
          >
            删除
          </el-link>
        </template>
      </ele-data-table>
    </el-form>
  </ele-drawer>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'sirius-platform-pro/es';
  import { Plus, Refresh } from '@element-plus/icons-vue';
  import { CodeApi } from '@/api/system/code';

  const emit = defineEmits(['done', 'update:modelValue']);
  const message = useMessage(); // 消息弹窗
  const segOptions = [
    {
      value: 'CONSTANT',
      label: '常量'
    },
    {
      value: 'DATE',
      label: '日期'
    },
    {
      value: 'SEQUENCE',
      label: '序列号'
    },
    {
      value: 'UUID',
      label: '随机数'
    }
  ];
  const resetOptions = [
    {
      value: 'NEVER',
      label: '永不'
    },
    {
      value: 'DAY',
      label: '每日'
    },
    {
      value: 'MONTH',
      label: '每月'
    },
    {
      value: 'YEAR',
      label: '每年'
    }
  ];
  const dateOptions = [
    {
      value: 'yyyy',
      label: 'yyyy'
    },
    {
      value: 'MM',
      label: 'MM'
    },
    {
      value: 'dd',
      label: 'dd'
    },
    {
      value: 'yyyyMM',
      label: 'yyyyMM'
    },
    {
      value: 'yyyyMMdd',
      label: 'yyyyMMdd'
    },
    {
      value: 'MMdd',
      label: 'MMdd'
    }
  ];
  const props = defineProps({
    /** 是否显示 */
    modelValue: Boolean,
    /** 编码 */
    data: Object
  });

  const formRef = ref(null);
  /**
   * 成员
   */
  export interface CodeRuleItem {
    id?: number;
    codeId?: number;
    code?: string;
    sort?: number;
    segmentValue?: string;
    dateFormat?: string;
    length?: string;
    incremental?: number;
    resetFrequence?: string;
    startValue?: number;
    segmentType?: string;
    /** 是否编辑状态 */
    isEdit?: boolean;
  }

  interface FormData {
    list: CodeRuleItem[];
  }

  /** 表单数据 */
  const form = reactive<FormData>({
    list: []
  });
  /** 添加 */
  const add = () => {
    form.list.push({
      codeId: props.data.id,
      code: props.data.code,
      isEdit: true
    });
  };

  /** 表格列配置 */
  const columns = [
    {
      prop: 'sort',
      label: '排序',
      minWidth: 140,
      slot: 'sort'
    },
    {
      prop: 'segmentType',
      label: '段',
      minWidth: 130,
      slot: 'segmentType'
    },
    {
      prop: 'segmentValue',
      label: '段值',
      minWidth: 130,
      slot: 'segmentValue'
    },
    {
      prop: 'dateFormat',
      label: '日期格式',
      minWidth: 140,
      slot: 'dateFormat'
    },
    {
      prop: 'length',
      label: '位数',
      minWidth: 140,
      slot: 'length'
    },
    {
      prop: 'incremental',
      label: '步长',
      minWidth: 160,
      slot: 'incremental'
    },
    {
      prop: 'startValue',
      label: '开始值',
      minWidth: 160,
      slot: 'startValue'
    },
    {
      prop: 'resetFrequence',
      label: '重置频率',
      minWidth: 160,
      slot: 'resetFrequence'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 130,
      align: 'center',
      slot: 'action',
      fixed: 'right',
      hideInPrint: true
    }
  ];
  const segmentTypeChange = (row) => {
    row.segmentValue = undefined;
    row.dateFormat = undefined;
    row.length = undefined;
    row.incremental = undefined;
    row.startValue = undefined;
    row.resetFrequence = undefined;
  };
  const getList = async () => {
    const loading = EleMessage.loading('查询中..');
    try {
      const result = await CodeApi.getCodeRuleList({
        codeId: props.data?.id
      });
      //循环设置code
      form.list = result.map((item) => {
        item.code = props.data.code;
        return item;
      });
    } finally {
      loading.close();
    }
  };
  const open = async () => {
    getList();
  };
  /** 编辑 */
  const edit = (row) => {
    row.isEdit = true;
  };

  /** 完成 */
  const saveLine = async (row) => {
    const valid = await formRef.value.validate();
    if (!valid) return;
    row.isEdit = false;
    const id = await CodeApi.updateLineCodeRule(row);
    message.success();
    row.id = id;
  };

  /** 批量删除 */
  const remove = (row, index) => {
    ElMessageBox.confirm(`确认要删除当前数据吗?`, '系统提示', {
      type: 'warning',
      draggable: true
    })
      .then(async () => {
        const loading = EleMessage.loading('请求中..');
        try {
          if (row.id) {
            await CodeApi.deleteCodeRule(row.id);
            EleMessage.success('删除成功');
          }
          //从列表移除
          console.log(row);
          form.list.splice(index, 1);
        } finally {
          loading.close();
        }
      })
      .catch(() => {});
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
</script>
<style lang="scss" scoped>
  .editable-table :deep(.editable-table-cell) {
    position: static;

    & > .cell {
      overflow: visible;
    }
  }
  //el-table-fixed-column--right样式重写，z-index=9999
  .editable-table :deep(.el-table-fixed-column--right) {
    z-index: 999;
  }
  .editable-cell-text {
    width: 100%;
    min-height: 32px;
    box-sizing: border-box;
  }

  .form-error-popper.is-error .editable-cell-text {
    border: 1px dashed var(--el-color-danger);
    border-radius: var(--el-border-radius-base);
  }

  /* 表单验证气泡形式 */
  .form-error-popper.el-form-item > :deep(.el-form-item__content) {
    & > .el-form-item__error {
      position: absolute;
      left: 0;
      top: calc(0px - 100% - 6px);
      width: max-content;
      color: #fff;
      font-size: 12px;
      background: var(--el-color-danger);
      transition: all 0.2s;
      padding: 10px;
      border-radius: 4px;
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      pointer-events: none;

      &:after {
        content: '';
        border: 6px solid transparent;
        border-top-color: var(--el-color-danger);
        position: absolute;
        left: 12px;
        bottom: -11px;
      }
    }

    &:hover > .el-form-item__error {
      opacity: 1;
      visibility: visible;
      pointer-events: all;
    }
  }

  .editable-table :deep(tbody > tr:first-child) {
    .el-form-item > .el-form-item__content > .el-form-item__error {
      bottom: calc(0px - 100% - 6px);
      top: auto;

      &:after {
        top: -11px;
        bottom: auto;
        border-bottom-color: var(--el-color-danger);
        border-top-color: transparent;
      }
    }

    &:last-child .el-form-item > .el-form-item__content > .el-form-item__error {
      bottom: calc(0px - 100% - 6px + 32px);
    }
  }
</style>
