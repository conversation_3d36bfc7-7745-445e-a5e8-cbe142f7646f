<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="740"
    :model-value="modelValue"
    :title="isUpdate ? '修改菜单' : '新建菜单'"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent=""
    >
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="上级菜单" prop="parentId">
            <menu-select v-model="form.parentId" />
          </el-form-item>
          <el-form-item label="菜单名称" prop="name">
            <el-input
              clearable
              :maxlength="20"
              v-model="form.name"
              placeholder="请输入菜单名称"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="菜单类型" prop="type">
            <el-radio-group v-model="form.type" @change="onMenuTypeChange">
              <el-radio-button
                v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_MENU_TYPE)"
                :key="dict.label"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="打开方式">
            <el-radio-group
              v-model="form.openType"
              :disabled="form.type == '1' || form.type == '3'"
              @change="onOpenTypeChange"
            >
              <el-radio :value="0" label="组件" />
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider style="margin: 8px 0 22px 0; opacity: 0.6" />
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="菜单图标" prop="icon">
            <icon-select
              clearable
              filterable="popper"
              :popper-width="460"
              :popper-height="388"
              :popper-options="{ strategy: 'fixed' }"
              placeholder="请选择图标"
              v-model="form.icon"
              :disabled="form.type === 3"
            />
          </el-form-item>
          <el-form-item prop="path">
            <template #label>
              <ele-tooltip
                v-if="form.openType === 2"
                content="需要以`http://`、`https://`、`//`开头"
                placement="top-start"
                :popper-options="{
                  modifiers: [
                    { name: 'offset', options: { offset: [-12, 10] } }
                  ]
                }"
              >
                <el-icon
                  :size="15"
                  style="align-self: center; margin-right: 4px; cursor: help"
                >
                  <QuestionCircleOutlined style="opacity: 0.6" />
                </el-icon>
              </ele-tooltip>
              <span>{{ form.openType === 2 ? '外链地址' : '路由地址' }}</span>
            </template>
            <el-input
              clearable
              :maxlength="100"
              v-model="form.path"
              :disabled="form.type == '3'"
              :placeholder="
                form.openType === 2 ? '请输入外链地址' : '请输入路由地址'
              "
            />
          </el-form-item>
          <el-form-item prop="component">
            <template #label>
              <ele-tooltip
                v-if="form.openType === 1"
                content="需要以`http://`、`https://`、`//`开头"
                placement="top-start"
                :popper-options="{
                  modifiers: [
                    { name: 'offset', options: { offset: [-12, 10] } }
                  ]
                }"
              >
                <el-icon
                  :size="15"
                  style="align-self: center; margin-right: 4px; cursor: help"
                >
                  <QuestionCircleOutlined style="opacity: 0.6" />
                </el-icon>
              </ele-tooltip>
              <span>{{ form.openType === 1 ? '内链地址' : '组件路径' }}</span>
            </template>
            <el-input
              clearable
              :maxlength="100"
              v-model="form.component"
              :disabled="
                form.type == '1' || form.type == '3' || form.openType === 2
              "
              :placeholder="
                form.openType === 1 ? '请输入内链地址' : '请输入组件路径'
              "
            />
          </el-form-item>
          <el-form-item label="路由参数" prop="query">
            <template #label>
              <ele-tooltip>
                <el-icon
                  :size="15"
                  style="align-self: center; margin-right: 4px; cursor: help"
                >
                  <QuestionCircleOutlined style="opacity: 0.6" />
                </el-icon>
                <template #content>
                  访问路由的默认传递参数, 如: {"id": 1, "name": "ry"}
                </template>
              </ele-tooltip>
              <span>路由参数</span>
            </template>
            <el-input
              clearable
              :maxlength="200"
              v-model="form.query"
              :disabled="
                form.type == '1' ||
                form.type == '3' ||
                form.openType === 2 ||
                form.openType === 1
              "
              placeholder="请输入路由参数"
            />
          </el-form-item>
          <el-form-item label="菜单状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                :key="dict.label"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="权限字符" prop="permission">
            <el-input
              clearable
              v-model="form.permission"
              placeholder="请输入权限字符"
              :disabled="
                form.type == '1' || (form.type == '2' && form.openType === 2)
              "
            />
          </el-form-item>
          <el-form-item label="显示排序" prop="sort">
            <el-input-number
              :min="0"
              :max="99999"
              v-model="form.sort"
              placeholder="请输入显示排序"
              controls-position="right"
              class="ele-fluid"
            />
          </el-form-item>
          <el-form-item label="显示状态" prop="visible">
            <template #label>
              <ele-tooltip
                content="选择不展示只注册路由不展示在侧边栏, 比如添加页面应该选择不展示"
                :popper-style="{ maxWidth: '240px' }"
              >
                <el-icon
                  :size="15"
                  style="align-self: center; margin-right: 4px; cursor: help"
                >
                  <QuestionCircleOutlined style="opacity: 0.6" />
                </el-icon>
              </ele-tooltip>
              <span>显示状态</span>
            </template>
            <el-radio-group v-model="form.visible">
              <el-radio key="true" :value="true">显示</el-radio>
              <el-radio key="false" :value="false">隐藏</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否缓存" prop="keepAlive">
            <el-radio-group
              v-model="form.keepAlive"
              :disabled="
                form.type == '1' || form.type == '3' || form.openType === 2
              "
            >
              <el-radio key="true" :value="true" label="缓存" />
              <el-radio key="false" :value="false" label="不缓存" />
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, watch } from 'vue';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { EleMessage, isExternalLink } from 'sirius-platform-pro/es';
  import { QuestionCircleOutlined } from '@/components/icons';
  import { useFormData } from '@/utils/use-form-data';
  import IconSelect from '@/components/IconSelect/index.vue';
  import MenuSelect from './menu-select.vue';
  import { updateMenu, createMenu } from '@/api/system/menu';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object,
    /** 上级菜单id */
    parentId: Number
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    parentId: void 0,
    name: '',
    type: 1,
    openType: 0,
    icon: '',
    path: '',
    component: '',
    query: '',
    status: 0,
    permission: '',
    sort: void 0,
    visible: true,
    keepAlive: true
  });

  /** 表单验证规则 */
  const rules = reactive({
    name: [
      {
        required: true,
        type: 'string',
        message: '请输入菜单名称',
        trigger: 'blur'
      }
    ],
    sort: [
      {
        required: true,
        type: 'number',
        message: '请输入排序号',
        trigger: 'blur'
      }
    ],
    query: [
      {
        type: 'number',
        validator: (_rule, value, callback) => {
          if (value) {
            const msg = '请输入正确的参数格式';
            try {
              const obj = JSON.parse(value);
              if (obj == null || typeof obj !== 'object') {
                return callback(msg);
              }
            } catch (_e) {
              return callback(msg);
            }
          }
          callback();
        },
        trigger: 'blur'
      }
    ]
  });

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      const saveOrUpdate = isUpdate.value ? updateMenu : createMenu;
      saveOrUpdate({ ...form, isFrame: form.openType == 2 ? '0' : '1' })
        .then((msg) => {
          loading.value = false;
          EleMessage.success('保存成功');
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error('保存失败');
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  /** 菜单类型选择改变 */
  const onMenuTypeChange = () => {
    if (form.type == '1') {
      form.permission = '';
      form.openType = 0;
      form.component = '';
      form.keepAlive = true;
      form.query = '';
      formRef.value?.clearValidate?.('query');
    } else if (form.type == '3') {
      form.openType = 0;
      form.icon = '';
      form.path = '';
      form.component = '';
      form.visible = true;
      form.keepAlive = true;
      form.query = '';
      formRef.value?.clearValidate?.('query');
    }
  };

  /** openType选择改变 */
  const onOpenTypeChange = () => {
    if (form.openType === 2) {
      form.component = '';
      form.permission = '';
      form.keepAlive = true;
      form.query = '';
      formRef.value?.clearValidate?.('query');
    } else if (form.openType === 1) {
      form.query = '';
      formRef.value?.clearValidate?.('query');
    }
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          const isExternal = props.data.isFrame == '0';
          const isInner = isExternalLink(props.data.component);
          assignFields({
            ...props.data,
            type: isExternal ? 2 : props.data.type,
            openType: isExternal ? 2 : isInner ? 1 : 0
          });
          isUpdate.value = true;
        } else {
          form.parentId = props.parentId ? props.parentId : 0;
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>
