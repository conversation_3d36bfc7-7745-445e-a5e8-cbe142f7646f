<template>
  <ele-modal
    form
    :width="680"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <el-form-item label="小程序ID" prop="appId">
        <el-input v-model="form.appId" placeholder="请输入小程序APPID" />
      </el-form-item>
      <el-form-item label="小程序标题" prop="title">
        <el-input v-model="form.title" placeholder="请输入小程序标题" />
      </el-form-item>
      <el-form-item label="小程序默认页面" prop="page">
        <el-input v-model="form.page" placeholder="请输入小程序打开默认页面" />
      </el-form-item>
      <el-form-item label="小程序图标" prop="image">
        <UploadImg v-model="form.image" />
      </el-form-item>
      <el-form-item label="显示顺序" prop="sort">
        <el-input v-model="form.sort" placeholder="请输入显示顺序" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import * as MiniProgramLinksApi from '@/api/system/miniProgramLinks';
  import { useFormData } from '@/utils/use-form-data';

  /** 外部小程序链接 表单 */
  defineOptions({ name: 'MiniProgramLinksForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    appId: undefined,
    title: undefined,
    page: undefined,
    image: undefined,
    sort: undefined,
    status: undefined
  });
  /** 表单验证规则 */
  const rules = reactive({
    appId: [{ required: true, message: '小程序id不能为空', trigger: 'blur' }],
    title: [{ required: true, message: '小程序标题不能为空', trigger: 'blur' }],
    image: [{ required: true, message: '小程序图片不能为空', trigger: 'blur' }],
    status: [
      {
        required: true,
        message: '状态不能为空',
        trigger: 'blur'
      }
    ]
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        await MiniProgramLinksApi.createMiniProgramLinks(form);
        message.success(t('common.createSuccess'));
      } else {
        await MiniProgramLinksApi.updateMiniProgramLinks(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('success');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await MiniProgramLinksApi.getMiniProgramLinks(
          props.data.id
        );
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
