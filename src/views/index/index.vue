<template>
  <ele-page class="dashboard-container">
    <!-- 顶部区域：时间筛选 + 统计卡片 -->
    <div class="dashboard-header">
      <div class="header-top">
        <div class="header-left">
          <div class="date-info">
            <span class="current-date">{{ currentDate }}</span>
          </div>
        </div>
        <div class="header-right">
          <div class="header-controls">
            <el-select
              v-model="selectedPeriod"
              placeholder="全部项目"
              size="small"
            >
              <el-option label="全部项目" value="all" />
              <el-option label="项目A" value="projectA" />
              <el-option label="项目B" value="projectB" />
            </el-select>
            <el-button type="primary" size="small">房源业态</el-button>
            <el-button size="small">高级</el-button>
          </div>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="top-stats-grid">
        <!-- 运营收入 -->
        <div class="top-stat-card primary">
          <div class="stat-header">
            <div class="stat-left">
              <div class="stat-icon">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stat-title">运营收入(万)</div>
            </div>
            <div class="stat-trend positive">+5.2%</div>
          </div>
          <div class="stat-main">
            <div class="stat-value">138235.53</div>
          </div>
          <div class="stat-footer">
            <div class="stat-info">
              <div class="stat-label">商业</div>
              <div class="stat-sub">138914</div>
            </div>
          </div>
        </div>

        <!-- 实际出租面积 -->
        <div class="top-stat-card success">
          <div class="stat-header">
            <div class="stat-left">
              <div class="stat-icon">
                <el-icon><House /></el-icon>
              </div>
              <div class="stat-title">实际出租面积(㎡)</div>
            </div>
            <div class="stat-trend negative">-2.1%</div>
          </div>
          <div class="stat-main">
            <div class="stat-value">47311.97</div>
          </div>
          <div class="stat-footer">
            <div class="stat-info">
              <div class="stat-label">出租率</div>
              <div class="stat-sub">34.29%</div>
            </div>
          </div>
        </div>

        <!-- 房源个数 -->
        <div class="top-stat-card warning">
          <div class="stat-header">
            <div class="stat-left">
              <div class="stat-icon">
                <el-icon><OfficeBuilding /></el-icon>
              </div>
              <div class="stat-title">房源个数(套/间)</div>
            </div>
            <div class="stat-trend positive">+12.5%</div>
          </div>
          <div class="stat-main">
            <div class="stat-value">7</div>
          </div>
          <div class="stat-footer">
            <div class="stat-info">
              <div class="stat-label">新增</div>
              <div class="stat-sub">1</div>
            </div>
          </div>
        </div>

        <!-- 未来收入 -->
        <div class="top-stat-card info">
          <div class="stat-header">
            <div class="stat-left">
              <div class="stat-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-title">未来收入预测(万)</div>
            </div>
            <div class="stat-trend neutral">0%</div>
          </div>
          <div class="stat-main">
            <div class="stat-value">0</div>
          </div>
          <div class="stat-footer">
            <div class="stat-info">
              <div class="stat-label">续租</div>
              <div class="stat-sub">0</div>
            </div>
          </div>
        </div>

        <!-- 平均租金 -->
        <div class="top-stat-card purple">
          <div class="stat-header">
            <div class="stat-left">
              <div class="stat-icon">
                <el-icon><Coin /></el-icon>
              </div>
              <div class="stat-title">平均租金(元/㎡/天)</div>
            </div>
            <div class="stat-trend negative">-3.8%</div>
          </div>
          <div class="stat-main">
            <div class="stat-value">1.93</div>
          </div>
          <div class="stat-footer">
            <div class="stat-info">
              <div class="stat-label">上月</div>
              <div class="stat-sub">1.98</div>
            </div>
          </div>
        </div>

        <!-- 出租率 -->
        <div class="top-stat-card success">
          <div class="stat-header">
            <div class="stat-left">
              <div class="stat-icon">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-title">出租、收缴、续租率(%)</div>
            </div>
            <div class="stat-trend positive">+1.2%</div>
          </div>
          <div class="stat-main">
            <div class="stat-value">34.23</div>
          </div>
          <div class="stat-footer">
            <div class="stat-info">
              <div class="stat-label">收缴率</div>
              <div class="stat-sub">91.77</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 中间区域：左侧指标卡片 + 右侧出租率排行 -->
    <div class="middle-section">
      <!-- 左侧：指标卡片区域 -->
      <div class="middle-metrics-section">
        <div class="metrics-panel">
          <div class="metrics-header">
            <div class="section-controls">
              <el-button-group size="small">
                <el-button type="primary">合计</el-button>
                <el-button>今天</el-button>
                <el-button>7天</el-button>
                <el-button>30天</el-button>
                <el-button>日</el-button>
                <el-button>周</el-button>
                <el-button>月</el-button>
                <el-button>年</el-button>
              </el-button-group>
            </div>
          </div>
          <div class="middle-metrics-grid">
            <div
              v-for="(metric, index) in middleMetrics"
              :key="index"
              class="middle-metric-card"
              :class="`metric-card-${metric.type}`"
            >
              <div class="metric-card-inner">
                <div class="metric-header">
                  <div
                    class="metric-icon-wrapper"
                    :style="{ backgroundColor: metric.iconBg }"
                  >
                    <component :is="metric.icon" class="metric-icon" />
                  </div>
                  <div class="metric-title">{{ metric.label }}</div>
                </div>

                <div class="metric-content">
                  <div class="metric-value">{{ metric.value }}</div>
                  <div class="metric-detail" v-if="metric.detail">{{
                    metric.detail
                  }}</div>
                </div>

                <div
                  class="metric-accent"
                  :style="{ backgroundColor: metric.accentColor }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：出租率排行 -->
      <div class="chart-panel ranking-chart">
        <div class="panel-header">
          <h3>出租率排行</h3>
          <el-link type="primary" size="small">查看全部 ></el-link>
        </div>
        <div class="ranking-content">
          <div class="ranking-list">
            <div
              v-for="(item, index) in rentRankingList"
              :key="index"
              class="ranking-item"
            >
              <div class="ranking-info">
                <span class="ranking-name">{{ item.name }}</span>
                <span class="ranking-percentage">{{ item.percentage }}%</span>
              </div>
              <div class="ranking-bar">
                <div
                  class="bar-fill"
                  :style="{
                    width: item.percentage + '%',
                    backgroundColor: item.color
                  }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部图表区域 -->
    <div class="bottom-charts-section">
      <!-- 左侧：月度收支图表 -->
      <div class="chart-panel monthly-chart">
        <div class="panel-header">
          <h3>财务收支</h3>
        </div>
        <div class="chart-content">
          <v-chart
            ref="monthlyIncomeChartRef"
            :option="monthlyIncomeChartOption"
            style="height: 320px"
          />
        </div>
      </div>

      <!-- 右侧：房源业态饼图 -->
      <div class="chart-panel property-type-chart">
        <div class="panel-header">
          <h3>房源业态</h3>
        </div>
        <div class="chart-content">
          <v-chart
            ref="propertyTypeChartRef"
            :option="propertyTypeChartOption"
            style="height: 280px"
          />
        </div>
      </div>

      <!-- 最右侧：出租情况饼图 -->
      <div class="chart-panel rent-status-chart">
        <div class="panel-header">
          <h3>出租情况</h3>
        </div>
        <div class="chart-content">
          <v-chart
            ref="rentStatusChartRef"
            :option="rentStatusChartOption"
            style="height: 280px"
          />
        </div>
      </div>
    </div>
  </ele-page>
</template>
<script lang="ts" setup>
  import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
  import {
    House,
    Money,
    User,
    DataLine,
    Calendar,
    TrendCharts,
    OfficeBuilding,
    Coin
  } from '@element-plus/icons-vue';
  import VChart from 'vue-echarts';
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { PieChart, LineChart } from 'echarts/charts';
  import {
    TitleComponent,
    TooltipComponent,
    LegendComponent,
    GridComponent
  } from 'echarts/components';

  use([
    CanvasRenderer,
    PieChart,
    LineChart,
    TitleComponent,
    TooltipComponent,
    LegendComponent,
    GridComponent
  ]);
  import dayjs from 'dayjs';

  defineOptions({ name: 'Dashboard' });

  // 响应式数据
  const selectedPeriod = ref('all');
  const rentStatusChartRef = ref();
  const propertyTypeChartRef = ref();
  const monthlyIncomeChartRef = ref();

  // 当前日期 - 实时更新
  const currentDate = ref(dayjs().format('YYYY年MM月DD日 dddd - HH:mm:ss'));
  let dateTimer: number | null = null;

  // 更新日期时间
  const updateDateTime = () => {
    currentDate.value = dayjs().format('YYYY年MM月DD日 dddd - HH:mm:ss');
  };

  // 组件挂载时启动定时器
  onMounted(() => {
    // 每秒更新一次时间
    dateTimer = setInterval(updateDateTime, 1000);
  });

  // 组件卸载时清除定时器
  onUnmounted(() => {
    if (dateTimer) {
      clearInterval(dateTimer);
      dateTimer = null;
    }
  });

  // 中间指标数据
  const middleMetrics = reactive([
    {
      icon: Money,
      value: '256,197',
      label: '本月实收-收款(万元)',
      detail: '今日新增收款 0万 | 完成率 85%',
      type: 'income',
      iconBg: '#3b82f6',
      accentColor: '#3b82f6',
      trend: 12.5
    },
    {
      icon: DataLine,
      value: '59,522',
      label: '本月实收(万元)',
      detail: '今日新增收款 0万 | 环比上月 +8.3%',
      type: 'revenue',
      iconBg: '#f59e0b',
      accentColor: '#f59e0b',
      trend: 8.3
    },
    {
      icon: House,
      value: '316.75',
      label: '本月应收-收款(万元)',
      detail: '今日新增收款 0.0000 | 待收款 316.75万',
      type: 'receivable',
      iconBg: '#10b981',
      accentColor: '#10b981',
      trend: -2.1
    },
    {
      icon: TrendCharts,
      value: '0.00',
      label: '本月债付(万元)',
      detail: '今日新增收款 0万 | 无待付款项',
      type: 'debt',
      iconBg: '#8b5cf6',
      accentColor: '#8b5cf6',
      trend: 0
    },
    {
      icon: Calendar,
      value: '0%',
      label: '现收款率(%)',
      detail: '今日新增收款 0万 | 目标 95%',
      type: 'rate',
      iconBg: '#06b6d4',
      accentColor: '#06b6d4',
      trend: 5.2
    },
    {
      icon: User,
      value: '36.78',
      label: '本月新增(万)',
      detail: '今日新增收款 5002.86 | 新增面积',
      type: 'new',
      iconBg: '#84cc16',
      accentColor: '#84cc16',
      trend: 15.7
    },
    {
      icon: Money,
      value: '606.08',
      label: '本月费用(万)',
      detail: '今日费用 0 | 运营成本控制良好',
      type: 'expense',
      iconBg: '#3b82f6',
      accentColor: '#3b82f6',
      trend: -3.4
    },
    {
      icon: DataLine,
      value: '35.71',
      label: '本月退费(万)',
      detail: '今日费用 0 | 退费率 2.1%',
      type: 'refund',
      iconBg: '#f59e0b',
      accentColor: '#f59e0b',
      trend: -8.9
    },
    {
      icon: House,
      value: '1.83',
      label: '在租单价(元/㎡/天)',
      detail: '今日新增收款 0万 | 市场均价 2.1元',
      type: 'price',
      iconBg: '#10b981',
      accentColor: '#10b981',
      trend: 4.2
    },
    {
      icon: TrendCharts,
      value: '0',
      label: '维修(笔)',
      detail: '今日新增收款 0笔 | 维修完成率 100%',
      type: 'maintenance',
      iconBg: '#ef4444',
      accentColor: '#ef4444',
      trend: 0
    },
    {
      icon: Calendar,
      value: '39',
      label: '在租户数',
      detail: '今日新增 0户 | 入住率 78%',
      type: 'tenants',
      iconBg: '#f97316',
      accentColor: '#f97316',
      trend: 7.1
    },
    {
      icon: User,
      value: '0',
      label: '投诉(笔)',
      detail: '今日投诉 0笔 | 满意度 98%',
      type: 'complaints',
      iconBg: '#ef4444',
      accentColor: '#ef4444',
      trend: 0
    }
  ]);

  // 出租情况饼图配置
  const rentStatusChartOption = reactive({
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}% ({d}%)'
    },
    legend: {
      show: true,
      orient: 'vertical',
      left: 'right',
      top: 'center',
      textStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '出租情况',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}\n{c}%',
          fontSize: 12
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10
        },
        data: [
          { value: 34.23, name: '已出租', itemStyle: { color: '#5B8FF9' } },
          { value: 65.77, name: '空置', itemStyle: { color: '#E6F7FF' } }
        ]
      }
    ]
  });

  // 房源业态饼图配置
  const propertyTypeChartOption = reactive({
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}% ({d}%)'
    },
    legend: {
      show: true,
      orient: 'vertical',
      left: 'right',
      top: 'center',
      textStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '房源业态',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}\n{c}%',
          fontSize: 12
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10
        },
        data: [
          { value: 65.77, name: '办公', itemStyle: { color: '#5B8FF9' } },
          { value: 34.23, name: '商业', itemStyle: { color: '#FF9845' } }
        ]
      }
    ]
  });

  // 月度收支折线图配置
  const monthlyIncomeChartOption = reactive({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [
        '5月',
        '6月',
        '7月',
        '8月',
        '9月',
        '10月',
        '11月',
        '12月',
        '1月',
        '2月',
        '3月',
        '4月'
      ]
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}'
      }
    },
    series: [
      {
        name: '支出',
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#FFD700',
          width: 2
        },
        itemStyle: {
          color: '#FFD700'
        },
        data: [300, 280, 350, 400, 500, 450, 300, 250, 400, 500, 600, 200]
      },
      {
        name: '支出',
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#409EFF',
          width: 2
        },
        itemStyle: {
          color: '#409EFF'
        },
        data: [200, 180, 250, 300, 400, 350, 200, 150, 300, 400, 500, 100]
      },
      {
        name: '收入',
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#10B981',
          width: 2
        },
        itemStyle: {
          color: '#10B981'
        },
        data: [120, 130, 260, 320, 400, 350, 200, 150, 300, 400, 500, 100]
      }
    ]
  });

  // 出租率排行数据
  const rentRankingList = reactive([
    { name: '数字化运营中心(栋)', percentage: 47.47, color: '#8B5CF6' },
    { name: '出租率排行中心(栋)', percentage: 29.71, color: '#10B981' },
    { name: '数字化运营中心(栋)', percentage: 26.89, color: '#F59E0B' }
  ]);
</script>

<style lang="scss" scoped>
  // CSS变量定义 - 支持系统级黑夜模式
  .dashboard-container {
    // 亮色模式变量（默认）
    --bg-primary: #f5f7fa;
    --bg-secondary: #ffffff;
    --bg-tertiary: #f8fafc;
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --text-tertiary: #9ca3af;
    --border-color: #e5e7eb;
    --border-light: #f1f5f9;
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md:
      0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 4px 15px rgba(0, 0, 0, 0.12);

    background-color: var(--bg-primary);
    transition: background-color 0.3s ease;

    // 响应式设计
    @media (max-width: 1400px) {
      padding: 14px;
    }

    @media (max-width: 1200px) {
      padding: 12px;
    }

    @media (max-width: 768px) {
      padding: 10px;
    }

    @media (max-width: 480px) {
      padding: 8px;
    }
  }

  .dashboard-header {
    background: var(--bg-secondary);
    padding: 12px;
    border-radius: 16px;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    margin-bottom: 12px;

    .header-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }

    // 响应式设计
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 12px;
      padding: 12px 16px;
    }

    @media (max-width: 480px) {
      padding: 10px 12px;
    }

    .header-left {
      .date-info {
        display: flex;
        align-items: center;
        gap: 12px;

        @media (max-width: 480px) {
          flex-direction: column;
          gap: 4px;
          align-items: flex-start;
        }

        .current-date {
          font-size: 14px;
          font-weight: 500;
          color: var(--text-primary);
          transition: color 0.3s ease;

          @media (max-width: 480px) {
            font-size: 13px;
          }
        }

        .weather-info {
          font-size: 12px;
          color: var(--text-secondary);
          background: var(--bg-tertiary);
          padding: 2px 8px;
          border-radius: 4px;
          transition: all 0.3s ease;

          @media (max-width: 480px) {
            font-size: 11px;
            padding: 1px 6px;
          }
        }
      }
    }

    .header-right {
      .header-controls {
        display: flex;
        align-items: center;
        gap: 8px;

        @media (max-width: 768px) {
          justify-content: center;
          width: 100%;
        }

        @media (max-width: 480px) {
          gap: 6px;
          flex-wrap: wrap;
        }
      }
    }

    // 顶部统计卡片
    .top-stats-grid {
      display: grid;
      grid-template-columns: repeat(6, 1fr);
      gap: 8px;

      @media (max-width: 1400px) {
        gap: 14px;
      }

      @media (max-width: 1200px) {
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
      }

      @media (max-width: 768px) {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
      }

      @media (max-width: 480px) {
        grid-template-columns: 1fr;
        gap: 8px;
      }

      .top-stat-card {
        border-radius: 12px;
        padding: 8px 12px;
        transition: all 0.3s ease;
        position: relative;
        min-height: 100px;
        overflow: hidden;
        color: white;

        @media (max-width: 768px) {
          padding: 16px;
          min-height: 90px;
        }

        @media (max-width: 480px) {
          padding: 12px;
          min-height: 80px;
        }

        // 右下角装饰图案
        &::after {
          content: '';
          position: absolute;
          right: -10px;
          bottom: -10px;
          width: 60px;
          height: 60px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 50%;

          @media (max-width: 480px) {
            width: 40px;
            height: 40px;
            right: -5px;
            bottom: -5px;
          }
        }

        &::before {
          content: '';
          position: absolute;
          right: 10px;
          bottom: 10px;
          width: 30px;
          height: 30px;
          background: rgba(255, 255, 255, 0.15);
          border-radius: 4px;
          transform: rotate(45deg);

          @media (max-width: 480px) {
            width: 20px;
            height: 20px;
            right: 8px;
            bottom: 8px;
          }
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        // 不同类型的背景色
        &.primary {
          background: linear-gradient(135deg, #6366f1, #4f46e5);
        }
        &.success {
          background: linear-gradient(135deg, #10b981, #059669);
        }
        &.warning {
          background: linear-gradient(135deg, #f59e0b, #d97706);
        }
        &.info {
          background: linear-gradient(135deg, #06b6d4, #0891b2);
        }
        &.danger {
          background: linear-gradient(135deg, #ef4444, #dc2626);
        }
        &.purple {
          background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        }

        .stat-header {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          margin-bottom: 12px;

          .stat-left {
            display: flex;
            align-items: center;
            gap: 8px;

            .stat-icon {
              width: 20px;
              height: 20px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 13px;
              color: rgba(255, 255, 255, 0.9);
            }

            .stat-title {
              font-size: 14px;
              font-weight: 500;
              color: rgba(255, 255, 255, 0.9);
              white-space: nowrap;
            }
          }

          .stat-trend {
            font-size: 11px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.8);
          }
        }

        .stat-main {
          text-align: left;
          margin-bottom: 6px;
          position: relative;
          z-index: 1;

          .stat-value {
            font-size: 28px;
            font-weight: 400;
            color: white;
            line-height: 1.1;
            margin-bottom: 4px;

            @media (max-width: 768px) {
              font-size: 24px;
            }

            @media (max-width: 480px) {
              font-size: 20px;
            }
          }

          .stat-unit {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;

            @media (max-width: 480px) {
              font-size: 12px;
            }
          }
        }

        .stat-footer {
          position: relative;
          z-index: 1;

          .stat-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 12px;

            .stat-label {
              color: rgba(255, 255, 255, 0.9);
              font-weight: 500;
            }

            .stat-sub {
              color: rgba(255, 255, 255, 0.8);
              font-weight: 600;
            }
            .stat-detail {
              font-size: 10px;
              color: var(--text-tertiary);
              font-weight: 500;
              line-height: 1.3;
              transition: color 0.3s ease;

              @media (max-width: 480px) {
                font-size: 9px;
              }
            }
          }
        }
      }
    }
  }

  // 中间区域：左侧指标卡片 + 右侧出租率排行
  .middle-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 10px;
    margin-bottom: 12px;

    @media (max-width: 1400px) {
      gap: 16px;
    }

    @media (max-width: 1200px) {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    @media (max-width: 768px) {
      margin-bottom: 20px;
    }

    .middle-metrics-section {
      .metrics-panel {
        background: var(--bg-secondary);
        border-radius: 16px;
        padding: 24px;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;

        @media (max-width: 768px) {
          padding: 20px;
        }

        @media (max-width: 480px) {
          padding: 16px;
        }

        .metrics-header {
          margin-bottom: 20px;
          display: flex;
          justify-content: flex-end;

          .section-controls {
            display: flex;
            justify-content: center;

            .el-button-group {
              .el-button {
                --el-button-bg-color: white;
                --el-button-border-color: #e5e7eb;
                --el-button-text-color: #6b7280;
                --el-button-active-bg-color: #3b82f6;
                --el-button-active-border-color: #3b82f6;
                --el-button-active-text-color: white;
              }
            }
          }
        }

        .middle-metrics-grid {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 10px;

          @media (max-width: 1200px) {
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
          }

          @media (max-width: 768px) {
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
          }

          @media (max-width: 480px) {
            grid-template-columns: 1fr;
            gap: 10px;
          }

          .middle-metric-card {
            background: var(--bg-tertiary);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-light);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            height: 120px;
            min-height: 120px;

            @media (max-width: 768px) {
              height: 110px;
              min-height: 110px;
            }

            @media (max-width: 480px) {
              height: 100px;
              min-height: 100px;
            }

            &:hover {
              transform: translateY(-2px);
              box-shadow: var(--shadow-lg);
            }

            .metric-card-inner {
              position: relative;
              height: 100%;
              display: flex;
              flex-direction: column;
            }

            .metric-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;

              .metric-icon-wrapper {
                width: 32px;
                height: 32px;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);

                .metric-icon {
                  width: 16px;
                  height: 16px;
                  color: white;
                }
              }

              .metric-title {
                font-size: 13px;
                font-weight: 600;
                color: var(--text-primary);
                line-height: 1.3;
                margin-left: 8px;
                flex: 1;
                transition: color 0.3s ease;
                display: flex;
                align-items: center;

                @media (max-width: 768px) {
                  font-size: 12px;
                  margin-left: 6px;
                }

                @media (max-width: 480px) {
                  font-size: 11px;
                  margin-left: 4px;
                }
              }

              .metric-trend {
                .trend-indicator {
                  display: inline-flex;
                  align-items: center;
                  gap: 2px;
                  padding: 2px 4px;
                  border-radius: 3px;
                  font-size: 10px;
                  font-weight: 600;

                  &.trend-up {
                    background: #dcfce7;
                    color: #16a34a;
                  }

                  &.trend-down {
                    background: #fef2f2;
                    color: #dc2626;
                  }

                  i {
                    font-size: 8px;
                  }
                }
              }
            }

            .metric-content {
              flex: 1;

              .metric-value {
                font-size: 18px;
                font-weight: 700;
                color: var(--text-primary);
                line-height: 1.1;
                letter-spacing: -0.025em;
                transition: color 0.3s ease;

                @media (max-width: 768px) {
                  font-size: 16px;
                }

                @media (max-width: 480px) {
                  font-size: 14px;
                }
              }

              .metric-label {
                font-size: 12px;
                color: var(--text-secondary);
                font-weight: 600;
                margin-bottom: 4px;
                line-height: 1.2;
                transition: color 0.3s ease;

                @media (max-width: 480px) {
                  font-size: 11px;
                }
              }

              .metric-detail {
                font-size: 10px;
                color: var(--text-tertiary);
                font-weight: 500;
                line-height: 1.3;
                transition: color 0.3s ease;

                @media (max-width: 480px) {
                  font-size: 9px;
                }
              }
            }

            .metric-accent {
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              height: 3px;
            }
          }
        }
      }
    }
  }

  // 底部图表区域
  .bottom-charts-section {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 10px;
    margin-bottom: 20px;

    @media (max-width: 1200px) {
      grid-template-columns: 1fr 1fr;
      gap: 16px;
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }

  // 通用图表面板样式
  .chart-panel {
    background: var(--bg-secondary);
    border-radius: 12px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: all 0.3s ease;

    @media (max-width: 768px) {
      border-radius: 10px;
    }

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px 0;
      margin-bottom: 12px;

      @media (max-width: 768px) {
        padding: 12px 16px 0;
        margin-bottom: 10px;
      }

      @media (max-width: 480px) {
        padding: 10px 12px 0;
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
      }

      h3 {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
        transition: color 0.3s ease;

        @media (max-width: 768px) {
          font-size: 15px;
        }

        @media (max-width: 480px) {
          font-size: 14px;
        }
      }

      .chart-controls {
        .el-radio-group {
          --el-radio-button-checked-bg-color: #3b82f6;
          --el-radio-button-checked-border-color: #3b82f6;
        }
      }
    }

    .chart-content {
      padding: 0 20px 20px;
    }

    .ranking-content {
      padding: 0 20px 20px;

      .ranking-list {
        .ranking-item {
          display: flex;
          flex-direction: column;
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .ranking-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .ranking-name {
              font-size: 13px;
              color: #374151;
              font-weight: 500;
            }

            .ranking-percentage {
              font-size: 13px;
              font-weight: 600;
              color: #1f2937;
            }
          }

          .ranking-bar {
            width: 100%;
            height: 8px;
            background: #f3f4f6;
            border-radius: 4px;
            overflow: hidden;

            .bar-fill {
              height: 100%;
              border-radius: 4px;
              transition: width 0.6s ease;
            }
          }
        }
      }
    }
  }

  // 中间指标区域
  .middle-metrics-section {
    .metrics-header {
      margin-bottom: 20px;

      .section-controls {
        display: flex;
        justify-content: center;

        .el-button-group {
          .el-button {
            --el-button-bg-color: white;
            --el-button-border-color: #e5e7eb;
            --el-button-text-color: #6b7280;
            --el-button-active-bg-color: #3b82f6;
            --el-button-active-border-color: #3b82f6;
            --el-button-active-text-color: white;
          }
        }
      }
    }

    .middle-metrics-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;

      @media (max-width: 1200px) {
        grid-template-columns: repeat(3, 1fr);
      }

      @media (max-width: 768px) {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
      }

      @media (max-width: 480px) {
        grid-template-columns: 1fr;
      }

      .middle-metric-card {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border: 1px solid #f1f5f9;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        min-height: 120px;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
        }

        .metric-card-inner {
          padding: 16px;
          position: relative;
          height: 100%;
          display: flex;
          flex-direction: column;
        }

        .metric-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .metric-icon-wrapper {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);

            .metric-icon {
              width: 20px;
              height: 20px;
              color: white;
            }
          }

          .metric-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            line-height: 1.3;
            margin-left: 12px;
            flex: 1;
            transition: color 0.3s ease;
            display: flex;
            align-items: center;

            @media (max-width: 768px) {
              font-size: 13px;
              margin-left: 10px;
            }

            @media (max-width: 480px) {
              font-size: 12px;
              margin-left: 8px;
            }
          }

          .metric-trend {
            .trend-indicator {
              display: inline-flex;
              align-items: center;
              gap: 2px;
              padding: 2px 6px;
              border-radius: 4px;
              font-size: 11px;
              font-weight: 600;

              &.trend-up {
                background: #dcfce7;
                color: #16a34a;
              }

              &.trend-down {
                background: #fef2f2;
                color: #dc2626;
              }

              i {
                font-size: 9px;
              }
            }
          }
        }

        .metric-content {
          flex: 1;

          .metric-value {
            font-size: 22px;
            font-weight: 700;
            color: #111827;
            margin-bottom: 4px;
            line-height: 1.1;
            letter-spacing: -0.025em;
          }

          .metric-label {
            font-size: 13px;
            color: #6b7280;
            font-weight: 600;
            margin-bottom: 6px;
            line-height: 1.2;
          }

          .metric-detail {
            font-size: 11px;
            color: #9ca3af;
            font-weight: 500;
            line-height: 1.3;
          }
        }

        .metric-accent {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 3px;
          border-radius: 0 0 12px 12px;
        }

        // 不同类型的卡片样式
        &.metric-card-income {
          background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
        }

        &.metric-card-revenue {
          background: linear-gradient(135deg, #fffbf5 0%, #ffffff 100%);
        }

        &.metric-card-receivable {
          background: linear-gradient(135deg, #f0fdf9 0%, #ffffff 100%);
        }

        &.metric-card-debt {
          background: linear-gradient(135deg, #faf5ff 0%, #ffffff 100%);
        }

        &.metric-card-prepaid {
          background: linear-gradient(135deg, #f0fdff 0%, #ffffff 100%);
        }

        &.metric-card-new {
          background: linear-gradient(135deg, #f7fee7 0%, #ffffff 100%);
        }

        &.metric-card-payment {
          background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
        }

        &.metric-card-refund {
          background: linear-gradient(135deg, #fffbf5 0%, #ffffff 100%);
        }

        &.metric-card-price {
          background: linear-gradient(135deg, #f0fdf9 0%, #ffffff 100%);
        }

        &.metric-card-collection {
          background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
        }

        &.metric-card-tenants {
          background: linear-gradient(135deg, #fff7ed 0%, #ffffff 100%);
        }

        &.metric-card-complaints {
          background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
        }
      }
    }
  }

  // 通用图表面板样式
  .chart-panel {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    // 通用图表面板样式
    .chart-panel {
      background: white;
      border-radius: 12px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      overflow: hidden;

      .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px 0;
        margin-bottom: 12px;

        h3 {
          font-size: 16px;
          font-weight: 600;
          color: #1f2937;
          margin: 0;
        }

        .chart-controls {
          .el-radio-group {
            --el-radio-button-checked-bg-color: #3b82f6;
            --el-radio-button-checked-border-color: #3b82f6;
          }
        }
      }

      .chart-content {
        padding: 0 20px 20px;
      }

      .ranking-content {
        padding: 0 20px 20px;

        .ranking-list {
          .ranking-item {
            display: flex;
            flex-direction: column;
            margin-bottom: 16px;

            &:last-child {
              margin-bottom: 0;
            }

            .ranking-info {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;

              .ranking-name {
                font-size: 13px;
                color: #374151;
                font-weight: 500;
              }

              .ranking-percentage {
                font-size: 13px;
                font-weight: 600;
                color: #1f2937;
              }
            }

            .ranking-bar {
              width: 100%;
              height: 8px;
              background: #f3f4f6;
              border-radius: 4px;
              overflow: hidden;

              .bar-fill {
                height: 100%;
                border-radius: 4px;
                transition: width 0.6s ease;
              }
            }
          }
        }
      }
    }

    // 月度收支图表特殊样式
    .monthly-chart {
      height: fit-content;
    }

    // 出租率排行图表特殊样式
    .ranking-chart {
      height: fit-content;
    }
  }
</style>
