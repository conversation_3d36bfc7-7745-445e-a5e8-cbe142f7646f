<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="680"
    :model-value="modelValue"
    :title="isUpdate ? '修改空间' : '添加空间'"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent=""
    >
      <div class="form-grid">
        <el-form-item label="上级空间" prop="parentId">
          <SpaceMainSelect
            v-model="form.parentId"
            placeholder="请选择上级空间"
          />
        </el-form-item>
        <el-form-item label="空间名称" prop="spaceName">
          <el-input
            clearable
            :maxlength="20"
            v-model="form.spaceName"
            placeholder="请输入空间名称"
          />
        </el-form-item>
        <el-form-item label="空间编码" prop="spaceCode">
          <el-input
            clearable
            disabled
            :maxlength="10"
            v-model="form.spaceCode"
            placeholder="请输入空间编码"
          />
        </el-form-item>
        <el-form-item label="园区名称" prop="parkName">
          <el-input
            disabled
            clearable
            v-model="form.parkName"
            placeholder="请输入园区名称"
          />
        </el-form-item>
        <el-form-item label="省市区" prop="proviceCityArea">
          <el-input
            disabled
            clearable
            v-model="form.proviceCityArea"
            placeholder="请输入省市区"
          />
        </el-form-item>
        <el-form-item label="详细地址" prop="addressDetail">
          <el-input
            clearable
            v-model="form.addressDetail"
            placeholder="请输入详细地址"
          />
        </el-form-item>
        <el-form-item label="楼层数" prop="floorCount">
          <el-input-number
            v-model="form.floorCount"
            placeholder="请输入楼层数"
            :min="0"
            :precision="0"
            controls-position="right"
            class="ele-fluid"
          />
        </el-form-item>
        <el-form-item label="楼高(m)" prop="buildingHeight">
          <el-input-number
            v-model="form.buildingHeight"
            placeholder="请输入楼高"
            :min="0"
            :precision="2"
            controls-position="right"
            class="ele-fluid"
          />
        </el-form-item>
        <el-form-item label="建筑面积(㎡)" prop="totalArea">
          <el-input-number
            v-model="form.totalArea"
            placeholder="请输入建筑面积"
            :min="0"
            :precision="2"
            controls-position="right"
            class="ele-fluid"
          />
        </el-form-item>
        <el-form-item label="产证面积(㎡)" prop="certificateArea">
          <el-input-number
            v-model="form.certificateArea"
            placeholder="请输入产证面积"
            :min="0"
            :precision="2"
            controls-position="right"
            class="ele-fluid"
          />
        </el-form-item>
        <el-form-item label="出租面积(㎡)" prop="rentalArea">
          <el-input-number
            v-model="form.rentalArea"
            placeholder="请输入出租面积"
            :min="0"
            :precision="2"
            controls-position="right"
            class="ele-fluid"
          />
        </el-form-item>
        <el-form-item label="竣工日期" prop="completionDate">
          <el-date-picker
            v-model="form.completionDate"
            type="datetime"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            placeholder="请选择竣工日期"
            class="ele-fluid"
          />
        </el-form-item>
        <el-form-item label="空间状态" prop="status">
          <el-select v-model="form.status" clearable placeholder="请选择状态">
            <el-option
              v-for="dict in getIntDictOptions(
                DICT_TYPE.INFO_COUNTRY_AREA_STATUS
              )"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </div>

      <!-- 用途字段独立一行 -->
      <el-form-item label="用途" prop="purpose" class="purpose-field">
        <el-input
          v-model="form.purpose"
          type="textarea"
          :rows="2"
          placeholder="请输入用途"
          clearable
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, watch } from 'vue';
  import { EleMessage } from 'sirius-platform-pro/es';
  import { useFormData } from '@/utils/use-form-data';
  import SpaceMainSelect from './spacemain-select.vue';
  import { createSpaceMain, updateSpaceMain } from '@/api/info/spacemain';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';

  /** 格式化日期，避免时区问题 */
  const formatDate = (date) => {
    if (!date) return '';

    const d = new Date(date);
    if (isNaN(d.getTime())) return '';

    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  };

  /** 空间信息 表单 */
  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object,
    /** 上级空间id */
    parentId: Number
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    parentId: void 0,
    spaceName: '',
    spaceCode: '',
    parkName: '',
    proviceCityArea: '',
    addressDetail: '',
    floorCount: undefined,
    buildingHeight: undefined,
    totalArea: undefined,
    certificateArea: undefined,
    rentalArea: undefined,
    completionDate: '',
    purpose: '',
    sourceCode: 'MANUAL',
    status: 1
  });

  /** 表单验证规则 */
  const rules = reactive({
    spaceName: [
      {
        required: true,
        message: '请输入空间名称',
        type: 'string',
        trigger: 'blur'
      }
    ],
    status: [
      {
        required: true,
        message: '请选择状态',
        type: 'number',
        trigger: 'blur'
      }
    ]
  });

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      const saveOrUpdate = isUpdate.value ? updateSpaceMain : createSpaceMain;
      saveOrUpdate({ ...form, parentId: form.parentId || 0 })
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          assignFields({
            ...props.data,
            parentId: props.data.parentId || void 0,
            completionDate: props.data.completionDate
              ? formatDate(props.data.completionDate)
              : props.data.completionDate
          });
          isUpdate.value = true;
        } else {
          form.parentId = props.parentId;
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>

<style scoped>
  .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
  }

  .form-grid .el-form-item {
    margin-bottom: 18px;
  }

  /* 用途字段独立一行样式 */
  .el-form-item:last-child {
    margin-bottom: 0;
  }

  /* 用途字段间隔样式 */
  .purpose-field {
    margin-top: 20px;
  }
</style>
