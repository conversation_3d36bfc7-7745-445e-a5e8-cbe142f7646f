<template>
  <ele-modal
    form
    :width="680"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      v-loading="loading"
    >
      <el-form-item label="空间编码" prop="spaceCode">
        <el-input v-model="form.spaceCode" placeholder="请输入空间编码" />
      </el-form-item>
      <el-form-item label="空间名称" prop="spaceName">
        <el-input v-model="form.spaceName" placeholder="请输入空间名称" />
      </el-form-item>
      <el-form-item label="id" prop="parkId">
        <el-input v-model="form.parkId" placeholder="请输入id" />
      </el-form-item>
      <el-form-item label="园区编码" prop="parkCode">
        <el-input v-model="form.parkCode" placeholder="请输入园区编码" />
      </el-form-item>
      <el-form-item label="园区名称" prop="parkName">
        <el-input v-model="form.parkName" placeholder="请输入园区名称" />
      </el-form-item>
      <el-form-item label="省市区" prop="proviceCityArea">
        <el-input v-model="form.proviceCityArea" placeholder="请输入省市区" />
      </el-form-item>
      <el-form-item label="详细地址" prop="addressDetail">
        <el-input v-model="form.addressDetail" placeholder="请输入详细地址" />
      </el-form-item>
      <el-form-item label="上级空间id，为0表示顶级空间" prop="parentId">
        <el-input
          v-model="form.parentId"
          placeholder="请输入上级空间id，为0表示顶级空间"
        />
      </el-form-item>
      <el-form-item label="上级编码" prop="parentCode">
        <el-input v-model="form.parentCode" placeholder="请输入上级编码" />
      </el-form-item>
      <el-form-item label="上级名称" prop="parentName">
        <el-input v-model="form.parentName" placeholder="请输入上级名称" />
      </el-form-item>
      <el-form-item label="来源，默认-MANUAL-手动创建" prop="sourceCode">
        <el-input
          v-model="form.sourceCode"
          placeholder="请输入来源，默认-MANUAL-手动创建"
        />
      </el-form-item>
      <el-form-item label="部门ID" prop="deptId">
        <el-input v-model="form.deptId" placeholder="请输入部门ID" />
      </el-form-item>
      <el-form-item label="楼层数" prop="floorCount">
        <el-input v-model="form.floorCount" placeholder="请输入楼层数" />
      </el-form-item>
      <el-form-item label="楼高" prop="buildingHeight">
        <el-input v-model="form.buildingHeight" placeholder="请输入楼高" />
      </el-form-item>
      <el-form-item label="建筑面积" prop="totalArea">
        <el-input v-model="form.totalArea" placeholder="请输入建筑面积" />
      </el-form-item>
      <el-form-item label="产证面积" prop="certificateArea">
        <el-input v-model="form.certificateArea" placeholder="请输入产证面积" />
      </el-form-item>
      <el-form-item label="出租面积" prop="rentalArea">
        <el-input v-model="form.rentalArea" placeholder="请输入出租面积" />
      </el-form-item>
      <el-form-item label="竣工日期" prop="completionDate">
        <el-date-picker
          v-model="form.completionDate"
          type="date"
          value-format="x"
          placeholder="选择竣工日期"
        />
      </el-form-item>
      <el-form-item label="用途" prop="purpose">
        <el-input v-model="form.purpose" placeholder="请输入用途" />
      </el-form-item>
      <el-form-item label="空间状态 1启用，0禁用" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备用字段1" prop="atributeVarchar1">
        <el-input
          v-model="form.atributeVarchar1"
          placeholder="请输入备用字段1"
        />
      </el-form-item>
      <el-form-item label="备用字段2" prop="atributeVarchar2">
        <el-input
          v-model="form.atributeVarchar2"
          placeholder="请输入备用字段2"
        />
      </el-form-item>
      <el-form-item label="备用字段3" prop="atributeVarchar3">
        <el-input
          v-model="form.atributeVarchar3"
          placeholder="请输入备用字段3"
        />
      </el-form-item>
      <el-form-item label="备用字段4" prop="atributeVarchar4">
        <el-input
          v-model="form.atributeVarchar4"
          placeholder="请输入备用字段4"
        />
      </el-form-item>
      <el-form-item label="备用字段5" prop="atributeVarchar5">
        <el-input
          v-model="form.atributeVarchar5"
          placeholder="请输入备用字段5"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import * as SpaceMainApi from '@/api/info/spacemain';
  import TinymceEditor from '@/components/TinymceEditor/index.vue';
  import { useFormData } from '@/utils/use-form-data';

  /** 空间信息 表单 */
  defineOptions({ name: 'SpaceMainForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object,
    /** 上级空间id */
    parentId: Number
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    spaceCode: undefined,
    spaceName: undefined,
    parkId: undefined,
    parkCode: undefined,
    parkName: undefined,
    proviceCityArea: undefined,
    addressDetail: undefined,
    parentId: undefined,
    parentCode: undefined,
    parentName: undefined,
    sourceCode: 'MANUAL',
    deptId: undefined,
    floorCount: undefined,
    buildingHeight: undefined,
    totalArea: undefined,
    certificateArea: undefined,
    rentalArea: undefined,
    completionDate: undefined,
    purpose: undefined,
    status: 1,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined
  });

  /** 表单验证规则 */
  const rules = reactive({
    spaceName: [
      {
        required: true,
        message: '请输入空间名称',
        type: 'string',
        trigger: 'blur'
      }
    ],
    spaceCode: [
      {
        required: true,
        message: '请输入空间编码',
        type: 'string',
        trigger: 'blur'
      }
    ],
    status: [
      {
        required: true,
        message: '请选择状态',
        type: 'number',
        trigger: 'blur'
      }
    ]
  });

  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef.value) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        await SpaceMainApi.createSpaceMain(form);
        message.success(t('common.createSuccess'));
      } else {
        await SpaceMainApi.updateSpaceMain(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await SpaceMainApi.getSpaceMain(props.data.id);
        assignFields({
          ...result,
          parentId: result.parentId || undefined
        });
        isUpdate.value = true;
      } else {
        resetFields();
        form.parentId = props.parentId;
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
