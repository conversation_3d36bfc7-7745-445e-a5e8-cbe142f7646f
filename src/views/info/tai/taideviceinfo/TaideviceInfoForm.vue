<template>
  <ele-modal
      form
      :width="680"
      v-model="visible"
      :close-on-click-modal="false"
      destroy-on-close
      :title="title"
      @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      v-loading="loading"
    >
      <el-form-item label="设备id" prop="code">
        <el-input v-model="form.code" placeholder="请输入设备id" />
      </el-form-item>
      <el-form-item label="设备描述" prop="name">
        <el-input v-model="form.name" placeholder="请输入设备描述" />
      </el-form-item>
      <el-form-item label="版本号" prop="version">
        <el-input v-model="form.version" placeholder="请输入版本号" />
      </el-form-item>
      <el-form-item label="播放列表ID" prop="playlistId">
        <el-input v-model="form.playlistId" placeholder="请输入播放列表ID" />
      </el-form-item>
      <el-form-item label="间隔" prop="deviceInterval">
        <el-input v-model="form.deviceInterval" placeholder="请输入间隔" />
      </el-form-item>
      <el-form-item label="显示顺序" prop="sort">
        <el-input v-model="form.sort" placeholder="请输入显示顺序" />
      </el-form-item>
      <el-form-item label="型号" prop="model">
        <el-input v-model="form.model" placeholder="请输入型号" />
      </el-form-item>
      <el-form-item label="sn" prop="sn">
        <el-input v-model="form.sn" placeholder="请输入sn" />
      </el-form-item>
      <el-form-item label="方向" prop="orientation">
        <el-input v-model="form.orientation" placeholder="请输入方向" />
      </el-form-item>
      <el-form-item label="组id" prop="groupId">
        <el-input v-model="form.groupId" placeholder="请输入组id" />
      </el-form-item>
      <el-form-item label="是否活动" prop="active">
        <el-input v-model="form.active" placeholder="请输入是否活动" />
      </el-form-item>
      <el-form-item label="电池" prop="battery">
        <el-input v-model="form.battery" placeholder="请输入电池" />
      </el-form-item>
      <el-form-item label="电源类型" prop="powerType">
        <el-select v-model="form.powerType" placeholder="请选择电源类型">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="生态模式" prop="ecoMode">
        <el-input v-model="form.ecoMode" placeholder="请输入生态模式" />
      </el-form-item>
      <el-form-item label="ip地址" prop="ipAddress">
        <el-input v-model="form.ipAddress" placeholder="请输入ip地址" />
      </el-form-item>
      <el-form-item label="wifi标识" prop="wifiSsid">
        <el-input v-model="form.wifiSsid" placeholder="请输入wifi标识" />
      </el-form-item>
      <el-form-item label="wifi密码" prop="wifiPwd">
        <el-input v-model="form.wifiPwd" placeholder="请输入wifi密码" />
      </el-form-item>
      <el-form-item label="mac地址" prop="macAddress">
        <el-input v-model="form.macAddress" placeholder="请输入mac地址" />
      </el-form-item>
      <el-form-item label="播放播放列表" prop="playingPlayList">
        <el-input v-model="form.playingPlayList" placeholder="请输入播放播放列表" />
      </el-form-item>
      <el-form-item label="最后连接时间" prop="lastConnected">
        <el-input v-model="form.lastConnected" placeholder="请输入最后连接时间" />
      </el-form-item>
      <el-form-item label="下一个唤醒" prop="nextWakeup">
        <el-input v-model="form.nextWakeup" placeholder="请输入下一个唤醒" />
      </el-form-item>
      <el-form-item label="遥测版本" prop="telemetryVer">
        <el-input v-model="form.telemetryVer" placeholder="请输入遥测版本" />
      </el-form-item>
      <el-form-item label="设备状态（10新建 20启用 30停用）" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备用字段1" prop="atributeVarchar1">
        <el-input v-model="form.atributeVarchar1" placeholder="请输入备用字段1" />
      </el-form-item>
      <el-form-item label="备用字段2" prop="atributeVarchar2">
        <el-input v-model="form.atributeVarchar2" placeholder="请输入备用字段2" />
      </el-form-item>
      <el-form-item label="备用字段3" prop="atributeVarchar3">
        <el-input v-model="form.atributeVarchar3" placeholder="请输入备用字段3" />
      </el-form-item>
      <el-form-item label="备用字段4" prop="atributeVarchar4">
        <el-input v-model="form.atributeVarchar4" placeholder="请输入备用字段4" />
      </el-form-item>
      <el-form-item label="备用字段5" prop="atributeVarchar5">
        <el-input v-model="form.atributeVarchar5" placeholder="请输入备用字段5" />
      </el-form-item>
      <el-form-item label="备用text字段1" prop="atributeText1">
        <el-input v-model="form.atributeText1" placeholder="请输入备用text字段1" />
      </el-form-item>
      <el-form-item label="备用text字段2" prop="atributeText2">
        <el-input v-model="form.atributeText2" placeholder="请输入备用text字段2" />
      </el-form-item>
      <el-form-item label="备用text字段3" prop="atributeText3">
        <el-input v-model="form.atributeText3" placeholder="请输入备用text字段3" />
      </el-form-item>
      <el-form-item label="备用text字段4" prop="atributeText4">
        <el-input v-model="form.atributeText4" placeholder="请输入备用text字段4" />
      </el-form-item>
      <el-form-item label="备用text字段5" prop="atributeText5">
        <el-input v-model="form.atributeText5" placeholder="请输入备用text字段5" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading">保存</el-button>
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
import * as TaideviceInfoApi from '@/api/info/tai/taideviceinfo'
import TinymceEditor from '@/components/TinymceEditor/index.vue';
import { useFormData } from '@/utils/use-form-data';

/** 设备管理 表单 */
defineOptions({ name: 'TaideviceInfoForm' })

const props = defineProps({
  /** 修改回显的数据 */
  data: Object
});

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const emit = defineEmits(['done']);

const title = ref('') // 弹窗的标题
/** 弹窗是否打开 */
const visible = defineModel({ type: Boolean });

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
                    id: undefined,
                    code: undefined,
                    name: undefined,
                    version: undefined,
                    playlistId: undefined,
                    deviceInterval: undefined,
                    sort: undefined,
                    model: undefined,
                    sn: undefined,
                    orientation: undefined,
                    groupId: undefined,
                    active: undefined,
                    battery: undefined,
                    powerType: undefined,
                    ecoMode: undefined,
                    ipAddress: undefined,
                    wifiSsid: undefined,
                    wifiPwd: undefined,
                    macAddress: undefined,
                    playingPlayList: undefined,
                    lastConnected: undefined,
                    nextWakeup: undefined,
                    telemetryVer: undefined,
                    status: undefined,
                    atributeVarchar1: undefined,
                    atributeVarchar2: undefined,
                    atributeVarchar3: undefined,
                    atributeVarchar4: undefined,
                    atributeVarchar5: undefined,
                    atributeText1: undefined,
                    atributeText2: undefined,
                    atributeText3: undefined,
                    atributeText4: undefined,
                    atributeText5: undefined,
});
/** 表单验证规则 */
const rules  = reactive({
                code: [{ required: true, message: '设备id不能为空', trigger: 'blur' }],
                name: [{ required: true, message: '设备描述不能为空', trigger: 'blur' }],
                sort: [{ required: true, message: '显示顺序不能为空', trigger: 'blur' }],
                status: [{ required: true, message: '设备状态（10新建 20启用 30停用）不能为空', trigger: 'blur' }],
});
/** 关闭弹窗 */
const cancle = () => {
  visible.value = false;
};

const save = async () => {
  if (!formRef) return
  const valid = await formRef.value.validate();
  if (!valid) return;
  // 提交请求
  loading.value = true
  try {
    if (!isUpdate.value) {
      await TaideviceInfoApi.createTaideviceInfo(form)
      message.success(t('common.createSuccess'))
    } else {
      await TaideviceInfoApi.updateTaideviceInfo(form)
      message.success(t('common.updateSuccess'))
    }
    visible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    loading.value = false
  }
}

/** 弹窗打开事件 */
const handleOpen = async () => {
  loading.value = true
  try{
    if (props.data) {
      const result = await TaideviceInfoApi.getTaideviceInfo(props.data.id)
      assignFields({...result});
      isUpdate.value = true;
    } else {
      resetFields();
      isUpdate.value = false;
    }
    title.value = isUpdate.value?t('action.update'): t('action.create')
  }finally {
    loading.value = false
  }
}
</script>