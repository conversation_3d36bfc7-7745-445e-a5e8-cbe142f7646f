<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="设备uuid" prop="code">
              <el-input
                v-model.trim="queryParams.code"
                placeholder="请输入设备id"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="设备描述" prop="name">
              <el-input
                v-model.trim="queryParams.name"
                placeholder="请输入设备描述"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #onlineStatus="{ row }">
          <div
            style="display: flex; align-items: center; justify-content: center"
          >
            <el-icon
              :color="isOnline(row.lastConnected) ? '#409EFF' : '#F56C6C'"
              :size="20"
            >
              <Connection v-if="isOnline(row.lastConnected)" />
              <Close v-else />
            </el-icon>
          </div>
        </template>
        <template #battery="{ row }">
          <div
            style="display: flex; align-items: center; justify-content: center"
          >
            <template v-if="isOnline(row.lastConnected)">
              <el-icon
                :color="getBatteryColor(row.battery || 0)"
                :size="18"
                style="margin-right: 6px"
              >
                <Lightning />
              </el-icon>
              <span style="font-weight: 500; color: #606266"
                >{{ row.battery || 0 }}%</span
              >
            </template>
            <template v-else>
              <span style="color: #c0c4cc; font-size: 14px">--</span>
            </template>
          </div>
        </template>
        <template #deviceName="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openDeviceDetail(row)"
            style="font-weight: 500"
          >
            {{ row.name }}
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 设备详情弹窗 -->
    <el-dialog
      v-model="showDeviceDetail"
      title="设备详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="deviceDetail" class="device-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="设备名称">
            {{ deviceDetail.name }}
          </el-descriptions-item>
          <el-descriptions-item label="设备UUID">
            {{ deviceDetail.code }}
          </el-descriptions-item>
          <el-descriptions-item label="版本号">
            {{ deviceDetail.version }}
          </el-descriptions-item>
          <el-descriptions-item label="地点">
            {{ deviceDetail.locationDesc }}
          </el-descriptions-item>
          <el-descriptions-item label="播放列表">
            {{ deviceDetail.playlistDesc }}
          </el-descriptions-item>
          <el-descriptions-item label="间隔">
            {{ deviceDetail.deviceInterval }}
          </el-descriptions-item>
          <el-descriptions-item label="显示顺序">
            {{ deviceDetail.sort }}
          </el-descriptions-item>
          <el-descriptions-item label="型号">
            {{ deviceDetail.model }}
          </el-descriptions-item>
          <el-descriptions-item label="SN">
            {{ deviceDetail.sn }}
          </el-descriptions-item>
          <el-descriptions-item label="方向">
            {{ deviceDetail.orientation }}
          </el-descriptions-item>
          <el-descriptions-item label="组">
            {{ deviceDetail.groupDesc }}
          </el-descriptions-item>
          <el-descriptions-item label="是否活动">
            {{ deviceDetail.active ? '是' : '否' }}
          </el-descriptions-item>
          <el-descriptions-item label="电池">
            <div style="display: flex; align-items: center">
              <template v-if="isOnline(deviceDetail.lastConnected)">
                <el-icon
                  :color="getBatteryColor(deviceDetail.battery)"
                  :size="16"
                  style="margin-right: 6px"
                >
                  <Lightning />
                </el-icon>
                <span>{{ deviceDetail.battery }}%</span>
              </template>
              <template v-else>
                <span style="color: #c0c4cc">设备离线</span>
              </template>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="电源类型">
            {{ deviceDetail.powerType }}
          </el-descriptions-item>
          <el-descriptions-item label="生态模式">
            {{ deviceDetail.ecoMode }}
          </el-descriptions-item>
          <el-descriptions-item label="IP地址">
            {{ deviceDetail.ipAddress }}
          </el-descriptions-item>
          <el-descriptions-item label="WiFi标识">
            {{ deviceDetail.wifiSsid }}
          </el-descriptions-item>
          <el-descriptions-item label="WiFi密码">
            {{ deviceDetail.wifiPwd }}
          </el-descriptions-item>
          <el-descriptions-item label="MAC地址">
            {{ deviceDetail.macAddress }}
          </el-descriptions-item>
          <el-descriptions-item label="播放播放列表">
            {{ deviceDetail.playingPlayList }}
          </el-descriptions-item>
          <el-descriptions-item label="最后连接时间">
            {{ deviceDetail.lastConnected }}
          </el-descriptions-item>
          <el-descriptions-item label="下一个唤醒">
            {{ deviceDetail.nextWakeup }}
          </el-descriptions-item>
          <el-descriptions-item label="遥测版本">
            {{ deviceDetail.telemetryVer }}
          </el-descriptions-item>
          <el-descriptions-item label="在线状态">
            <div style="display: flex; align-items: center">
              <el-icon
                :color="
                  isOnline(deviceDetail.lastConnected) ? '#409EFF' : '#F56C6C'
                "
                :size="16"
                style="margin-right: 6px"
              >
                <Connection v-if="isOnline(deviceDetail.lastConnected)" />
                <Close v-else />
              </el-icon>
              {{ isOnline(deviceDetail.lastConnected) ? '在线' : '离线' }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="showDeviceDetail = false">关闭</el-button>
      </template>
    </el-dialog>
  </ele-page>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import {
    Search,
    Refresh,
    Connection,
    Close,
    Lightning
  } from '@element-plus/icons-vue';
  import { DownloadOutlined } from '@/components/icons';
  import { useMessage } from '@/hooks/web/useMessage';
  import download from '@/utils/download';
  import * as TaideviceInfoApi from '@/api/info/tai/taideviceinfo';
  import { useFormData } from '@/utils/use-form-data';
  import { dateFormatter } from '@/utils/formatTime';

  /** 设备管理 列表 */
  defineOptions({ name: 'TaideviceInfoIndex' });

  const message = useMessage(); // 消息弹窗
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    code: undefined,
    name: undefined,
    version: undefined,
    playlistId: undefined,
    deviceInterval: undefined,
    sort: undefined,
    model: undefined,
    sn: undefined,
    orientation: undefined,
    groupId: undefined,
    active: undefined,
    battery: undefined,
    powerType: undefined,
    ecoMode: undefined,
    ipAddress: undefined,
    wifiSsid: undefined,
    wifiPwd: undefined,
    macAddress: undefined,
    playingPlayList: undefined,
    lastConnected: undefined,
    nextWakeup: undefined,
    telemetryVer: undefined,
    status: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined,
    atributeText1: undefined,
    atributeText2: undefined,
    atributeText3: undefined,
    atributeText4: undefined,
    atributeText5: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref<any>(null);
  /** 表格列配置 */
  const columns = ref([
    {
      prop: 'name',
      label: '设备名称',
      align: 'center',
      minWidth: 200,
      slot: 'deviceName'
    },
    {
      prop: 'code',
      label: '设备UUID',
      align: 'center',
      minWidth: 200
    },
    {
      prop: 'onlineStatus',
      label: '在线状态',
      align: 'center',
      width: 120,
      slot: 'onlineStatus'
    },
    {
      prop: 'battery',
      label: '电量',
      align: 'center',
      width: 160,
      slot: 'battery'
    },
    {
      prop: 'lastConnected',
      label: '最后连接时间',
      align: 'center',
      minWidth: 160,
      formatter: dateFormatter
    }
  ]);
  /** 当前编辑数据 */
  const current = ref<any>(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);
  /** 是否显示设备详情弹窗 */
  const showDeviceDetail = ref(false);
  /** 设备详情数据 */
  const deviceDetail = ref<any>(null);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return TaideviceInfoApi.getTaideviceInfoPage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };

  const refresh = async () => {
    try {
      // 发起刷新
      await TaideviceInfoApi.refreshDeviceInfo();
      message.success('刷新成功');
      // 刷新列表
      reload();
    } catch {}
  };

  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data = await TaideviceInfoApi.exportTaideviceInfo(queryParams);
      download.excel(data.data, '设备管理.xls');
    } catch {
    } finally {
      exportLoading.value = false;
    }
  };

  /** 判断设备是否在线 */
  const isOnline = (lastConnected: string) => {
    if (!lastConnected) return false;
    const today = new Date();
    const lastConnectedDate = new Date(lastConnected);

    // 比较年月日是否相同
    return (
      today.getFullYear() === lastConnectedDate.getFullYear() &&
      today.getMonth() === lastConnectedDate.getMonth() &&
      today.getDate() === lastConnectedDate.getDate()
    );
  };

  /** 获取电池颜色 */
  const getBatteryColor = (battery: number) => {
    if (battery >= 60) return '#67C23A'; // 绿色
    if (battery >= 30) return '#E6A23C'; // 橙色
    return '#F56C6C'; // 红色
  };

  /** 打开设备详情 */
  const openDeviceDetail = (row: any) => {
    deviceDetail.value = row;
    showDeviceDetail.value = true;
  };
</script>

<style scoped>
  .device-detail {
    max-height: 60vh;
    overflow-y: auto;
  }

  .device-detail :deep(.el-descriptions__body) {
    background-color: #fafafa;
  }

  .device-detail :deep(.el-descriptions__label) {
    font-weight: 600;
    color: #303133;
  }

  .device-detail :deep(.el-descriptions__content) {
    color: #606266;
  }
</style>
