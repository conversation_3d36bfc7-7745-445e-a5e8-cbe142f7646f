<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="组uuid" prop="code">
              <el-input
                v-model.trim="queryParams.code"
                placeholder="请输入组uuid"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="组描述" prop="name">
              <el-input
                v-model.trim="queryParams.name"
                placeholder="请输入组描述"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            v-permission="['information:taigroup-info:create']"
            :icon="Plus"
            @click="openEdit(null)"
          >
            新增
          </el-button>
          <el-button
            type="primary"
            class="ele-btn-icon"
            v-permission="['information:taigroup-info:create']"
            :icon="Plus"
            @click="refresh(null)"
          >
            刷新数据
          </el-button>
          <el-button
            class="ele-btn-icon"
            v-permission="['information:taigroup-info:export']"
            :icon="DownloadOutlined"
            @click="exportData"
            :loading="exportLoading"
          >
            导出
          </el-button>
        </template>
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.INFORMATION_STATUS"
            type="tag"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openEdit(row)"
            v-permission="['information:taigroup-info:update']"
          >
            编辑
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['information:taigroup-info:delete']"
          />
          <el-link
            :underline="false"
            type="primary"
            @click="removeBatch(row)"
            v-permission="['information:taigroup-info:delete']"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <TaigroupInfoForm v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { DownloadOutlined } from '@/components/icons';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import download from '@/utils/download';
  import * as TaigroupInfoApi from '@/api/info/tai/taigroupinfo';
  import TaigroupInfoForm from './TaigroupInfoForm.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE } from '@/utils/dict';

  /** 组管理 列表 */
  defineOptions({ name: 'TaigroupInfoIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    code: undefined,
    name: undefined,
    status: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined,
    atributeText1: undefined,
    atributeText2: undefined,
    atributeText3: undefined,
    atributeText4: undefined,
    atributeText5: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'code',
      label: '组UUID',
      align: 'center',
      minWidth: 170
    },
    {
      prop: 'name',
      label: '组描述',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return TaigroupInfoApi.getTaigroupInfoPage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await TaigroupInfoApi.deleteTaigroupInfo(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data = await TaigroupInfoApi.exportTaigroupInfo(queryParams);
      download.excel(data, '组管理.xls');
    } catch {
    } finally {
      exportLoading.value = false;
    }
  };
  const refresh = async (row) => {
    try {
      // 发起删除
      await TaigroupInfoApi.refreshGroupInfo();
      message.success('刷新成功');
      // 刷新列表
      reload();
    } catch {}
  };
</script>
