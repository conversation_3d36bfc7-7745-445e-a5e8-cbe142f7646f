<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="文件表id" prop="taifileId">
                    <el-input
                            v-model.trim="queryParams.taifileId"
                            placeholder="请输入文件表id"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="文件名" prop="name">
                    <el-input
                            v-model.trim="queryParams.name"
                            placeholder="请输入文件名"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="图片地址" prop="imageSrc">
                    <el-input
                            v-model.trim="queryParams.imageSrc"
                            placeholder="请输入图片地址"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="播放列表id" prop="playlistId">
                    <el-input
                            v-model.trim="queryParams.playlistId"
                            placeholder="请输入播放列表id"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="排序" prop="sort">
                    <el-input
                            v-model.trim="queryParams.sort"
                            placeholder="请输入排序"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="组状态（10新建 20启用 30停用）" prop="status">
                    <el-select
                            v-model="queryParams.status"
                            placeholder="请选择组状态（10新建 20启用 30停用）"
                            clearable
                    >
                        <el-option label="请选择字典生成" value="" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="备用字段1" prop="atributeVarchar1">
                    <el-input
                            v-model.trim="queryParams.atributeVarchar1"
                            placeholder="请输入备用字段1"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="备用字段2" prop="atributeVarchar2">
                    <el-input
                            v-model.trim="queryParams.atributeVarchar2"
                            placeholder="请输入备用字段2"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="备用字段3" prop="atributeVarchar3">
                    <el-input
                            v-model.trim="queryParams.atributeVarchar3"
                            placeholder="请输入备用字段3"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="备用字段4" prop="atributeVarchar4">
                    <el-input
                            v-model.trim="queryParams.atributeVarchar4"
                            placeholder="请输入备用字段4"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="备用字段5" prop="atributeVarchar5">
                    <el-input
                            v-model.trim="queryParams.atributeVarchar5"
                            placeholder="请输入备用字段5"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="备用text字段1" prop="atributeText1">
                    <el-input
                            v-model.trim="queryParams.atributeText1"
                            placeholder="请输入备用text字段1"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="备用text字段2" prop="atributeText2">
                    <el-input
                            v-model.trim="queryParams.atributeText2"
                            placeholder="请输入备用text字段2"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="备用text字段3" prop="atributeText3">
                    <el-input
                            v-model.trim="queryParams.atributeText3"
                            placeholder="请输入备用text字段3"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="备用text字段4" prop="atributeText4">
                    <el-input
                            v-model.trim="queryParams.atributeText4"
                            placeholder="请输入备用text字段4"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="备用text字段5" prop="atributeText5">
                    <el-input
                            v-model.trim="queryParams.atributeText5"
                            placeholder="请输入备用text字段5"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                  <el-col :lg="6" :md="12" :sm="12" :xs="24">
                    <el-form-item label="创建时间" prop="createTime">
                      <el-date-picker
                              v-model="queryParams.createTime"
                              value-format="YYYY-MM-DD HH:mm:ss"
                              type="daterange"
                              start-placeholder="开始日期"
                              end-placeholder="结束日期"
                              :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                              class="!w-220px"
                      />
                    </el-form-item>
                  </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
              >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
              ref="tableRef"
              row-key="id"
              :columns="columns"
              :datasource="datasource"
              :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
                  type="primary"
                  class="ele-btn-icon"
                  v-permission="['information:taifile-playlist-info:create']"
                  :icon="Plus"
                  @click="openEdit(null)"
          >
            新增
          </el-button>
          <el-button
                  class="ele-btn-icon"
                  v-permission="['information:taifile-playlist-info:export']"
                  :icon="DownloadOutlined"
                  @click="exportData"
                  :loading="exportLoading"
          >
            导出
          </el-button>
        </template>
        <template #action="{ row }">
          <el-link
                  :underline="false"
                  type="primary"
                  @click="openEdit(row)"
                  v-permission="['information:taifile-playlist-info:update']"
          >
            编辑
          </el-link>
          <el-divider
                  direction="vertical"
                  v-permission="['information:taifile-playlist-info:delete']"
          />
          <el-link
                  :underline="false"
                  type="primary"
                  @click="removeBatch(row)"
                  v-permission="['information:taifile-playlist-info:delete']"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <TaifilePlaylistInfoForm v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup lang="ts">
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { DownloadOutlined } from '@/components/icons';
import { dateFormatter } from '@/utils/formatTime';
import { useMessage } from '@/hooks/web/useMessage';
import download from '@/utils/download'
import * as TaifilePlaylistInfoApi  from '@/api/info/tai/taifileplaylistinfo'
import TaifilePlaylistInfoForm from './TaifilePlaylistInfoForm.vue'
import { useFormData } from '@/utils/use-form-data';

/** 播放列表分配文件列 列表 */
defineOptions({ name: 'TaifilePlaylistInfoIndex' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
  /** 表单数据 */
const [queryParams, resetFields] = useFormData({
            taifileId: undefined,
            name: undefined,
            imageSrc: undefined,
            playlistId: undefined,
            sort: undefined,
            status: undefined,
            atributeVarchar1: undefined,
            atributeVarchar2: undefined,
            atributeVarchar3: undefined,
            atributeVarchar4: undefined,
            atributeVarchar5: undefined,
            atributeText1: undefined,
            atributeText2: undefined,
            atributeText3: undefined,
            atributeText4: undefined,
            atributeText5: undefined,
            createTime: [],
});
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
const tableRef = ref(null);
  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
        {
          prop: 'id',
          label: '地址ID',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'taifileId',
          label: '文件表id',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'name',
          label: '文件名',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'imageSrc',
          label: '图片地址',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'playlistId',
          label: '播放列表id',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'sort',
          label: '排序',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'status',
          label: '组状态（10新建 20启用 30停用）',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'atributeVarchar1',
          label: '备用字段1',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'atributeVarchar2',
          label: '备用字段2',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'atributeVarchar3',
          label: '备用字段3',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'atributeVarchar4',
          label: '备用字段4',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'atributeVarchar5',
          label: '备用字段5',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'atributeText1',
          label: '备用text字段1',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'atributeText2',
          label: '备用text字段2',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'atributeText3',
          label: '备用text字段3',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'atributeText4',
          label: '备用text字段4',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'atributeText5',
          label: '备用text字段5',
          align: 'center',
          minWidth: 110,
        },
        {
          prop:  'createTime',
          label: '创建时间',
          align: 'center',
          minWidth: 130,
          formatter: dateFormatter
        },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false) // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return TaifilePlaylistInfoApi.getTaifilePlaylistInfoPage({ ...where, ...filters, ...pages });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await TaifilePlaylistInfoApi.deleteTaifilePlaylistInfo(row.id)
      message.success(t('common.delSuccess'))
      // 刷新列表
      reload();
    } catch {}
  };
  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm()
      // 发起导出
      exportLoading.value = true
      const data = await TaifilePlaylistInfoApi.exportTaifilePlaylistInfo(queryParams)
      download.excel(data, '播放列表分配文件列.xls')
    } catch {
    } finally {
      exportLoading.value = false
    }
  };
</script>