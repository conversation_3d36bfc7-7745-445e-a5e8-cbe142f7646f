<template>
  <ele-modal
      form
      :width="680"
      v-model="visible"
      :close-on-click-modal="false"
      destroy-on-close
      :title="title"
      @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      v-loading="loading"
    >
      <el-form-item label="文件表id" prop="taifileId">
        <el-input v-model="form.taifileId" placeholder="请输入文件表id" />
      </el-form-item>
      <el-form-item label="文件名" prop="name">
        <el-input v-model="form.name" placeholder="请输入文件名" />
      </el-form-item>
      <el-form-item label="图片地址" prop="imageSrc">
        <el-input v-model="form.imageSrc" placeholder="请输入图片地址" />
      </el-form-item>
      <el-form-item label="播放列表id" prop="playlistId">
        <el-input v-model="form.playlistId" placeholder="请输入播放列表id" />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input v-model="form.sort" placeholder="请输入排序" />
      </el-form-item>
      <el-form-item label="组状态（10新建 20启用 30停用）" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备用字段1" prop="atributeVarchar1">
        <el-input v-model="form.atributeVarchar1" placeholder="请输入备用字段1" />
      </el-form-item>
      <el-form-item label="备用字段2" prop="atributeVarchar2">
        <el-input v-model="form.atributeVarchar2" placeholder="请输入备用字段2" />
      </el-form-item>
      <el-form-item label="备用字段3" prop="atributeVarchar3">
        <el-input v-model="form.atributeVarchar3" placeholder="请输入备用字段3" />
      </el-form-item>
      <el-form-item label="备用字段4" prop="atributeVarchar4">
        <el-input v-model="form.atributeVarchar4" placeholder="请输入备用字段4" />
      </el-form-item>
      <el-form-item label="备用字段5" prop="atributeVarchar5">
        <el-input v-model="form.atributeVarchar5" placeholder="请输入备用字段5" />
      </el-form-item>
      <el-form-item label="备用text字段1" prop="atributeText1">
        <el-input v-model="form.atributeText1" placeholder="请输入备用text字段1" />
      </el-form-item>
      <el-form-item label="备用text字段2" prop="atributeText2">
        <el-input v-model="form.atributeText2" placeholder="请输入备用text字段2" />
      </el-form-item>
      <el-form-item label="备用text字段3" prop="atributeText3">
        <el-input v-model="form.atributeText3" placeholder="请输入备用text字段3" />
      </el-form-item>
      <el-form-item label="备用text字段4" prop="atributeText4">
        <el-input v-model="form.atributeText4" placeholder="请输入备用text字段4" />
      </el-form-item>
      <el-form-item label="备用text字段5" prop="atributeText5">
        <el-input v-model="form.atributeText5" placeholder="请输入备用text字段5" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading">保存</el-button>
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
import * as TaifilePlaylistInfoApi from '@/api/info/tai/taifileplaylistinfo'
import TinymceEditor from '@/components/TinymceEditor/index.vue';
import { useFormData } from '@/utils/use-form-data';

/** 播放列表分配文件列 表单 */
defineOptions({ name: 'TaifilePlaylistInfoForm' })

const props = defineProps({
  /** 修改回显的数据 */
  data: Object
});

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const emit = defineEmits(['done']);

const title = ref('') // 弹窗的标题
/** 弹窗是否打开 */
const visible = defineModel({ type: Boolean });

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
                    id: undefined,
                    taifileId: undefined,
                    name: undefined,
                    imageSrc: undefined,
                    playlistId: undefined,
                    sort: undefined,
                    status: undefined,
                    atributeVarchar1: undefined,
                    atributeVarchar2: undefined,
                    atributeVarchar3: undefined,
                    atributeVarchar4: undefined,
                    atributeVarchar5: undefined,
                    atributeText1: undefined,
                    atributeText2: undefined,
                    atributeText3: undefined,
                    atributeText4: undefined,
                    atributeText5: undefined,
});
/** 表单验证规则 */
const rules  = reactive({
                taifileId: [{ required: true, message: '文件表id不能为空', trigger: 'blur' }],
                name: [{ required: true, message: '文件名不能为空', trigger: 'blur' }],
                playlistId: [{ required: true, message: '播放列表id不能为空', trigger: 'blur' }],
                status: [{ required: true, message: '组状态（10新建 20启用 30停用）不能为空', trigger: 'blur' }],
});
/** 关闭弹窗 */
const cancle = () => {
  visible.value = false;
};

const save = async () => {
  if (!formRef) return
  const valid = await formRef.value.validate();
  if (!valid) return;
  // 提交请求
  loading.value = true
  try {
    if (!isUpdate.value) {
      await TaifilePlaylistInfoApi.createTaifilePlaylistInfo(form)
      message.success(t('common.createSuccess'))
    } else {
      await TaifilePlaylistInfoApi.updateTaifilePlaylistInfo(form)
      message.success(t('common.updateSuccess'))
    }
    visible.value = false
    // 发送操作成功的事件
    emit('done')
  } finally {
    loading.value = false
  }
}

/** 弹窗打开事件 */
const handleOpen = async () => {
  loading.value = true
  try{
    if (props.data) {
      const result = await TaifilePlaylistInfoApi.getTaifilePlaylistInfo(props.data.id)
      assignFields({...result});
      isUpdate.value = true;
    } else {
      resetFields();
      isUpdate.value = false;
    }
    title.value = isUpdate.value?t('action.update'): t('action.create')
  }finally {
    loading.value = false
  }
}
</script>