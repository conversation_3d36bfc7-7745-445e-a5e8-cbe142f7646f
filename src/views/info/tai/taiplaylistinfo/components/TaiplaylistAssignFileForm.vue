<template>
  <ele-page v-model="visible">
    <demo-basic />
    <demo-advanced/>
  </ele-page>
</template>

<script setup>
  import DemoBasic from './demo-basic.vue';
  import DemoAdvanced from './demo-advanced.vue';
  import { computed } from 'vue';
  const showDemoBasic = ref(false); 
  const showDemoAdvanced = ref(false); 
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };
</script>

<script>
  export default {
    name: 'ExtensionUpload'
  };
</script>
