<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="播放列表uuid" prop="code">
              <el-input
                v-model.trim="queryParams.code"
                placeholder="请输入播放列表uuid"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="名称" prop="name">
              <el-input
                v-model.trim="queryParams.name"
                placeholder="请输入名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="方向" prop="orientation">
              <el-input
                v-model.trim="queryParams.orientation"
                placeholder="请输入方向"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="模型" prop="model">
              <el-input
                v-model.trim="queryParams.model"
                placeholder="请输入模型"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="排序" prop="sort">
              <el-input
                v-model.trim="queryParams.sort"
                placeholder="请输入排序"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="版本" prop="version">
              <el-input
                v-model.trim="queryParams.version"
                placeholder="请输入版本"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="内容版本" prop="contentVersion">
              <el-input
                v-model.trim="queryParams.contentVersion"
                placeholder="请输入内容版本"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            v-permission="['information:taiplaylist-info:create']"
            :icon="Plus"
            @click="openEdit(null)"
          >
            新增
          </el-button>
          <el-button
            type="primary"
            class="ele-btn-icon"
            v-permission="['information:taiplaylist-info:create']"
            :icon="Plus"
            :loading="refreshLoading"
            @click="refresh(null)"
          >
            刷新数据
          </el-button>
          <el-button
            class="ele-btn-icon"
            v-permission="['information:taiplaylist-info:export']"
            :icon="DownloadOutlined"
            @click="exportData"
            :loading="exportLoading"
          >
            导出
          </el-button>
        </template>
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.INFORMATION_STATUS"
            type="tag"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openEdit(row)"
            v-permission="['information:taiplaylist-info:update']"
          >
            编辑
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['information:taiplaylist-info:delete']"
          />
          <el-link
            :underline="false"
            type="primary"
            @click="removeBatch(row)"
            v-permission="['information:taiplaylist-info:delete']"
          >
            删除
          </el-link>
          <el-divider direction="vertical" />
          <ele-dropdown
            v-if="moreItems.length"
            :items="moreItems"
            style="display: inline"
            @command="(key) => dropClick(key, row)"
          >
            <el-link type="primary" :underline="false">
              <span>更多</span>
              <el-icon
                :size="12"
                style="vertical-align: -1px; margin-left: 2px"
              >
                <ArrowDown />
              </el-icon>
            </el-link>
          </ele-dropdown>
        </template>
      </ele-pro-table>
    </ele-card>
    <TaiplaylistInfoForm v-model="showEdit" :data="current" @done="reload" />
    <TaiplaylistAssignFileForm
      v-model="showAssignFile"
      :data="current"
      @done="reload"
    />
  </ele-page>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { DownloadOutlined } from '@/components/icons';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import download from '@/utils/download';
  import * as TaiplaylistInfoApi from '@/api/info/tai/taiplaylistinfo';
  import TaiplaylistInfoForm from './components/TaiplaylistInfoForm.vue';
  import TaiplaylistAssignFileForm from './components/TaiplaylistAssignFileForm.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE } from '@/utils/dict';

  /** 播放列 列表 */
  defineOptions({ name: 'TaiplaylistInfoIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    code: undefined,
    name: undefined,
    orientation: undefined,
    model: undefined,
    sort: undefined,
    version: undefined,
    contentVersion: undefined,
    status: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined,
    atributeText1: undefined,
    atributeText2: undefined,
    atributeText3: undefined,
    atributeText4: undefined,
    atributeText5: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'code',
      label: '播放列表uuid',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'name',
      label: '名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'orientation',
      label: '方向',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'model',
      label: '模型',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'sort',
      label: '排序',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'version',
      label: '版本',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'contentVersion',
      label: '内容版本',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'status',
      label: '组状态',
      align: 'center',
      minWidth: 110,
      slot: 'status'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  const refreshLoading = ref(false); // 刷新的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);
  /** 是否显示图片分配弹窗 */
  const showAssignFile = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return TaiplaylistInfoApi.getTaiplaylistInfoPage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await TaiplaylistInfoApi.deleteTaiplaylistInfo(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data = await TaiplaylistInfoApi.exportTaiplaylistInfo(queryParams);
      download.excel(data, '播放列.xls');
    } catch {
    } finally {
      exportLoading.value = false;
    }
  };
  const refresh = async (row) => {
    try {
      refreshLoading.value = true;
      await TaiplaylistInfoApi.refreshPlaylistInfo();
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload;
      refreshLoading.value = false;
    } catch {
    } finally {
      refreshLoading.value = false;
    }
  };

  /** 操作列更多下拉菜单 */
  const moreItems = computed(() => {
    const items = [];
    items.push({ title: '分配图片', command: 'assignFile' });
    return items;
  });

  /** 下拉菜单点击事件 */
  const dropClick = (key, row) => {
    if (key === 'assignFile') {
      openAssignFile(row);
    }
  };

  /** 打开图片分配弹窗 */
  const openAssignFile = (row) => {
    current.value = row ?? null;
    showAssignFile.value = true;
  };
</script>
