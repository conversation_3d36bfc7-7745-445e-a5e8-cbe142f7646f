<template>
  <div class="play-theme-config">
    <!-- 顶部文件区域 -->
    <div class="top-section">
      <!-- 左侧：文件上传区 -->
      <div class="upload-section">
        <div
          class="upload-area"
          @click="triggerFileUpload"
          @dragover.prevent="handleDragOver"
          @dragleave.prevent="handleDragLeave"
          @drop.prevent="handleDrop"
          :class="{
            uploading: uploadLoading,
            'drag-over': isDragOver
          }"
        >
          <div v-if="!uploadLoading">
            <el-icon class="upload-icon"><Plus /></el-icon>
            <div class="upload-text">点击/拖拽图片</div>
            <div class="upload-hint">
              图像格式：JPG/JPEG/PNG/BMP<br />
              支持手动裁剪为 2560×1440 分辨率
            </div>
          </div>
          <div v-else class="upload-processing">
            <el-icon class="upload-loading"><Loading /></el-icon>
            <div class="upload-text">正在处理图片...</div>
            <div class="upload-hint"> 正在准备裁剪工具... </div>
          </div>

          <!-- 隐藏的文件输入框，只覆盖上传区域 -->
          <input
            ref="fileInput"
            type="file"
            multiple
            accept="image/*"
            style="
              opacity: 0;
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              cursor: pointer;
              pointer-events: none;
            "
            @change="handleFileUpload"
          />
        </div>
      </div>

      <!-- 右侧：文件展示和操作区 -->
      <div class="files-section">
        <!-- 文件操作栏 -->
        <div class="files-toolbar">
          <el-button type="primary" :icon="Plus" @click="addToPlaylist">
            添加到播放列表
          </el-button>
          <div class="file-count">
            已选: {{ selectedFiles.length }}/{{ totalFiles }}
            <el-button
              :icon="Delete"
              size="small"
              type="danger"
              @click="clearSelection"
              :disabled="selectedFiles.length === 0"
              circle
            />
          </div>
        </div>

        <!-- 文件网格 -->
        <div class="files-grid">
          <div
            v-for="file in fileList"
            :key="file.id"
            class="file-item"
            :class="{ selected: selectedFiles.includes(file.id) }"
            @click="toggleFileSelection(file.id)"
          >
            <div class="file-preview">
              <img :src="file.url" :alt="file.name" />
              <div class="file-overlay" v-if="selectedFiles.includes(file.id)">
                <el-icon class="check-icon">
                  <Check />
                </el-icon>
              </div>
            </div>
            <div class="file-name">{{ file.name }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 下方主要内容区域 -->
    <div class="bottom-section">
      <!-- 左侧：播放主题管理 -->
      <div class="playlist-management">
        <div class="card-header">
          <div class="header-left">
            <div class="header-icon">
              <el-icon><VideoPlay /></el-icon>
            </div>
            <span class="header-title">播放主题</span>
          </div>
          <div class="left-header-actions">
            <el-button
              :icon="Plus"
              size="small"
              type="primary"
              class="action-btn add-btn"
              title="新增主题"
              @click="openCreateDialog"
            />
            <el-button
              :icon="Edit"
              size="small"
              type="warning"
              class="action-btn edit-btn"
              title="编辑主题"
              :disabled="!selectedPlaylist"
              @click="editPlaylist(selectedPlaylist)"
            />
            <el-button
              :icon="VideoPlay"
              size="small"
              type="success"
              class="action-btn play-btn"
              title="播放预览"
              :disabled="!selectedPlaylist"
              @click="previewPlaylist(selectedPlaylist)"
            />
            <el-button
              :icon="Delete"
              size="small"
              type="danger"
              class="action-btn delete-btn"
              title="删除主题"
              :disabled="!selectedPlaylist"
              @click="deletePlaylist(selectedPlaylist)"
            />
          </div>
        </div>

        <div class="playlist-table-container">
          <el-table
            ref="playlistTableRef"
            :data="playlistTableData"
            class="playlist-table"
            highlight-current-row
            @current-change="handlePlaylistRowSelect"
            max-height="400"
            size="small"
          >
            <el-table-column
              prop="name"
              label="播放主题名称"
              show-overflow-tooltip
            />
            <el-table-column
              prop="updateTime"
              label="上次编辑日期"
              width="160"
              :formatter="dateFormatter"
              show-overflow-tooltip
            />
          </el-table>
        </div>
      </div>

      <!-- 右侧：播放列表内容 -->
      <div class="playlist-content">
        <div class="card-header">
          <div class="header-right-content">
            <span class="device-name">{{
              selectedPlaylist ? selectedPlaylist.name : '我是列表名称'
            }}</span>
            <span class="content-count">{{ playlistFiles.length }} 个内容</span>
            <div class="header-actions">
              <el-button
                size="small"
                class="save-btn"
                :loading="saveLoading"
                @click="handleSave"
                >保存</el-button
              >
              <el-button
                type="primary"
                size="small"
                class="assign-btn"
                @click="openDeviceAssignment"
                >分配设备</el-button
              >
            </div>
          </div>
        </div>
        <div class="playlist-files" v-if="playlistFiles.length > 0">
          <draggable
            v-model="playlistFiles"
            class="files-grid"
            item-key="id"
            @end="handleDragEnd"
          >
            <template #item="{ element }">
              <div class="file-item">
                <div class="file-preview">
                  <img :src="element.url" :alt="element.name" />
                  <div class="file-actions">
                    <el-button
                      :icon="View"
                      size="small"
                      @click="previewFile(element)"
                      circle
                    />
                    <el-button
                      :icon="Delete"
                      size="small"
                      type="danger"
                      @click="removeFromPlaylist(element)"
                      circle
                    />
                  </div>
                </div>
                <div class="file-name">{{ element.name }}</div>
              </div>
            </template>
          </draggable>
        </div>
        <div v-else class="empty-playlist">
          <el-empty description="暂无文件" />
        </div>
      </div>
    </div>

    <!-- 对话框组件 -->
    <CreatePlaylistDialog
      v-model="showCreateDialog"
      @success="handleCreateSuccess"
    />

    <EditPlaylistDialog
      v-model="showEditDialog"
      :playlist="editingPlaylist"
      @success="handleEditSuccess"
    />

    <DeviceAssignmentDialog
      v-model="showDeviceDialog"
      :playlist="selectedPlaylist"
      :files="playlistFiles"
    />

    <!-- 文件预览对话框 -->
    <FilePreviewDialog v-model="showPreviewDialog" :file="currentPreviewFile" />

    <!-- 图片裁剪对话框 -->
    <el-dialog
      v-model="showCropDialog"
      title=""
      width="90%"
      :close-on-click-modal="false"
      :show-close="false"
      :modal="true"
      :append-to-body="true"
      :destroy-on-close="false"
      class="crop-dialog no-animation"
    >
      <div class="crop-editor">
        <!-- 主裁剪区域 -->
        <div class="crop-main">
          <div class="crop-canvas-container">
            <canvas
              ref="cropCanvas"
              @mousedown="startCrop"
              @mousemove="updateCrop"
              @mouseup="endCrop"
              @mouseleave="endCrop"
            ></canvas>

            <!-- 裁剪框控制点 -->
            <div
              v-if="showCropBox"
              class="crop-resize-box"
              :style="cropBoxStyle"
            >
              <!-- 四个角的控制点 -->
              <div
                class="resize-handle corner-tl"
                @mousedown="startResize($event, 'tl')"
              ></div>
              <div
                class="resize-handle corner-tr"
                @mousedown="startResize($event, 'tr')"
              ></div>
              <div
                class="resize-handle corner-bl"
                @mousedown="startResize($event, 'bl')"
              ></div>
              <div
                class="resize-handle corner-br"
                @mousedown="startResize($event, 'br')"
              ></div>

              <!-- 四条边的控制点 -->
              <div
                class="resize-handle edge-t"
                @mousedown="startResize($event, 't')"
              ></div>
              <div
                class="resize-handle edge-r"
                @mousedown="startResize($event, 'r')"
              ></div>
              <div
                class="resize-handle edge-b"
                @mousedown="startResize($event, 'b')"
              ></div>
              <div
                class="resize-handle edge-l"
                @mousedown="startResize($event, 'l')"
              ></div>
            </div>
          </div>
        </div>

        <!-- 右侧预览和信息 -->
        <div class="crop-sidebar">
          <!-- 预览区域 -->
          <div class="crop-preview-section">
            <h4>预览</h4>
            <div class="preview-container">
              <canvas ref="previewCanvas" class="preview-canvas"></canvas>
              <div class="preview-info">
                <span>2560 × 1440</span>
              </div>
            </div>
          </div>

          <!-- 操作控制 -->
          <div class="controls-section">
            <h4>操作</h4>
            <div class="control-group">
              <label>旋转</label>
              <div class="button-group">
                <el-button
                  @click="rotateImage(-90)"
                  :icon="RefreshLeft"
                  size="small"
                >
                  逆时针
                </el-button>
                <el-button
                  @click="rotateImage(90)"
                  :icon="RefreshRight"
                  size="small"
                >
                  顺时针
                </el-button>
              </div>
            </div>

            <div class="control-group">
              <label>翻转</label>
              <div class="button-group">
                <el-button @click="flipImage('horizontal')" size="small">
                  水平翻转
                </el-button>
                <el-button @click="flipImage('vertical')" size="small">
                  垂直翻转
                </el-button>
              </div>
            </div>

            <!-- 重置按钮 -->
            <div class="control-group">
              <el-button
                @click="resetCrop"
                :icon="Refresh"
                size="small"
                style="width: 100%"
                plain
              >
                重置裁剪
              </el-button>
            </div>

            <!-- 操作提示 -->
            <div class="crop-tip"> 拖拽边角调整大小，拖拽中心移动位置 </div>

            <!-- 主要操作按钮 -->
            <div class="action-buttons">
              <el-button @click="cancelCrop" size="small" style="flex: 1">
                取消
              </el-button>
              <el-button
                type="primary"
                @click="confirmCrop"
                size="small"
                style="flex: 1"
                :loading="cropLoading"
                :disabled="cropLoading"
              >
                <el-icon v-if="!cropLoading"><Check /></el-icon>
                {{ cropLoading ? '处理中...' : '确认裁剪' }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 图片预览播放对话框 -->
    <el-dialog
      v-model="showImagePreview"
      width="50%"
      :show-close="true"
      :close-on-click-modal="false"
      class="image-preview-dialog"
      @close="stopAutoPlay"
    >
      <div
        class="image-preview-container"
        v-if="currentPreviewImages.length > 0"
      >
        <!-- 自动播放按钮 - 右上角 -->
        <el-button
          :type="isAutoPlaying ? 'danger' : 'primary'"
          @click="toggleAutoPlay"
          v-if="currentPreviewImages.length > 1"
          size="small"
          class="auto-play-btn"
          :icon="isAutoPlaying ? VideoPause : VideoPlay"
          round
        >
          {{ isAutoPlaying ? '暂停' : '自动播放' }}
        </el-button>

        <div class="image-display">
          <img
            :src="currentPreviewImages[currentImageIndex]"
            :alt="`图片 ${currentImageIndex + 1}`"
            class="preview-image"
          />
        </div>

        <div class="image-controls">
          <el-button
            :icon="ArrowLeft"
            @click="previousImage"
            circle
            size="large"
            class="nav-btn"
          />

          <div class="image-counter">
            <span class="current-index">{{ currentImageIndex + 1 }}</span>
            <span class="separator">/</span>
            <span class="total-count">{{ currentPreviewImages.length }}</span>
          </div>

          <el-button
            :icon="ArrowRight"
            @click="nextImage"
            circle
            size="large"
            class="nav-btn"
          />
        </div>
      </div>
      <div v-else class="empty-preview">
        <el-empty description="暂无图片内容" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, nextTick, watch } from 'vue';
  import {
    Plus,
    Check,
    Refresh,
    Edit,
    Delete,
    RefreshLeft,
    RefreshRight,
    ArrowLeft,
    ArrowRight,
    Loading,
    VideoPlay,
    VideoPause,
    View
  } from '@element-plus/icons-vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import draggable from 'vuedraggable';
  import DeviceAssignmentDialog from './components/DeviceAssignmentDialog.vue';
  import CreatePlaylistDialog from './components/CreatePlaylistDialog.vue';
  import EditPlaylistDialog from './components/EditPlaylistDialog.vue';
  import FilePreviewDialog from './components/FilePreviewDialog.vue';
  import * as TaiSendApi from '@/api/info/tai/taisend';
  import { dateFormatter } from '@/utils/formatTime';

  /** 播放主题配置页面 */
  defineOptions({ name: 'PlayThemeConfig' });

  // 响应式数据
  const fileInput = ref<HTMLInputElement>();
  const fileList = ref<any[]>([]);
  const selectedFiles = ref<number[]>([]);

  // 表格引用
  const playlistTableRef = ref();

  // 加载状态
  const saveLoading = ref(false);

  // 图片裁剪相关
  const showCropDialog = ref(false);
  const cropCanvas = ref<HTMLCanvasElement>();
  const previewCanvas = ref<HTMLCanvasElement>();
  const currentCropFile = ref<File | null>(null);
  const cropImage = ref<HTMLImageElement | null>(null);
  const cropRect = ref({ x: 0, y: 0, width: 0, height: 0 });
  const isDragging = ref(false);
  const isResizing = ref(false);
  const dragStart = ref({ x: 0, y: 0 });
  const resizeHandle = ref('');
  const resizeStart = ref({
    x: 0,
    y: 0,
    rect: { x: 0, y: 0, width: 0, height: 0 }
  });
  const showCropBox = ref(true);
  const imageRotation = ref(0);
  const zoomLevel = ref(1);
  const imageFlip = ref({ horizontal: false, vertical: false });

  // 播放主题相关
  const selectedPlaylist = ref<any>(null);
  const selectedPlaylistId = ref<number>();
  const playlistOptions = ref<any[]>([]);
  const playlistTableData = ref<any[]>([]);
  const playlistFiles = ref<any[]>([]);

  // 对话框状态
  const showDeviceDialog = ref(false);
  const showCreateDialog = ref(false);
  const showEditDialog = ref(false);
  const editingPlaylist = ref<any>(null);
  const showPreviewDialog = ref(false);
  const currentPreviewFile = ref<any>(null);

  const uploadLoading = ref(false);
  const cropLoading = ref(false);
  const isDragOver = ref(false);

  // 计算属性
  const totalFiles = computed(() => fileList.value.length);

  // 强制刷新标志
  const forceRefresh = ref(0);

  // 裁剪框样式 - 相对于canvas容器定位
  const cropBoxStyle = computed(() => {
    // 依赖forceRefresh来强制重新计算
    forceRefresh.value;

    const canvas = cropCanvas.value;
    if (!canvas || !cropRect.value.width || !cropRect.value.height) return {};

    // 计算canvas在容器中的偏移
    const container = canvas.parentElement;
    if (container) {
      // 使用canvas的style属性而不是getBoundingClientRect，避免DOM更新延迟问题
      const canvasStyleWidth =
        parseFloat(canvas.style.width) || canvas.offsetWidth;
      const canvasStyleHeight =
        parseFloat(canvas.style.height) || canvas.offsetHeight;

      // 获取容器尺寸
      const containerWidth = container.offsetWidth;
      const containerHeight = container.offsetHeight;

      // 计算canvas在容器中的居中偏移（因为canvas是居中显示的）
      const offsetX = (containerWidth - canvasStyleWidth) / 2;
      const offsetY = (containerHeight - canvasStyleHeight) / 2;

      return {
        left: `${offsetX + cropRect.value.x}px`,
        top: `${offsetY + cropRect.value.y}px`,
        width: `${cropRect.value.width}px`,
        height: `${cropRect.value.height}px`
      };
    }

    // 备用方案：直接使用cropRect的值
    return {
      left: `${cropRect.value.x}px`,
      top: `${cropRect.value.y}px`,
      width: `${cropRect.value.width}px`,
      height: `${cropRect.value.height}px`
    };
  });

  // 拖拽上传处理
  const handleDragOver = (event: DragEvent) => {
    event.preventDefault();
    isDragOver.value = true;
  };

  const handleDragLeave = (event: DragEvent) => {
    event.preventDefault();
    isDragOver.value = false;
  };

  const handleDrop = (event: DragEvent) => {
    event.preventDefault();
    isDragOver.value = false;

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      const file = files[0];

      // 验证文件类型
      const allowedTypes = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/bmp'
      ];
      if (!allowedTypes.includes(file.type)) {
        ElMessage.error('请选择JPG、PNG或BMP格式的图片文件');
        return;
      }

      // 验证文件大小 (10MB)
      const maxSize = 10 * 1024 * 1024;
      if (file.size > maxSize) {
        ElMessage.error('文件大小不能超过10MB');
        return;
      }

      // 打开裁剪对话框
      currentCropFile.value = file;
      openCropDialog(file);
    }
  };

  // 触发文件上传
  const triggerFileUpload = () => {
    fileInput.value?.click();
  };

  // 处理文件上传
  const handleFileUpload = async (event: Event) => {
    const target = event.target as HTMLInputElement;
    const files = target.files;

    if (!files || files.length === 0) return;

    // 只处理第一个文件进行裁剪
    const file = files[0];

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      ElMessage.warning(`文件 ${file.name} 不是有效的图片格式`);
      target.value = '';
      return;
    }

    // 验证文件大小
    if (file.size > 20 * 1024 * 1024) {
      ElMessage.warning(`文件 ${file.name} 大小超过20MB限制`);
      target.value = '';
      return;
    }

    // 打开裁剪对话框
    currentCropFile.value = file;
    openCropDialog(file);

    // 清空input值，允许重复选择同一文件
    target.value = '';
  };

  // 打开裁剪对话框
  const openCropDialog = (file: File) => {
    const img = new Image();
    img.onload = () => {
      cropImage.value = img;
      // 重置所有状态
      imageRotation.value = 0;
      zoomLevel.value = 1;
      imageFlip.value = { horizontal: false, vertical: false };

      // 重置裁剪框控制状态
      isDragging.value = false;
      isResizing.value = false;
      resizeHandle.value = '';
      dragStart.value = { x: 0, y: 0 };
      resizeStart.value = {
        x: 0,
        y: 0,
        rect: { x: 0, y: 0, width: 0, height: 0 }
      };
      cropRect.value = { x: 0, y: 0, width: 0, height: 0 };

      showCropDialog.value = true;

      // 等待对话框打开后初始化canvas
      setTimeout(() => {
        initCropCanvas();
        // 再次延迟确保控制点位置正确
        setTimeout(() => {
          // 强制重新计算控制点位置
          forceRefresh.value++;
        }, 10);
      }, 50);
    };
    img.src = URL.createObjectURL(file);
  };

  // 初始化裁剪canvas
  const initCropCanvas = () => {
    const canvas = cropCanvas.value;
    const img = cropImage.value;
    if (!canvas || !img) return;

    // 确保从干净状态开始
    cropRect.value = { x: 0, y: 0, width: 0, height: 0 };

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 设置canvas尺寸 - 确保显示完整图片
    const maxWidth = 800;
    const maxHeight = 500;

    // 获取原始图片尺寸
    const imgWidth = img.width;
    const imgHeight = img.height;
    const imgRatio = imgWidth / imgHeight;
    const maxRatio = maxWidth / maxHeight;

    let canvasWidth, canvasHeight;
    // 按比例缩放，确保图片完全显示在canvas内
    if (imgRatio > maxRatio) {
      // 图片更宽，以宽度为准
      canvasWidth = maxWidth;
      canvasHeight = maxWidth / imgRatio;
    } else {
      // 图片更高，以高度为准
      canvasHeight = maxHeight;
      canvasWidth = maxHeight * imgRatio;
    }

    // 设置高分辨率显示
    const dpr = window.devicePixelRatio || 1;
    canvas.width = canvasWidth * dpr;
    canvas.height = canvasHeight * dpr;
    canvas.style.width = canvasWidth + 'px';
    canvas.style.height = canvasHeight + 'px';

    // 缩放上下文以匹配设备像素比
    ctx.scale(dpr, dpr);

    // 初始化裁剪区域 - 检查图片比例并设置最大裁剪框
    const targetRatio = 16 / 9;
    const imageRatio = imgWidth / imgHeight;
    let cropWidth, cropHeight;

    // 如果图片本身就是16:9比例（或接近），直接使用整个画布
    if (Math.abs(imageRatio - targetRatio) < 0.01) {
      // 图片比例接近16:9，裁剪框覆盖整个画布
      cropWidth = canvasWidth;
      cropHeight = canvasHeight;
    } else {
      // 图片比例不是16:9，计算最大可能的16:9裁剪框
      const margin = 10; // 只留很小的边距
      const maxCropWidth = canvasWidth - margin;
      const maxCropHeight = canvasHeight - margin;

      if (maxCropWidth / maxCropHeight > targetRatio) {
        // 高度限制，以高度为准
        cropHeight = maxCropHeight;
        cropWidth = cropHeight * targetRatio;
      } else {
        // 宽度限制，以宽度为准
        cropWidth = maxCropWidth;
        cropHeight = cropWidth / targetRatio;
      }
    }

    cropRect.value = {
      x: (canvasWidth - cropWidth) / 2,
      y: (canvasHeight - cropHeight) / 2,
      width: cropWidth,
      height: cropHeight
    };

    drawCropOverlay();
    updatePreview();

    // 延迟更新控制点位置，确保DOM已更新
    nextTick(() => {
      // 强制触发cropBoxStyle重新计算
      forceRefresh.value++;

      // 再次延迟确保canvas尺寸完全更新
      setTimeout(() => {
        forceRefresh.value++;
      }, 50);
    });
  };

  // 监听对话框状态变化
  watch(showCropDialog, (newVal) => {
    if (newVal) {
      // 对话框打开时，延迟一点时间确保DOM完全渲染
      setTimeout(() => {
        if (cropCanvas.value && cropRect.value.width > 0) {
          // 强制重新计算控制点位置
          forceRefresh.value++;

          nextTick(() => {
            // 再次强制刷新
            forceRefresh.value++;
          });
        }
      }, 100);
    }
  });

  // 绘制裁剪覆盖层
  const drawCropOverlay = () => {
    const canvas = cropCanvas.value;
    const img = cropImage.value;
    if (!canvas || !img) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 设置高质量渲染
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    // 清除canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 获取显示尺寸（去除设备像素比影响）
    const displayWidth = parseFloat(canvas.style.width) || canvas.width;
    const displayHeight = parseFloat(canvas.style.height) || canvas.height;

    // 保存上下文状态
    ctx.save();

    // 应用变换
    const centerX = displayWidth / 2;
    const centerY = displayHeight / 2;

    ctx.translate(centerX, centerY);
    ctx.scale(
      zoomLevel.value * (imageFlip.value.horizontal ? -1 : 1),
      zoomLevel.value * (imageFlip.value.vertical ? -1 : 1)
    );
    ctx.rotate((imageRotation.value * Math.PI) / 180);

    // 绘制完整图片 - 使用显示尺寸
    ctx.drawImage(
      img,
      -displayWidth / 2,
      -displayHeight / 2,
      displayWidth,
      displayHeight
    );

    // 恢复上下文状态
    ctx.restore();

    // 绘制半透明遮罩
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 清除裁剪区域，显示原图
    ctx.save();
    ctx.globalCompositeOperation = 'destination-out';
    ctx.fillRect(
      cropRect.value.x,
      cropRect.value.y,
      cropRect.value.width,
      cropRect.value.height
    );
    ctx.restore();

    // 重新绘制裁剪区域的图片（清晰显示）
    ctx.save();
    ctx.beginPath();
    ctx.rect(
      cropRect.value.x,
      cropRect.value.y,
      cropRect.value.width,
      cropRect.value.height
    );
    ctx.clip();

    ctx.translate(centerX, centerY);
    ctx.scale(
      zoomLevel.value * (imageFlip.value.horizontal ? -1 : 1),
      zoomLevel.value * (imageFlip.value.vertical ? -1 : 1)
    );
    ctx.rotate((imageRotation.value * Math.PI) / 180);

    // 使用显示尺寸绘制图片
    ctx.drawImage(
      img,
      -displayWidth / 2,
      -displayHeight / 2,
      displayWidth,
      displayHeight
    );
    ctx.restore();

    // 绘制专业的裁剪框边框
    const { x, y, width, height } = cropRect.value;

    // 主边框 - 使用更专业的颜色和样式
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 2;
    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
    ctx.shadowBlur = 4;
    ctx.strokeRect(x, y, width, height);
    ctx.shadowBlur = 0; // 重置阴影

    // 内边框 - 增加层次感
    ctx.strokeStyle = 'rgba(0, 0, 0, 0.2)';
    ctx.lineWidth = 1;
    ctx.strokeRect(x + 1, y + 1, width - 2, height - 2);

    // 绘制精美的三分法则网格线
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.4)';
    ctx.lineWidth = 1;
    ctx.setLineDash([5, 5]); // 虚线效果

    const thirdWidth = width / 3;
    const thirdHeight = height / 3;

    ctx.beginPath();
    // 垂直线
    ctx.moveTo(x + thirdWidth, y + 8);
    ctx.lineTo(x + thirdWidth, y + height - 8);
    ctx.moveTo(x + thirdWidth * 2, y + 8);
    ctx.lineTo(x + thirdWidth * 2, y + height - 8);

    // 水平线
    ctx.moveTo(x + 8, y + thirdHeight);
    ctx.lineTo(x + width - 8, y + thirdHeight);
    ctx.moveTo(x + 8, y + thirdHeight * 2);
    ctx.lineTo(x + width - 8, y + thirdHeight * 2);
    ctx.stroke();

    ctx.setLineDash([]); // 重置虚线

    // 绘制角落装饰线 - 专业摄影软件风格
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 3;
    const cornerLength = 20;

    // 左上角
    ctx.beginPath();
    ctx.moveTo(x, y + cornerLength);
    ctx.lineTo(x, y);
    ctx.lineTo(x + cornerLength, y);
    ctx.stroke();

    // 右上角
    ctx.beginPath();
    ctx.moveTo(x + width - cornerLength, y);
    ctx.lineTo(x + width, y);
    ctx.lineTo(x + width, y + cornerLength);
    ctx.stroke();

    // 左下角
    ctx.beginPath();
    ctx.moveTo(x, y + height - cornerLength);
    ctx.lineTo(x, y + height);
    ctx.lineTo(x + cornerLength, y + height);
    ctx.stroke();

    // 右下角
    ctx.beginPath();
    ctx.moveTo(x + width - cornerLength, y + height);
    ctx.lineTo(x + width, y + height);
    ctx.lineTo(x + width, y + height - cornerLength);
    ctx.stroke();

    updatePreview();
  };

  // 更新预览
  const updatePreview = () => {
    const previewCanvasEl = previewCanvas.value;
    const canvas = cropCanvas.value;
    const img = cropImage.value;

    if (!previewCanvasEl || !canvas || !img) return;

    const previewCtx = previewCanvasEl.getContext('2d');
    if (!previewCtx) return;

    // 设置预览canvas尺寸
    const previewWidth = 200;
    const previewHeight = (previewWidth * 9) / 16; // 16:9比例

    previewCanvasEl.width = previewWidth;
    previewCanvasEl.height = previewHeight;

    // 清除预览canvas
    previewCtx.clearRect(0, 0, previewWidth, previewHeight);

    // 绘制裁剪预览
    previewCtx.save();

    const centerX = previewWidth / 2;
    const centerY = previewHeight / 2;

    previewCtx.translate(centerX, centerY);
    previewCtx.scale(
      imageFlip.value.horizontal ? -1 : 1,
      imageFlip.value.vertical ? -1 : 1
    );
    previewCtx.rotate((imageRotation.value * Math.PI) / 180);

    // 计算裁剪区域在原图中的位置 - 使用显示尺寸
    const displayWidth = parseFloat(canvas.style.width) || canvas.width;
    const displayHeight = parseFloat(canvas.style.height) || canvas.height;

    const scaleX = img.width / displayWidth;
    const scaleY = img.height / displayHeight;

    const sourceX = cropRect.value.x * scaleX;
    const sourceY = cropRect.value.y * scaleY;
    const sourceWidth = cropRect.value.width * scaleX;
    const sourceHeight = cropRect.value.height * scaleY;

    previewCtx.drawImage(
      img,
      sourceX,
      sourceY,
      sourceWidth,
      sourceHeight,
      -previewWidth / 2,
      -previewHeight / 2,
      previewWidth,
      previewHeight
    );

    previewCtx.restore();
  };

  // 开始调整大小
  const startResize = (event: MouseEvent, handle: string) => {
    event.preventDefault();
    event.stopPropagation();

    const canvas = cropCanvas.value;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    isResizing.value = true;
    resizeHandle.value = handle;
    resizeStart.value = {
      x: event.clientX,
      y: event.clientY,
      rect: { ...cropRect.value }
    };

    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', handleResize);
    document.addEventListener('mouseup', endResize);
  };

  // 处理调整大小
  const handleResize = (event: MouseEvent) => {
    if (!isResizing.value) return;

    const canvas = cropCanvas.value;
    if (!canvas) return;

    const deltaX = event.clientX - resizeStart.value.x;
    const deltaY = event.clientY - resizeStart.value.y;
    const originalRect = resizeStart.value.rect;
    const targetRatio = 16 / 9; // 16:9比例

    let newRect = { ...originalRect };

    switch (resizeHandle.value) {
      case 'tl': // 左上角
        newRect.width = originalRect.width - deltaX;
        newRect.height = newRect.width / targetRatio;
        newRect.x = originalRect.x + deltaX;
        newRect.y = originalRect.y + originalRect.height - newRect.height;
        break;
      case 'tr': // 右上角
        newRect.width = originalRect.width + deltaX;
        newRect.height = newRect.width / targetRatio;
        newRect.y = originalRect.y + originalRect.height - newRect.height;
        break;
      case 'bl': // 左下角
        newRect.width = originalRect.width - deltaX;
        newRect.height = newRect.width / targetRatio;
        newRect.x = originalRect.x + deltaX;
        break;
      case 'br': // 右下角
        newRect.width = originalRect.width + deltaX;
        newRect.height = newRect.width / targetRatio;
        break;
      case 't': // 上边
        newRect.height = originalRect.height - deltaY;
        newRect.width = newRect.height * targetRatio;
        newRect.x = originalRect.x + (originalRect.width - newRect.width) / 2;
        newRect.y = originalRect.y + deltaY;
        break;
      case 'b': // 下边
        newRect.height = originalRect.height + deltaY;
        newRect.width = newRect.height * targetRatio;
        newRect.x = originalRect.x + (originalRect.width - newRect.width) / 2;
        break;
      case 'l': // 左边
        newRect.width = originalRect.width - deltaX;
        newRect.height = newRect.width / targetRatio;
        newRect.x = originalRect.x + deltaX;
        newRect.y = originalRect.y + (originalRect.height - newRect.height) / 2;
        break;
      case 'r': // 右边
        newRect.width = originalRect.width + deltaX;
        newRect.height = newRect.width / targetRatio;
        newRect.y = originalRect.y + (originalRect.height - newRect.height) / 2;
        break;
    }

    // 限制最小尺寸
    const minWidth = 100;
    const minHeight = minWidth / targetRatio;
    if (newRect.width < minWidth) {
      newRect.width = minWidth;
      newRect.height = minHeight;
    }

    // 获取canvas显示尺寸
    const displayWidth = parseFloat(canvas.style.width) || canvas.width;
    const displayHeight = parseFloat(canvas.style.height) || canvas.height;

    // 边界检查 - 确保裁剪框不超出canvas显示区域
    if (newRect.x < 0) {
      newRect.x = 0;
    }
    if (newRect.y < 0) {
      newRect.y = 0;
    }
    if (newRect.x + newRect.width > displayWidth) {
      newRect.x = displayWidth - newRect.width;
      if (newRect.x < 0) {
        newRect.x = 0;
        newRect.width = displayWidth;
        newRect.height = newRect.width / targetRatio;
      }
    }
    if (newRect.y + newRect.height > displayHeight) {
      newRect.y = displayHeight - newRect.height;
      if (newRect.y < 0) {
        newRect.y = 0;
        newRect.height = displayHeight;
        newRect.width = newRect.height * targetRatio;
      }
    }

    cropRect.value = newRect;
    drawCropOverlay();
  };

  // 结束调整大小
  const endResize = () => {
    isResizing.value = false;
    resizeHandle.value = '';
    document.removeEventListener('mousemove', handleResize);
    document.removeEventListener('mouseup', endResize);
  };

  // 鼠标事件处理
  const startCrop = (event: MouseEvent) => {
    if (isResizing.value) return;

    const canvas = cropCanvas.value;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // 检查是否点击在裁剪区域内
    if (
      x >= cropRect.value.x &&
      x <= cropRect.value.x + cropRect.value.width &&
      y >= cropRect.value.y &&
      y <= cropRect.value.y + cropRect.value.height
    ) {
      isDragging.value = true;
      dragStart.value = { x: x - cropRect.value.x, y: y - cropRect.value.y };
    }
  };

  const updateCrop = (event: MouseEvent) => {
    if (!isDragging.value || isResizing.value) return;

    const canvas = cropCanvas.value;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // 更新裁剪区域位置
    let newX = x - dragStart.value.x;
    let newY = y - dragStart.value.y;

    // 获取canvas显示尺寸
    const displayWidth = parseFloat(canvas.style.width) || canvas.width;
    const displayHeight = parseFloat(canvas.style.height) || canvas.height;

    // 严格的边界检查 - 确保裁剪框完全在canvas显示区域内
    newX = Math.max(0, Math.min(newX, displayWidth - cropRect.value.width));
    newY = Math.max(0, Math.min(newY, displayHeight - cropRect.value.height));

    // 如果裁剪框比canvas还大，则居中显示
    if (cropRect.value.width > displayWidth) {
      newX = (displayWidth - cropRect.value.width) / 2;
    }
    if (cropRect.value.height > displayHeight) {
      newY = (displayHeight - cropRect.value.height) / 2;
    }

    cropRect.value.x = newX;
    cropRect.value.y = newY;

    drawCropOverlay();
  };

  const endCrop = () => {
    isDragging.value = false;
  };

  // 旋转图片
  const rotateImage = (angle: number) => {
    imageRotation.value = (imageRotation.value + angle) % 360;
    if (imageRotation.value < 0) imageRotation.value += 360;
    drawCropOverlay();
  };

  // 翻转图片
  const flipImage = (direction: 'horizontal' | 'vertical') => {
    if (direction === 'horizontal') {
      imageFlip.value.horizontal = !imageFlip.value.horizontal;
    } else {
      imageFlip.value.vertical = !imageFlip.value.vertical;
    }
    drawCropOverlay();
  };

  // 重置裁剪区域
  const resetCrop = () => {
    imageRotation.value = 0;
    zoomLevel.value = 1;
    imageFlip.value = { horizontal: false, vertical: false };
    initCropCanvas();
  };

  // 取消裁剪
  const cancelCrop = () => {
    // 重置所有状态
    isDragging.value = false;
    isResizing.value = false;
    resizeHandle.value = '';
    dragStart.value = { x: 0, y: 0 };
    resizeStart.value = {
      x: 0,
      y: 0,
      rect: { x: 0, y: 0, width: 0, height: 0 }
    };
    cropRect.value = { x: 0, y: 0, width: 0, height: 0 };

    // 移除事件监听器
    document.removeEventListener('mousemove', handleResize);
    document.removeEventListener('mouseup', endResize);
    document.removeEventListener('mousemove', updateCrop);
    document.removeEventListener('mouseup', endCrop);

    showCropDialog.value = false;
    currentCropFile.value = null;
    cropImage.value = null;
  };

  // 确认裁剪
  const confirmCrop = async () => {
    const canvas = cropCanvas.value;
    const img = cropImage.value;
    const file = currentCropFile.value;

    if (!canvas || !img || !file) return;

    try {
      cropLoading.value = true;

      // 创建新的canvas进行裁剪
      const cropCanvasElement = document.createElement('canvas');
      const cropCtx = cropCanvasElement.getContext('2d');

      if (!cropCtx) {
        throw new Error('无法创建裁剪canvas');
      }

      // 设置输出尺寸为2560x1440
      cropCanvasElement.width = 2560;
      cropCanvasElement.height = 1440;

      // 设置高质量渲染
      cropCtx.imageSmoothingEnabled = true;
      cropCtx.imageSmoothingQuality = 'high';

      // 计算原图中的裁剪区域 - 使用显示尺寸
      const displayWidth = parseFloat(canvas.style.width) || canvas.width;
      const displayHeight = parseFloat(canvas.style.height) || canvas.height;

      const scaleX = img.width / displayWidth;
      const scaleY = img.height / displayHeight;

      const sourceX = cropRect.value.x * scaleX;
      const sourceY = cropRect.value.y * scaleY;
      const sourceWidth = cropRect.value.width * scaleX;
      const sourceHeight = cropRect.value.height * scaleY;

      // 保存上下文状态
      cropCtx.save();

      // 应用变换到输出canvas
      const centerX = 2560 / 2;
      const centerY = 1440 / 2;

      cropCtx.translate(centerX, centerY);
      cropCtx.scale(
        imageFlip.value.horizontal ? -1 : 1,
        imageFlip.value.vertical ? -1 : 1
      );
      cropCtx.rotate((imageRotation.value * Math.PI) / 180);

      // 绘制裁剪后的图片，应用所有变换
      cropCtx.drawImage(
        img,
        sourceX,
        sourceY,
        sourceWidth,
        sourceHeight,
        -centerX,
        -centerY,
        2560,
        1440
      );

      cropCtx.restore();

      // 转换为Blob
      cropCanvasElement.toBlob(
        async (blob) => {
          if (blob) {
            try {
              // 创建新的File对象
              const croppedFile = new File(
                [blob],
                file.name.replace(/\.(jpg|jpeg|png|bmp)$/i, '_2560x1440.$1'),
                {
                  type: file.type,
                  lastModified: Date.now()
                }
              );

              // 创建FormData对象上传文件
              const formData = new FormData();
              formData.append('file', croppedFile);

              // 调用上传API
              const uploadResult = await TaiSendApi.uploadFile(formData);

              if (uploadResult && uploadResult.data) {
                ElMessage.success('图片上传成功！');

                // 直接刷新文件列表获取最新数据
                await loadFileList();
              } else {
                throw new Error('上传响应数据异常');
              }
            } catch (uploadError: any) {
              console.error('文件上传失败:', uploadError);
              ElMessage.error(
                `文件上传失败: ${uploadError?.message || '未知错误'}`
              );
            }

            // 重置所有状态并关闭对话框
            isDragging.value = false;
            isResizing.value = false;
            resizeHandle.value = '';
            dragStart.value = { x: 0, y: 0 };
            resizeStart.value = {
              x: 0,
              y: 0,
              rect: { x: 0, y: 0, width: 0, height: 0 }
            };
            cropRect.value = { x: 0, y: 0, width: 0, height: 0 };

            // 移除事件监听器
            document.removeEventListener('mousemove', handleResize);
            document.removeEventListener('mouseup', endResize);
            document.removeEventListener('mousemove', updateCrop);
            document.removeEventListener('mouseup', endCrop);

            showCropDialog.value = false;
            currentCropFile.value = null;
            cropImage.value = null;
          } else {
            ElMessage.error('图片裁剪失败');
          }
          cropLoading.value = false;
        },
        file.type,
        0.9
      );
    } catch (error: any) {
      console.error('裁剪失败:', error);
      ElMessage.error(`裁剪失败: ${error?.message || '未知错误'}`);
      cropLoading.value = false;
    }
  };

  // 切换文件选择状态
  const toggleFileSelection = (fileId: number) => {
    const index = selectedFiles.value.indexOf(fileId);
    if (index > -1) {
      selectedFiles.value.splice(index, 1);
    } else {
      selectedFiles.value.push(fileId);
    }
  };

  // 删除选中的文件
  const clearSelection = async () => {
    if (selectedFiles.value.length === 0) {
      ElMessage.warning('请先选择要删除的文件');
      return;
    }

    try {
      await ElMessageBox.confirm(
        `确定要删除选中的 ${selectedFiles.value.length} 个文件吗？`,
        '批量删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      );

      // 调用删除接口，传入选中的文件ID数组
      const idsToDelete = selectedFiles.value.join(',');
      await TaiSendApi.deleteFile(idsToDelete);

      // 从文件列表中移除被删除的文件
      fileList.value = fileList.value.filter(
        (file) => !selectedFiles.value.includes(file.id)
      );

      // 清空选中列表
      const deletedCount = selectedFiles.value.length;
      selectedFiles.value = [];

      ElMessage.success(`成功删除 ${deletedCount} 个文件`);
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('批量删除文件失败:', error);
        ElMessage.error('批量删除文件失败');
      }
    }
  };

  // 添加到播放列表
  const addToPlaylist = () => {
    if (!selectedPlaylist.value) {
      ElMessage.warning('请先选择播放主题');
      return;
    }

    if (selectedFiles.value.length === 0) {
      ElMessage.warning('请先选择要添加的文件');
      return;
    }

    // 获取选中的文件
    const selectedFileObjects = fileList.value.filter((file) =>
      selectedFiles.value.includes(file.id)
    );

    // 添加到播放列表，避免重复
    selectedFileObjects.forEach((file) => {
      if (!playlistFiles.value.find((pf) => pf.id === file.id)) {
        playlistFiles.value.push({ ...file });
      }
    });

    // 清空选择
    selectedFiles.value = [];
  };

  // 保存配置
  const handleSave = async () => {
    if (!selectedPlaylist.value) {
      ElMessage.warning('请先选择播放主题');
      return;
    }

    if (playlistFiles.value.length === 0) {
      ElMessage.warning('播放列表为空，请先添加文件');
      return;
    }

    // 开始加载
    saveLoading.value = true;

    try {
      // 准备分配数据：播放主题ID和文件详细信息列表
      const assignData = {
        playlistId: selectedPlaylist.value.id,
        files: playlistFiles.value.map((file) => {
          // 确保externalUuid存在，否则使用id作为fallback
          const uuid = file.externalUuid || file.id;
          return {
            fileName: uuid + '.jpg',
            url: file.url,
            externalUuid: file.externalUuid || file.id // 确保externalUuid字段不为空
          };
        })
      };

      console.log('保存播放主题文件分配:', assignData);

      // 调用分配接口
      await TaiSendApi.assignFile(assignData);

      ElMessage.success('保存成功');
    } catch (error) {
      console.error('保存失败:', error);
      ElMessage.error('保存失败');
    } finally {
      // 结束加载
      saveLoading.value = false;
    }
  };

  // 打开设备分配对话框
  const openDeviceAssignment = () => {
    if (!selectedPlaylist.value) {
      ElMessage.warning('请先选择播放主题');
      return;
    }
    showDeviceDialog.value = true;
  };

  // 打开新建主题对话框
  const openCreateDialog = () => {
    showCreateDialog.value = true;
  };

  // 测试表格选中功能
  const testTableSelection = () => {
    console.log('测试表格选中功能');
    console.log('当前表格数据:', playlistTableData.value);
    console.log('表格引用:', playlistTableRef.value);

    if (playlistTableData.value.length > 0) {
      const firstRow = playlistTableData.value[0];
      console.log('尝试选中第一行:', firstRow);

      // 设置选中状态
      selectedPlaylist.value = firstRow;
      selectedPlaylistId.value = firstRow.id;

      // 设置表格当前行
      if (playlistTableRef.value) {
        playlistTableRef.value.setCurrentRow(firstRow);
        console.log('已调用setCurrentRow');
      }
    }
  };

  // 处理新建主题成功
  const handleCreateSuccess = async (result: any) => {
    console.log('新建主题成功，返回ID:', result);

    // 重新加载播放主题列表
    await loadPlaylists();
    console.log('刷新后的播放主题列表:', playlistTableData.value);

    // 根据返回的ID查找新创建的主题
    const newPlaylist = playlistTableData.value.find(
      (item) => item.id === result
    );

    if (newPlaylist) {
      console.log('找到新创建的主题:', newPlaylist);

      // 设置选中状态
      selectedPlaylist.value = newPlaylist;
      selectedPlaylistId.value = newPlaylist.id;

      // 等待DOM更新后设置表格当前行
      await nextTick();

      setTimeout(() => {
        console.log('尝试设置表格当前行');
        console.log('表格引用:', playlistTableRef.value);

        if (playlistTableRef.value && newPlaylist) {
          playlistTableRef.value.setCurrentRow(newPlaylist);
          console.log('已调用setCurrentRow，选中主题:', newPlaylist.name);
        } else {
          console.log('表格引用不存在');
        }
      }, 100);
    } else {
      console.log('未找到新创建的主题，ID:', result);
    }

    showCreateDialog.value = false;
  };

  // 处理编辑主题成功
  const handleEditSuccess = async () => {
    // 保存当前编辑的主题ID
    const editingId = editingPlaylist.value?.id;

    // 重新加载播放主题列表
    await loadPlaylists();

    // 找到编辑后的主题并选中
    if (editingId) {
      const updatedPlaylist = playlistTableData.value.find(
        (p) => p.id === editingId
      );
      if (updatedPlaylist) {
        // 设置选中状态
        selectedPlaylist.value = updatedPlaylist;
        selectedPlaylistId.value = updatedPlaylist.id;

        // 等待DOM更新后设置表格当前行
        await nextTick();

        setTimeout(() => {
          if (playlistTableRef.value && updatedPlaylist) {
            playlistTableRef.value.setCurrentRow(updatedPlaylist);
            console.log('编辑成功后设置表格当前行');
          }
        }, 100);
      }
    }

    showEditDialog.value = false;
  };

  // 播放主题管理相关方法
  const togglePlaylistView = () => {
    // 切换播放主题视图（列表/卡片）
    console.log('切换播放主题视图');
  };

  const editSelectedPlaylist = () => {
    if (selectedPlaylist.value) {
      editPlaylist(selectedPlaylist.value);
    }
  };

  const previewSelectedPlaylist = () => {
    if (selectedPlaylist.value) {
      previewPlaylist(selectedPlaylist.value);
    }
  };

  const deleteSelectedPlaylist = () => {
    if (selectedPlaylist.value) {
      deletePlaylist(selectedPlaylist.value);
    }
  };

  const editPlaylist = (playlist: any) => {
    editingPlaylist.value = playlist;
    showEditDialog.value = true;
  };

  const previewPlaylist = (playlist: any) => {
    // 预览播放主题 - 打开图片自动播放
    console.log('预览播放主题:', playlist);

    // 获取当前播放主题的图片列表
    if (playlistFiles.value.length > 0) {
      currentPreviewImages.value = playlistFiles.value.map((file) => file.url);
      currentImageIndex.value = 0;
      showImagePreview.value = true;

      // 如果有多张图片，自动开始播放
      if (currentPreviewImages.value.length > 1) {
        setTimeout(() => {
          startAutoPlay();
        }, 1000); // 1秒后开始自动播放
      }
    } else {
      ElMessage.warning('该播放主题暂无图片内容');
    }
  };

  // 添加图片预览相关状态
  const showImagePreview = ref(false);
  const currentPreviewImages = ref<string[]>([]);
  const currentImageIndex = ref(0);
  const isAutoPlaying = ref(false);
  const autoPlayInterval = ref<number | null>(null);
  const playSpeed = ref(2000); // 3秒切换一次

  // 图片预览控制方法
  const previousImage = () => {
    if (currentImageIndex.value > 0) {
      currentImageIndex.value--;
    } else {
      // 循环到最后一张
      currentImageIndex.value = currentPreviewImages.value.length - 1;
    }
  };

  const nextImage = () => {
    if (currentImageIndex.value < currentPreviewImages.value.length - 1) {
      currentImageIndex.value++;
    } else {
      // 循环到第一张
      currentImageIndex.value = 0;
    }
  };

  // 开始自动播放
  const startAutoPlay = () => {
    if (currentPreviewImages.value.length <= 1) return;

    isAutoPlaying.value = true;
    autoPlayInterval.value = setInterval(() => {
      nextImage();
    }, playSpeed.value);
  };

  // 停止自动播放
  const stopAutoPlay = () => {
    isAutoPlaying.value = false;
    if (autoPlayInterval.value) {
      clearInterval(autoPlayInterval.value);
      autoPlayInterval.value = null;
    }
  };

  // 切换自动播放状态
  const toggleAutoPlay = () => {
    if (isAutoPlaying.value) {
      stopAutoPlay();
    } else {
      startAutoPlay();
    }
  };

  const deletePlaylist = async (playlist: any) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除播放主题 "${playlist.name}" 吗？`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      );

      // 调用删除API
      await TaiSendApi.deleteTaiplaylistInfo(playlist.id);

      ElMessage.success('删除成功');

      // 重新加载播放主题列表
      await loadPlaylists();

      // 如果删除的是当前选中的主题，清空选择
      if (selectedPlaylist.value?.id === playlist.id) {
        selectedPlaylist.value = null;
        selectedPlaylistId.value = undefined;
        playlistFiles.value = [];

        // 清空表格选中状态
        if (playlistTableRef.value) {
          playlistTableRef.value.setCurrentRow(null);
        }
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除播放主题失败:', error);
        ElMessage.error('删除失败');
      }
    }
  };

  const handlePlaylistChange = (playlistId: number) => {
    const playlist = playlistOptions.value.find((p) => p.id === playlistId);
    selectedPlaylist.value = playlist || null;

    // 加载该播放主题的文件列表
    if (playlist) {
      loadPlaylistFiles(playlist.id);
    } else {
      playlistFiles.value = [];
    }
  };

  const handlePlaylistRowSelect = (row: any) => {
    console.log('表格行选择:', row);
    selectedPlaylist.value = row;
    selectedPlaylistId.value = row?.id;

    // 加载该播放主题的文件列表
    if (row) {
      loadPlaylistFiles(row.id);
    } else {
      playlistFiles.value = [];
    }
  };

  // 播放列表文件管理
  const handleDragEnd = () => {
    // 拖拽排序完成
    console.log('拖拽排序完成');
  };

  const previewFile = (file: any) => {
    currentPreviewFile.value = file;
    showPreviewDialog.value = true;
  };

  const removeFromPlaylist = (file: any) => {
    const index = playlistFiles.value.findIndex((pf) => pf.id === file.id);
    if (index > -1) {
      playlistFiles.value.splice(index, 1);
      ElMessage.success('已从播放列表中移除');
    }
  };

  // 删除文件
  const deleteFile = async (file: any) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除文件 "${file.name}" 吗？`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      );

      // 调用删除接口
      await TaiSendApi.deleteFile(file.id);

      // 从文件列表中移除
      const index = fileList.value.findIndex((f) => f.id === file.id);
      if (index > -1) {
        fileList.value.splice(index, 1);
      }

      // 从选中列表中移除
      const selectedIndex = selectedFiles.value.indexOf(file.id);
      if (selectedIndex > -1) {
        selectedFiles.value.splice(selectedIndex, 1);
      }

      ElMessage.success('文件删除成功');
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('删除文件失败:', error);
        ElMessage.error('删除文件失败');
      }
    }
  };

  // 清空播放列表文件
  const clearPlaylistFiles = () => {
    playlistFiles.value = [];
    ElMessage.success('已清空播放列表');
  };

  // 数据加载方法
  const loadPlaylists = async () => {
    try {
      const params = {
        pageNo: 1,
        pageSize: 100
      };

      const result = await TaiSendApi.getTaiplaylistInfoPage(params);
      playlistOptions.value = result.list;
      playlistTableData.value = result.list;
    } catch (error) {
      console.error('加载播放主题失败:', error);
      ElMessage.error('加载播放主题失败');
    }
  };

  const loadPlaylistFiles = async (playlistId: number) => {
    try {
      const result = await TaiSendApi.getTaifilePlaylistInfoList(playlistId);
      console.log('播放主题文件列表返回数据:', result);

      // 处理返回的数据，映射字段名
      const processedFiles = (result.list || result || []).map((file: any) => ({
        id: file.id,
        name: file.name, // 附件名称
        url: file.imageSrc, // 图片地址
        size: file.size || 0,
        contentType: file.contentType || 'image/jpeg',
        status: file.status || 1,
        createTime: file.createTime,
        updateTime: file.updateTime,
        externalUuid: file.externalUuid || file.uuid || file.fileUuid || file.id // 确保有externalUuid
      }));

      playlistFiles.value = processedFiles;
      console.log('处理后的播放主题文件列表:', playlistFiles.value);
    } catch (error) {
      console.error('加载播放列表文件失败:', error);
      ElMessage.error('加载播放列表文件失败');
      playlistFiles.value = [];
    }
  };

  const loadFileList = async () => {
    try {
      const params = {
        pageNo: 1,
        pageSize: 100
      };

      const result = await TaiSendApi.getTaifileInfoPage(params);

      // 处理返回的数据，确保包含正确的图片URL
      const processedFiles = (result.list || []).map((file: any) => ({
        id: file.id,
        name: file.name || file.fileName || '未知文件',
        url: file.url || file.fileUrl || file.path, // 图片地址
        size: file.size || file.fileSize || 0,
        contentType: file.contentType || file.type || 'image/jpeg',
        status: file.status || 1,
        createTime: file.createTime,
        updateTime: file.updateTime,
        externalUuid: file.externalUuid || file.uuid || file.fileUuid // 保存外部UUID
      }));

      fileList.value = processedFiles;
    } catch (error) {
      console.error('加载文件列表失败:', error);
      ElMessage.error('加载文件列表失败');
    }
  };

  // 初始化模拟数据
  const initMockData = () => {
    // 模拟文件数据
    const mockFiles: any[] = [];
    for (let i = 1; i <= 18; i++) {
      mockFiles.push({
        id: i,
        name: `image_2560x1440_${i}.png`,
        url: 'https://picsum.photos/2560/1440?random=' + i, // 使用2560x1440比例的图片
        size: 1024 * 1024 * 3, // 3MB
        contentType: 'image/png',
        status: 1
      });
    }
    fileList.value = mockFiles;

    // 模拟播放主题数据
    const mockPlaylists: any[] = [];
    for (let i = 1; i <= 8; i++) {
      mockPlaylists.push({
        id: i,
        name: `我是列表名称${i > 1 ? i : '名称名称...'}`,
        playType: i % 2,
        createTime: '2025.08.09 12:00:00',
        status: 1
      });
    }
    playlistOptions.value = mockPlaylists;
    playlistTableData.value = mockPlaylists;

    // 模拟播放列表文件数据
    const mockPlaylistFiles: any[] = [];
    for (let i = 1; i <= 6; i++) {
      mockPlaylistFiles.push({
        id: i + 100,
        name: `playlist_2560x1440_${i}.png`,
        url: 'https://picsum.photos/2560/1440?random=' + (i + 100), // 使用2560x1440比例
        size: 1024 * 1024 * 3,
        contentType: 'image/png',
        status: 1
      });
    }
    playlistFiles.value = mockPlaylistFiles;
  };

  // 组件挂载时加载数据
  onMounted(() => {
    loadFileList(); // 使用真实接口加载文件列表
    loadPlaylists(); // 加载播放主题列表
    // initMockData(); // 使用模拟数据
  });
</script>

<style lang="scss" scoped>
  @use 'element-plus/theme-chalk/src/mixins/function.scss' as *;
  .play-theme-config {
    padding: 16px;
    background: getCssVar('bg-color', 'page');
    height: 100vh;
    width: 100%; /* 确保占满可用宽度 */
    box-sizing: border-box; /* 包含padding在内的宽度计算 */
    font-family:
      -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue',
      Arial, sans-serif;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 防止页面滚动 */
    transition: background-color 0.3s ease;
  }

  /* 顶部区域 - 响应式布局 */
  .top-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 16px;
    width: 100%;
    flex-shrink: 0;
  }

  /* 底部区域 - 响应式布局 */
  .bottom-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
    flex: 1;
    min-height: 0;
    width: 100%;
    max-width: 100%;
  }

  /* 播放主题管理区 - 响应式默认样式 */
  .playlist-management {
    background: var(--el-bg-color);
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    margin-bottom: 16px;
  }

  /* 播放内容区 - 响应式默认样式 */
  .playlist-content {
    background: var(--el-bg-color);
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  /* 新的卡片头部样式 - 一排式布局 */
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px;
    background: getCssVar('fill-color', 'light');
    border-bottom: 1px solid getCssVar('border-color', 'lighter');
    height: 56px;
    box-sizing: border-box;
    transition: all 0.3s ease;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 0;
    flex-shrink: 0;
  }

  .header-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 8px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
    flex-shrink: 0;
  }

  .header-icon:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  }

  .header-icon .el-icon {
    color: white;
    font-size: 18px;
  }

  .header-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
  }

  .header-center {
    display: flex;
    align-items: center;
    gap: 20px;
    flex: 1;
    justify-content: center;
    min-width: 0;
  }

  .status-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 13px;
    color: var(--el-text-color-regular);
  }

  .status-item .el-icon {
    font-size: 14px;
    color: var(--el-color-primary);
  }

  .header-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
  }

  /* 右侧头部内容样式 */
  .header-right-content {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 20px;
    width: 100%;
  }

  .device-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .content-count {
    font-size: 13px;
    color: var(--el-text-color-regular);
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .header-actions .el-button {
    border-radius: 5px;
    font-weight: 500;
    padding: 6px 12px;
    font-size: 13px;
  }

  /* 圆形图标按钮样式 */
  .header-actions .el-button.is-circle {
    width: 28px;
    height: 28px;
    padding: 0;
    border: 1px solid var(--el-border-color);
    background: var(--el-bg-color);
    color: var(--el-text-color-regular);
    transition: all 0.3s;
  }

  .header-actions .el-button.is-circle:hover {
    border-color: var(--el-color-primary);
    color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(64, 158, 255, 0.2);
  }

  .header-actions .el-button.is-circle.el-button--danger:hover {
    border-color: var(--el-color-danger);
    color: var(--el-color-danger);
    background: var(--el-color-danger-light-9);
    box-shadow: 0 4px 8px rgba(245, 108, 108, 0.2);
  }

  /* 美化保存和分配设备按钮 */
  .save-btn {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #cbd5e1;
    color: #475569;
    font-weight: 500;
    transition: all 0.3s;
  }

  .save-btn:hover {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    border-color: #94a3b8;
    color: #334155;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(148, 163, 184, 0.3);
  }

  .assign-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;
    font-weight: 500;
    transition: all 0.3s;
  }

  .assign-btn:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  }

  /* Element Plus 表格当前行高亮样式覆盖 */
  :deep(.playlist-table .el-table__row.current-row) {
    background-color: #409eff !important;
  }

  :deep(.playlist-table .el-table__row.current-row td) {
    background-color: #409eff !important;
    color: white !important;
    font-weight: 500;
  }

  :deep(.playlist-table .el-table__row.current-row:hover) {
    background-color: #337ecc !important;
  }

  :deep(.playlist-table .el-table__row.current-row:hover td) {
    background-color: #337ecc !important;
    color: white !important;
  }

  /* 表格行悬停效果 */
  :deep(.el-table__row:hover) {
    background-color: #f0f9ff !important;
    transform: translateY(-1px);
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .playlist-table-container {
    padding: 0 16px 16px 16px;
    flex: 1;
    overflow: hidden;
    width: 100%;
    box-sizing: border-box;
  }

  .playlist-table {
    border-radius: 8px;
    overflow: hidden;
    font-size: 13px;
    width: 100%;
    table-layout: fixed;
  }

  /* 优化small表格的样式 */
  :deep(.playlist-table.el-table--small) {
    font-size: 14px;
  }

  :deep(.playlist-table.el-table--small .el-table__cell) {
    padding: 9px 1px;
    font-size: 13px;
  }

  :deep(.playlist-table .el-table__header-wrapper) {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  }

  :deep(.playlist-table .el-table__header th) {
    background: transparent;
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
    padding: 14px 8px;
  }

  :deep(.playlist-table .el-table__body tr) {
    transition: all 0.2s ease;
  }

  /* 表格响应式优化 */
  @media (max-width: 400px) {
    .playlist-table-container {
      padding: 0 8px 8px 8px;
    }

    :deep(.playlist-table .el-table__cell) {
      padding: 8px 4px;
      font-size: 13px;
    }

    :deep(.playlist-table .el-table__header th) {
      padding: 10px 4px;
      font-size: 13px;
    }
  }

  /* 播放列表内容区 */
  .playlist-content {
    background: transparent; /* 移除独立背景 */
    border-radius: 0; /* 移除独立圆角 */
    box-shadow: none; /* 移除独立阴影 */
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    max-width: 100%; /* 防止超出容器 */
    min-width: 0; /* 允许收缩 */
  }

  /* 优化按钮样式 */
  .playlist-actions .el-button {
    border-radius: 6px;
    font-weight: 500;
  }

  .playlist-actions .el-button--primary {
    background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
    border: none;
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
  }

  .playlist-actions .el-button--primary:hover {
    background: linear-gradient(135deg, #337ecc 0%, #529b2e 100%);
    box-shadow: 0 4px 8px rgba(64, 158, 255, 0.4);
    transform: translateY(-1px);
  }

  .playlist-actions .el-button.is-plain {
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color);
    color: var(--el-text-color-regular);
  }

  .playlist-actions .el-button.is-plain:hover {
    background: var(--el-color-primary-light-9);
    border-color: var(--el-color-primary);
    color: var(--el-color-primary);
  }

  .playlist-actions .el-button--danger.is-plain:hover {
    background: var(--el-color-danger-light-9);
    border-color: var(--el-color-danger);
    color: var(--el-color-danger);
  }

  /* 文件头部操作区 */
  .files-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 16px;
    background: var(--el-fill-color-lighter);
    border-radius: 6px;
  }

  .pagination-info {
    font-size: 14px;
    color: var(--el-text-color-regular);
  }

  /* 区域标题 */
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .section-header h3 {
    margin: 0;
    font-size: 16px;
    color: var(--el-text-color-primary);
  }

  .header-actions {
    display: flex;
    gap: 8px;
  }
  .left-header-actions {
    display: flex;
  }

  /* 操作按钮美化样式 */
  .action-btn {
    border-radius: 8px !important;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  .add-btn {
    background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%) !important;
  }

  .add-btn:hover {
    background: linear-gradient(135deg, #5daf34 0%, #7bc143 100%) !important;
  }

  .edit-btn {
    background: linear-gradient(135deg, #e6a23c 0%, #f0c78a 100%) !important;
  }

  .edit-btn:hover {
    background: linear-gradient(135deg, #d39e33 0%, #ebc077 100%) !important;
  }

  .play-btn {
    background: linear-gradient(135deg, #409eff 0%, #79bbff 100%) !important;
  }

  .play-btn:hover {
    background: linear-gradient(135deg, #337ecc 0%, #66b1ff 100%) !important;
  }

  .delete-btn {
    background: linear-gradient(135deg, #f56c6c 0%, #f89898 100%) !important;
  }

  .delete-btn:hover {
    background: linear-gradient(135deg, #f25555 0%, #f78989 100%) !important;
  }

  .action-btn:disabled {
    opacity: 0.5;
    transform: none !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
    cursor: not-allowed;
  }

  .content-info {
    font-size: 14px;
    color: var(--el-text-color-regular);
  }

  /* 文件上传区 */
  .upload-section {
    background: getCssVar('fill-color', 'light');
    border: 1px solid getCssVar('border-color', 'lighter');
    border-radius: 6px;
    padding: 0;
    height: 100%;
    width: 100%; /* 确保占满容器宽度 */
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
  }

  .upload-area {
    position: relative; /* 为内部绝对定位元素提供定位上下文 */
    border: 2px dashed getCssVar('border-color');
    border-radius: 6px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: getCssVar('bg-color');
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .upload-area:hover {
    border-color: getCssVar('color-primary');
    background: getCssVar('color-primary-light-9');
  }

  .upload-area.drag-over {
    border-color: getCssVar('color-primary');
    background: getCssVar('color-primary-light-8');
    border-style: solid;
    transform: scale(1.02);
  }

  .upload-icon {
    font-size: 48px;
    color: #409eff;
    margin-bottom: 16px;
    pointer-events: none;
  }

  .upload-text {
    font-size: 16px;
    color: #303133;
    margin-bottom: 8px;
    font-weight: 500;
  }

  .upload-hint {
    font-size: 12px;
    color: #909399;
    line-height: 1.5;
    text-align: center;
  }

  /* 文件展示区 */
  .files-section {
    background: getCssVar('bg-color');
    border: 1px solid getCssVar('border-color', 'lighter');
    border-radius: 6px;
    padding: 0;
    height: 100%;
    width: 100%; /* 确保占满可用宽度 */
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .files-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid getCssVar('border-color', 'lighter');
    background: getCssVar('fill-color', 'light');
    transition: all 0.3s ease;
  }

  .file-count {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #606266;
  }

  .delete-icon {
    cursor: pointer;
    color: #f56c6c;
    font-size: 16px;
  }

  .delete-icon:hover {
    color: #f78989;
  }

  .playlist-files {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden; /* 防止横向滚动 */
    height: 0; /* 关键：让flex子元素可以正确收缩 */
    width: 100%; /* 确保占满容器宽度 */
    max-width: 100%; /* 防止超出容器 */
    min-height: 0;
  }

  .empty-playlist {
    padding: 20px;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* 文件计数样式优化 */
  .file-count {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--el-text-color-regular);
  }

  .delete-icon {
    cursor: pointer;
    color: var(--el-text-color-placeholder);
    transition: color 0.3s;
  }

  .delete-icon:hover {
    color: var(--el-color-danger);
  }

  .files-grid {
    display: grid;
    grid-template-columns: repeat(
      auto-fill,
      minmax(160px, 1fr)
    ); /* 使用minmax防止横向滚动 */
    gap: 12px;
    padding: 16px;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden; /* 防止横向滚动 */
    min-height: 0; /* 允许flex子元素收缩 */
    justify-content: start; /* 左对齐 */
    width: 100%; /* 确保占满容器宽度 */
  }

  .file-item {
    border: 2px solid transparent;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s ease;
    background: getCssVar('bg-color');
    position: relative;
    box-shadow: getCssVar('box-shadow', 'lighter');
    width: 160px; /* 16:9比例宽度 */
    height: 118px; /* 总高度：90px预览 + 28px文件名 */
  }

  .file-item:hover {
    border-color: getCssVar('color-primary');
    transform: translateY(-1px);
    box-shadow: getCssVar('box-shadow', 'light');
  }

  .file-item.selected {
    border-color: getCssVar('color-primary');
    box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2);
  }

  .file-preview {
    position: relative;
    width: 100%;
    height: 90px; /* 16:9比例的预览区高度 (160*9/16=90) */
    overflow: hidden;
    background: getCssVar('fill-color', 'lighter');
    border-bottom: 1px solid getCssVar('border-color', 'lighter');
  }

  .file-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover; /* 保持16:9比例，裁剪多余部分 */
    object-position: center; /* 居中显示 */
    display: block;
    position: relative; /* 确保图片在正确的层级 */
    z-index: 1; /* 确保图片在背景之上 */
  }

  .file-overlay {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 24px;
    height: 24px;
    background: rgba(64, 158, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    transition: opacity 0.2s ease;
    z-index: 3; /* 确保在分辨率标签之上 */
  }

  .check-icon {
    font-size: 14px;
    color: white;
  }

  /* 播放列表中的文件操作按钮 */
  .file-actions {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 4; /* 确保在分辨率标签和选中图标之上 */
  }

  .file-item:hover .file-actions {
    opacity: 1;
  }

  .file-name {
    padding: 3px 6px;
    font-size: 10px;
    color: #606266;
    text-align: center;
    word-break: break-all;
    line-height: 1.2;
    background: getCssVar('fill-color', 'light');
    height: 28px; /* 调整文件名区域高度 */
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  }

  .playlist-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
    height: 56px;
    box-sizing: border-box;
  }

  .playlist-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }

  .playlist-actions {
    display: flex;
    gap: 8px;
  }

  .playlist-filters {
    padding: 16px;
    border-bottom: 1px solid getCssVar('border-color', 'lighter');
    background: getCssVar('bg-color');
    transition: all 0.3s ease;
  }

  .filter-row {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
  }

  .filter-row:last-child {
    margin-bottom: 0;
  }

  .filter-label {
    font-size: 14px;
    color: #606266;
    min-width: 80px;
  }

  .playlist-table-container {
    flex: 1;
    overflow: hidden;
    height: 0;
    width: 100%;
  }

  .playlist-table {
    height: 100%;
  }

  .playlist-table .el-table__body-wrapper {
    overflow-y: auto;
  }

  .playlist-files .files-grid {
    height: auto;
    min-height: 0;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    justify-content: start;
    width: 100%;
    max-width: 100%; /* 防止超出容器 */
    gap: 12px;
    padding: 16px;
    overflow-x: hidden; /* 防止横向滚动 */
    box-sizing: border-box; /* 包含padding在内的宽度计算 */
  }

  .empty-playlist {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
  }

  /* 底部操作按钮 */
  .footer-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    padding: 20px;
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin-top: 20px;
  }

  /* 拖拽样式 */
  .sortable-ghost {
    opacity: 0.5;
  }

  .sortable-chosen {
    transform: scale(1.05);
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .top-section {
      grid-template-columns: 1fr;
      height: auto;
    }

    .bottom-section {
      grid-template-columns: 1fr;
      height: auto;
    }

    .card-header {
      padding: 16px;
    }

    .header-icon {
      width: 36px;
      height: 36px;
    }

    .header-right-content {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }

    .header-actions {
      flex-direction: column;
      gap: 8px;
    }

    .upload-section {
      margin-bottom: 20px;
      height: 200px;
    }

    .files-section {
      height: 300px;
    }
  }

  /* 大屏幕响应式布局 */
  @media (min-width: 1200px) {
    .top-section {
      flex-direction: row;
      height: 260px;
    }

    .upload-section {
      width: 280px;
      flex-shrink: 0;
    }

    .files-section {
      flex: 1;
    }

    .bottom-section {
      flex-direction: row;
      gap: 0;
      background: var(--el-bg-color);
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      overflow: hidden;
    }

    .playlist-management {
      width: 350px;
      flex-shrink: 0;
      background: transparent;
      border-radius: 0;
      box-shadow: none;
    }

    .playlist-content {
      flex: 1;
    }
  }

  /* 中等屏幕响应式布局 */
  @media (min-width: 768px) and (max-width: 1199px) {
    .top-section {
      flex-direction: row;
      height: auto;
      min-height: 200px;
    }

    .upload-section {
      width: 250px;
      flex-shrink: 0;
    }

    .files-section {
      flex: 1;
    }

    .bottom-section {
      flex-direction: column;
      gap: 16px;
    }

    .playlist-management,
    .playlist-content {
      width: 100%;
      background: var(--el-bg-color);
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    }
  }

  @media (max-width: 768px) {
    .play-theme-config {
      padding: 12px;
    }

    .top-section {
      flex-direction: column;
      gap: 12px;
    }

    .upload-section {
      width: 100%;
      height: auto;
      min-height: 150px;
    }

    .files-section {
      width: 100%;
    }

    .bottom-section {
      flex-direction: column;
      gap: 12px;
    }

    .playlist-management,
    .playlist-content {
      width: 100%;
      background: var(--el-bg-color);
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      margin-bottom: 12px;
    }

    .card-header {
      flex-direction: column;
      gap: 12px;
      padding: 16px;
    }

    .header-left {
      justify-content: center;
    }

    .header-title {
      max-width: none;
      text-align: center;
    }

    .header-center {
      justify-content: center;
      flex-wrap: wrap;
      gap: 16px;
    }

    .header-actions {
      justify-content: center;
    }

    .section-header {
      flex-direction: column;
      gap: 12px;
      text-align: center;
    }

    .header-actions {
      justify-content: center;
    }

    .files-header {
      flex-direction: column;
      gap: 12px;
      text-align: center;
    }

    .upload-area {
      padding: 30px 15px;
      min-height: 120px;
    }

    .files-grid {
      grid-template-columns: repeat(
        auto-fill,
        minmax(140px, 1fr)
      ); /* 移动端调整最小宽度 */
      gap: 10px;
    }

    .file-item {
      min-height: 140px;
    }

    .file-preview {
      height: 100px;
    }

    .footer-actions {
      flex-direction: column;
    }

    .playlist-filters .el-form {
      flex-direction: column;
    }

    .playlist-filters .el-form-item {
      margin-right: 0;
      margin-bottom: 12px;
    }

    /* 对话框响应式 */
    :deep(.el-dialog) {
      width: 95% !important;
      max-width: 500px;
      margin: 5vh auto;
    }

    :deep(.el-dialog__body) {
      padding: 15px;
    }

    /* 表格响应式 */
    :deep(.el-table) {
      font-size: 14px;
    }

    :deep(.el-table th),
    :deep(.el-table td) {
      padding: 8px 5px;
    }

    /* 按钮响应式 */
    .el-button {
      padding: 8px 12px;
      font-size: 14px;
    }

    .el-button--small {
      padding: 6px 10px;
      font-size: 12px;
    }
  }

  /* 超小屏幕响应式 (手机) */
  @media (max-width: 480px) {
    .play-theme-config {
      padding: 8px;
    }

    .top-section,
    .bottom-section {
      gap: 8px;
    }

    .upload-area {
      padding: 20px 10px;
      min-height: 100px;
    }

    .files-grid {
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 8px;
    }

    .file-item {
      min-height: 120px;
    }

    .file-preview {
      height: 80px;
    }

    .card-header {
      padding: 12px;
    }

    .header-title {
      font-size: 16px;
    }

    :deep(.el-dialog) {
      width: 98% !important;
      margin: 2vh auto;
    }

    :deep(.el-table th),
    :deep(.el-table td) {
      padding: 6px 3px;
      font-size: 12px;
    }

    .el-button {
      padding: 6px 8px;
      font-size: 12px;
    }

    .el-button--small {
      padding: 4px 6px;
      font-size: 11px;
    }
  }

  /* 表格样式优化 */
  :deep(.el-table__row) {
    cursor: pointer;
  }

  :deep(.el-table__row:hover) {
    background-color: var(--el-table-row-hover-bg-color);
  }

  :deep(.el-table .current-row) {
    background-color: var(--el-color-primary-light-9);
  }

  /* 按钮组样式 */
  .header-actions .el-button {
    margin-left: 0;
    margin-right: 8px;
  }

  .header-actions .el-button:last-child {
    margin-right: 0;
  }

  /* 图片裁剪对话框样式 - 专业设计 */
  .crop-dialog {
    --el-dialog-bg-color: getCssVar('bg-color');
    border-radius: 16px;
    overflow: hidden;
    box-shadow: getCssVar('box-shadow');
  }

  /* 禁用对话框动画 */
  .crop-dialog.no-animation .el-dialog {
    animation: none !important;
    transition: none !important;
  }

  .crop-dialog.no-animation .el-dialog__wrapper {
    animation: none !important;
    transition: none !important;
  }

  .crop-dialog.no-animation .el-overlay {
    animation: none !important;
    transition: none !important;
  }

  .crop-dialog .el-dialog__header {
    padding: 20px 24px;
    border-bottom: 1px solid getCssVar('border-color', 'lighter');
    background: getCssVar('fill-color', 'light');
    transition: all 0.3s ease;
  }

  .crop-dialog .el-dialog__body {
    padding: 0;
    background: getCssVar('bg-color');
    transition: all 0.3s ease;
  }

  .crop-editor {
    display: flex;
    height: 70vh;
    min-height: 400px;
    background: getCssVar('bg-color');
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: getCssVar('box-shadow', 'light');
    transition: all 0.3s ease;
  }

  .crop-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: getCssVar('fill-color', 'lighter');
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .crop-main::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(
        circle at 20% 20%,
        rgba(64, 158, 255, 0.02) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 80% 80%,
        rgba(64, 158, 255, 0.02) 0%,
        transparent 50%
      );
    pointer-events: none;
    z-index: 0;
  }

  .crop-canvas-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background:
      linear-gradient(45deg, #f1f5f9 25%, transparent 25%),
      linear-gradient(-45deg, #f1f5f9 25%, transparent 25%),
      linear-gradient(45deg, transparent 75%, #f1f5f9 75%),
      linear-gradient(-45deg, transparent 75%, #f1f5f9 75%);
    background-size: 16px 16px;
    background-position:
      0 0,
      0 8px,
      8px -8px,
      -8px 0px;
    overflow: hidden;
    padding: 32px;
    z-index: 1;
    box-shadow:
      inset 0 2px 8px rgba(0, 0, 0, 0.06),
      0 4px 16px rgba(0, 0, 0, 0.04);
  }

  .crop-canvas-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    z-index: -1;
  }

  .crop-canvas-container canvas {
    display: block;
    cursor: move;
    border-radius: 12px;
    box-shadow:
      0 10px 40px rgba(0, 0, 0, 0.15),
      0 4px 16px rgba(0, 0, 0, 0.1),
      0 2px 8px rgba(0, 0, 0, 0.06);
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    transition: all 0.3s ease;
    z-index: 2;
    border: 2px solid rgba(255, 255, 255, 0.8);
  }

  .crop-canvas-container canvas:hover {
    box-shadow:
      0 15px 50px rgba(0, 0, 0, 0.2),
      0 8px 24px rgba(0, 0, 0, 0.15),
      0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .zoom-value {
    font-size: 14px;
    color: #606266;
    min-width: 40px;
    text-align: center;
  }

  .crop-sidebar {
    width: 300px;
    background: getCssVar('fill-color', 'light');
    border-left: 1px solid getCssVar('border-color', 'lighter');
    padding: 20px;
    overflow-y: auto;
    box-shadow: getCssVar('box-shadow', 'lighter');
    display: flex;
    flex-direction: column;
    height: 100%;
    transition: all 0.3s ease;
  }

  .crop-preview-section,
  .controls-section {
    background: getCssVar('bg-color', 'overlay');
    border-radius: 12px;
    padding: 14px;
    box-shadow: getCssVar('box-shadow', 'lighter');
    border: 1px solid getCssVar('border-color', 'lighter');
    margin-bottom: 12px;
    flex-shrink: 0;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
  }

  .crop-preview-section h4,
  .controls-section h4 {
    margin: 0 0 10px 0;
    color: getCssVar('text-color', 'primary');
    font-size: 16px;
    font-weight: 700;
    border-bottom: 1px solid getCssVar('border-color', 'lighter');
    padding-bottom: 8px;
    letter-spacing: -0.025em;
    transition: all 0.3s ease;
  }

  .control-group {
    margin-bottom: 10px;
  }

  .control-group:last-child {
    margin-bottom: 0;
  }

  .control-group label {
    display: block;
    margin-bottom: 8px;
    color: getCssVar('text-color', 'regular');
    font-size: 14px;
    font-weight: 600;
    letter-spacing: -0.025em;
    transition: all 0.3s ease;
  }

  .button-group {
    display: flex;
    gap: 12px;
  }

  .button-group .el-button {
    flex: 1;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
    font-size: 12px;
    padding: 6px 12px;
  }

  .button-group .el-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
  }

  .crop-tip {
    font-size: 11px;
    color: getCssVar('text-color', 'secondary');
    text-align: center;
    margin-top: 8px;
    padding: 8px;
    background: getCssVar('fill-color', 'light');
    border-radius: 6px;
    line-height: 1.4;
    transition: all 0.3s ease;
  }

  .preview-wrapper {
    position: relative;
    background: getCssVar('fill-color', 'lighter');
    border: 1px solid getCssVar('border-color', 'lighter');
    border-radius: 16px;
    overflow: hidden;
    box-shadow: getCssVar('box-shadow', 'lighter');
    transition: all 0.2s ease;
    max-height: 100px;
  }

  .preview-wrapper:hover {
    box-shadow: getCssVar('box-shadow', 'light');
    transform: translateY(-2px);
  }

  .preview-canvas {
    width: 100%;
    height: auto;
    max-height: 100px;
    display: block;
    border-radius: 16px;
    object-fit: contain;
  }

  .preview-label {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.7);
    color: #fff;
    text-align: center;
    padding: 6px;
    font-size: 12px;
  }

  .info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 13px;
  }

  .info-label {
    color: #606266;
  }

  .info-value {
    color: #303133;
    font-weight: 500;
  }

  .action-buttons {
    display: flex;
    gap: 12px;
    margin-top: 12px;
  }

  .action-buttons .el-button {
    border-radius: 8px;
    font-weight: 500;
    padding: 8px 16px;
    font-size: 14px;
    transition: all 0.2s ease;
    letter-spacing: -0.025em;
    min-height: 36px;
  }

  .action-buttons .el-button:hover {
    transform: translateY(-1px);
    box-shadow: getCssVar('box-shadow', 'light');
  }

  .action-buttons .el-button[type='primary'] {
    background: getCssVar('color-primary');
    border: none;
  }

  .action-buttons .el-button[type='primary']:hover {
    background: getCssVar('color-primary-light-3');
  }

  /* 裁剪框调整控制点 - 专业设计 */
  .crop-resize-box {
    position: absolute;
    pointer-events: none;
    z-index: 100;
    /* 为子元素提供定位上下文 */
    transform: translateZ(0);
  }

  .resize-handle {
    position: absolute;
    pointer-events: all;
    background: getCssVar('bg-color');
    border: 2px solid getCssVar('color-primary');
    border-radius: 3px; /* 方形设计更专业 */
    width: 12px;
    height: 12px;
    z-index: 101;
    box-shadow: getCssVar('box-shadow', 'light');
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .resize-handle:hover {
    background: getCssVar('color-primary');
    border-color: getCssVar('color-primary-light-3');
    transform: scale(1.2);
    box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.3);
  }

  .resize-handle:active {
    transform: scale(1.1);
    box-shadow:
      0 2px 6px rgba(64, 158, 255, 0.4),
      0 1px 3px rgba(0, 0, 0, 0.2);
  }

  /* 角控制点 - 精确定位 */
  .corner-tl {
    top: -6px;
    left: -6px;
    cursor: nw-resize;
  }

  .corner-tr {
    top: -6px;
    right: -6px;
    cursor: ne-resize;
  }

  .corner-bl {
    bottom: -6px;
    left: -6px;
    cursor: sw-resize;
  }

  .corner-br {
    bottom: -6px;
    right: -6px;
    cursor: se-resize;
  }

  /* 边控制点 - 精确居中 */
  .edge-t {
    top: -6px;
    left: 50%;
    transform: translateX(-50%);
    cursor: n-resize;
  }

  .edge-r {
    right: -6px;
    top: 50%;
    transform: translateY(-50%);
    cursor: e-resize;
  }

  .edge-b {
    bottom: -6px;
    left: 50%;
    transform: translateX(-50%);
    cursor: s-resize;
  }

  .edge-l {
    left: -6px;
    top: 50%;
    transform: translateY(-50%);
    cursor: w-resize;
  }

  /* 工具栏样式 */
  .crop-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  .crop-tip {
    font-size: 13px;
    color: getCssVar('text-color', 'secondary');
    text-align: center;
    transition: all 0.3s ease;
  }

  /* 图片预览对话框样式 */
  .image-preview-dialog {
    border-radius: 16px;
    overflow: hidden;

    .el-dialog__body {
      padding: 10px;
      background: getCssVar('bg-color');
    }

    .el-dialog__header {
      background: getCssVar('bg-color');
      border-bottom: 1px solid getCssVar('border-color', 'lighter');
    }
  }

  .image-preview-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    padding: 5px;
  }

  /* 自动播放按钮 - 右上角 */
  .auto-play-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  .image-display {
    max-height: 50vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background: getCssVar('fill-color', 'lighter');
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid getCssVar('border-color', 'lighter');
    box-shadow: getCssVar('box-shadow', 'lighter');
    transition: all 0.3s ease;
  }

  .preview-image {
    max-width: 100%;
    max-height: 50vh;
    object-fit: contain;
    border-radius: 8px;
  }

  .image-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    max-width: 300px;
    padding: 12px 20px;
    background: getCssVar('bg-color', 'overlay');
    border-radius: 50px;
    box-shadow: getCssVar('box-shadow', 'light');
    border: 1px solid getCssVar('border-color', 'lighter');
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
  }

  .nav-btn {
    width: 48px;
    height: 48px;
    background: getCssVar('color-primary');
    border: none;
    color: white;
    font-size: 18px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.3);
  }

  .nav-btn:hover {
    transform: translateY(-2px);
    background: getCssVar('color-primary-light-3');
    box-shadow: 0 6px 20px rgba(var(--el-color-primary-rgb), 0.4);
  }

  .nav-btn:disabled {
    background: getCssVar('fill-color', 'darker');
    color: getCssVar('text-color', 'disabled');
    transform: none;
    box-shadow: none;
  }

  .image-counter {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    background: getCssVar('color-primary');
    color: white;
    padding: 6px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 13px;
    box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.3);
    min-width: 70px;
    transition: all 0.3s ease;
  }

  .current-index {
    font-size: 16px;
    font-weight: 700;
  }

  .separator {
    opacity: 0.8;
    margin: 0 2px;
  }

  .total-count {
    opacity: 0.9;
    font-size: 13px;
  }

  .empty-preview {
    padding: 40px;
    text-align: center;
  }
</style>
