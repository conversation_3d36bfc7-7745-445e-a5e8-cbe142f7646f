<template>
  <el-dialog
    v-model="visible"
    title="编辑播放主题"
    width="400px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="主题名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入主题名称"
          maxlength="100"
          clearable
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, watch } from 'vue';
  import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
  import * as TaiSendApi from '@/api/info/tai/taisend';

  interface Props {
    modelValue: boolean;
    playlist?: any;
  }

  interface Emits {
    (e: 'update:modelValue', value: boolean): void;
    (e: 'success', playlist: any): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  // 响应式数据
  const visible = ref(false);
  const submitLoading = ref(false);
  const formRef = ref<FormInstance>();

  // 表单数据
  const formData = reactive({
    name: ''
  });

  // 表单验证规则
  const formRules: FormRules = {
    name: [
      { required: true, message: '请输入主题名称', trigger: 'blur' },
      {
        min: 2,
        max: 100,
        message: '主题名称长度在 2 到 100 个字符',
        trigger: 'blur'
      }
    ]
  };

  // 监听props变化
  watch(
    () => props.modelValue,
    (val) => {
      visible.value = val;
      if (val && props.playlist) {
        initForm();
      }
    },
    { immediate: true }
  );

  // 监听visible变化
  watch(visible, (val) => {
    emit('update:modelValue', val);
  });

  // 初始化表单
  const initForm = () => {
    if (props.playlist) {
      formData.name = props.playlist.name || '';
    }
    formRef.value?.clearValidate();
  };

  // 关闭对话框
  const handleClose = () => {
    visible.value = false;
  };

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value || !props.playlist) return;

    try {
      const valid = await formRef.value.validate();
      if (!valid) return;

      submitLoading.value = true;

      const submitData = {
        ...props.playlist,
        name: formData.name
      };

      const result = await TaiSendApi.updateTaiplaylistInfo(submitData);

      ElMessage.success('编辑播放主题成功');
      emit('success', result);
      handleClose();
    } catch (error) {
      console.error('编辑播放主题失败:', error);
    } finally {
      submitLoading.value = false;
    }
  };
</script>

<style lang="scss" scoped>
  @use 'element-plus/theme-chalk/src/mixins/function.scss' as *;

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: getCssVar('text-color', 'regular');
  }

  :deep(.el-dialog__header) {
    background: getCssVar('bg-color');
    border-bottom: 1px solid getCssVar('border-color', 'lighter');
  }

  :deep(.el-dialog__body) {
    background: getCssVar('bg-color');
  }

  :deep(.el-dialog__footer) {
    background: getCssVar('bg-color');
    border-top: 1px solid getCssVar('border-color', 'lighter');
  }

  :deep(.el-input) {
    --el-input-bg-color: getCssVar('fill-color', 'lighter');
    --el-input-border-color: getCssVar('border-color', 'lighter');
  }

  :deep(.el-input:hover) {
    --el-input-border-color: getCssVar('color-primary');
  }

  :deep(.el-input.is-focus) {
    --el-input-border-color: getCssVar('color-primary');
  }
</style>
