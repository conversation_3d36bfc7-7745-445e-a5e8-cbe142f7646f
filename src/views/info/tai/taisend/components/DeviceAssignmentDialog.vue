<template>
  <ele-modal
    :form="true"
    :width="920"
    title="设备分配"
    :append-to-body="true"
    position="center"
    v-model="visible"
    @closed="handleClose"
  >
    <!-- 设备搜索区域 -->
    <div class="search-section">
      <el-form :inline="true" @submit.prevent="">
        <el-form-item label="设备名称">
          <el-input
            v-model="searchForm.deviceName"
            placeholder="请输入设备名称"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-form-item label="图像变化间隔：" style="margin-left: 20px">
            <div class="interval-input-group">
              <el-input
                v-model="playConfig.intervalValue"
                type="number"
                :min="1"
                :max="999"
                placeholder="1"
                style="width: 100px"
              />
              <el-select
                v-model="playConfig.intervalUnit"
                style="width: 80px; margin-left: 8px"
              >
                <el-option label="秒" value="second" />
                <el-option label="分" value="minute" />
                <el-option label="时" value="hour" />
                <el-option label="天" value="day" />
              </el-select>
            </div>
          </el-form-item>
        </el-form-item>
      </el-form>
    </div>

    <!-- 设备列表 -->
    <div class="device-list">
      <el-table
        ref="tableRef"
        :data="deviceData"
        @current-change="handleCurrentChange"
        highlight-current-row
        v-loading="loading"
      >
        <el-table-column
          prop="name"
          label="设备名称"
          min-width="200"
          align="center"
        />
        <el-table-column prop="externalUuid" label="设备UUID" min-width="250" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <div class="selection-info">
          {{
            selectedDevice
              ? `已选择设备：${selectedDevice.name} | 间隔：${intervalDisplayText}`
              : '请选择一个设备'
          }}
        </div>
        <div class="buttons">
          <el-button @click="handleClose">取消</el-button>
          <el-button
            type="primary"
            @click="handleConfirm"
            :disabled="!selectedDevice"
            :loading="assignLoading"
          >
            确定分配
          </el-button>
        </div>
      </div>
    </template>
  </ele-modal>
</template>

<script setup lang="ts">
  import { ref, reactive, watch, computed } from 'vue';
  import { ElMessage } from 'element-plus';
  import * as TaiSendApi from '@/api/info/tai/taisend';

  interface Props {
    modelValue: boolean;
    playlist?: any;
    files?: any[];
  }

  interface Emits {
    (e: 'update:modelValue', value: boolean): void;
    (
      e: 'confirm',
      data: { playlist: any; files: any[]; devices: any[]; config?: any }
    ): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  // 响应式数据
  const visible = ref(false);
  const loading = ref(false);
  const assignLoading = ref(false);
  const deviceData = ref<any[]>([]);
  const selectedDevice = ref<any>(null);

  // 播放配置
  const playConfig = reactive({
    intervalValue: 1, // 间隔数值
    intervalUnit: 'minute' // 间隔单位：second, minute, hour, day
  });

  // 搜索表单
  const searchForm = reactive({
    deviceName: '',
    status: undefined
  });

  // 分页数据
  const pagination = reactive({
    page: 1,
    size: 10,
    total: 0
  });

  // 计算属性
  const playlist = computed(() => props.playlist);
  const files = computed(() => props.files || []);

  // 计算总间隔秒数
  const totalIntervalSeconds = computed(() => {
    const value = Number(playConfig.intervalValue) || 1;
    const unitMultipliers = {
      second: 1,
      minute: 60,
      hour: 3600,
      day: 86400
    };
    return value * (unitMultipliers[playConfig.intervalUnit] || 1);
  });

  // 获取间隔显示文本
  const intervalDisplayText = computed(() => {
    const unitLabels = {
      second: '秒',
      minute: '分钟',
      hour: '小时',
      day: '天'
    };
    return `${playConfig.intervalValue}${unitLabels[playConfig.intervalUnit] || '秒'}`;
  });

  // 获取播放类型文本
  const getPlayTypeText = (type: number) => {
    const typeMap = {
      0: '广告',
      1: '宣传'
    };
    return typeMap[type] || '未知';
  };

  // 监听props变化
  watch(
    () => props.modelValue,
    (val) => {
      visible.value = val;
      if (val) {
        loadDeviceData();
      }
    },
    { immediate: true }
  );

  // 监听visible变化
  watch(visible, (val) => {
    emit('update:modelValue', val);
  });

  // 加载设备数据
  const loadDeviceData = async () => {
    loading.value = true;
    try {
      const params = {
        pageNo: pagination.page,
        pageSize: pagination.size,
        name: searchForm.deviceName || undefined, // 使用name字段搜索设备名称
        status: searchForm.status
      };

      const result = await TaiSendApi.getInformationDeviceList(params);
      deviceData.value = result.list || [];
      pagination.total = result.total || 0;
    } catch (error) {
      console.error('加载设备列表失败:', error);
      ElMessage.error('加载设备列表失败');
    } finally {
      loading.value = false;
    }
  };

  // 搜索
  const handleSearch = () => {
    pagination.page = 1;
    loadDeviceData();
  };

  // 重置
  const handleReset = () => {
    searchForm.deviceName = '';
    searchForm.status = undefined;
    pagination.page = 1;
    loadDeviceData();
  };

  // 设备行选择变化（单选）
  const handleCurrentChange = (currentRow: any) => {
    selectedDevice.value = currentRow;
  };

  // 分页大小改变
  const handleSizeChange = (size: number) => {
    pagination.size = size;
    pagination.page = 1;
    loadDeviceData();
  };

  // 页码改变
  const handlePageChange = (page: number) => {
    pagination.page = page;
    loadDeviceData();
  };

  // 关闭对话框
  const handleClose = () => {
    visible.value = false;
    selectedDevice.value = null;
  };

  // 确认分配
  const handleConfirm = async () => {
    if (!selectedDevice.value) {
      ElMessage.warning('请选择要分配的设备');
      return;
    }

    // 开始加载
    assignLoading.value = true;

    try {
      await TaiSendApi.assignDevice({
        deviceId: selectedDevice.value.externalUuid,
        playlistId: props.playlist?.externalUuid, // 播放主题的ID
        interval: totalIntervalSeconds.value // 传递秒数
      });

      ElMessage.success(
        `成功分配到设备：${selectedDevice.value.name}，间隔：${intervalDisplayText.value}`
      );
      emit('confirm', {
        playlist: props.playlist,
        files: props.files || [],
        devices: [selectedDevice.value], // 保持数组格式以兼容现有接口
        config: {
          intervalValue: playConfig.intervalValue,
          intervalUnit: playConfig.intervalUnit,
          intervalSeconds: totalIntervalSeconds.value // 提供秒数用于后端处理
        }
      });
      handleClose();
    } catch (error) {
      console.error('设备分配失败:', error);
      ElMessage.error('设备分配失败');
    } finally {
      // 结束加载
      assignLoading.value = false;
    }
  };
</script>

<style scoped>
  .playlist-info {
    margin-bottom: 20px;
    padding: 16px;
    background: var(--el-fill-color-lighter);
    border-radius: 6px;
    border-left: 4px solid var(--el-color-primary);
  }

  .playlist-info h4 {
    margin: 0 0 12px 0;
    color: var(--el-text-color-primary);
  }

  .info-content {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }

  .info-item {
    display: flex;
    align-items: center;
  }

  .info-item .label {
    font-weight: 500;
    color: var(--el-text-color-regular);
    margin-right: 8px;
  }

  .info-item .value {
    color: var(--el-text-color-primary);
  }

  .search-section {
    margin-bottom: 20px;
    border-radius: 6px;
  }

  .interval-input-group {
    display: flex;
    align-items: center;
  }

  .device-list {
    margin-bottom: 20px;
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .dialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .selection-info {
    color: var(--el-text-color-regular);
    font-size: 14px;
  }

  .buttons {
    display: flex;
    gap: 12px;
  }

  /* 设备表格选中行样式 */
  :deep(.el-table__body tr.current-row) {
    background-color: #409eff !important;
    color: white !important;
  }

  :deep(.el-table__body tr.current-row td) {
    background-color: #409eff !important;
    color: white !important;
  }

  :deep(.el-table__body tr.current-row:hover td) {
    background-color: #337ecc !important;
    color: white !important;
  }
</style>
