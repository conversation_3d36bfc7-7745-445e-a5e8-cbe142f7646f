<template>
  <el-dialog
    v-model="visible"
    title="选择播放主题"
    width="800px"
    :before-close="handleClose"
  >
    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :inline="true" @submit.prevent="">
        <el-form-item label="主题名称">
          <el-input
            v-model="searchForm.playName"
            placeholder="请输入播放主题名称"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="播放类型">
          <el-select v-model="searchForm.playType" placeholder="请选择播放类型" clearable>
            <el-option label="广告" :value="0" />
            <el-option label="宣传" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 播放主题列表 -->
    <div class="playlist-list">
      <el-table
        ref="tableRef"
        :data="playlistData"
        highlight-current-row
        @current-change="handleCurrentChange"
        v-loading="loading"
      >
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="playCode" label="播放编码" min-width="120" />
        <el-table-column prop="playName" label="播放名称" min-width="150" />
        <el-table-column prop="playType" label="播放类型" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.playType === 0 ? 'warning' : 'success'">
              {{ row.playType === 0 ? '广告' : '宣传' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :disabled="!selectedPlaylist">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, watch, onMounted } from 'vue';
  import { ElMessage } from 'element-plus';
  import * as PlayMainApi from '@/api/info/playmain';

  interface Props {
    modelValue: boolean;
  }

  interface Emits {
    (e: 'update:modelValue', value: boolean): void;
    (e: 'confirm', playlist: any): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  // 响应式数据
  const visible = ref(false);
  const loading = ref(false);
  const playlistData = ref<any[]>([]);
  const selectedPlaylist = ref<any>(null);

  // 搜索表单
  const searchForm = reactive({
    playName: '',
    playType: undefined
  });

  // 分页数据
  const pagination = reactive({
    page: 1,
    size: 10,
    total: 0
  });

  // 监听props变化
  watch(
    () => props.modelValue,
    (val) => {
      visible.value = val;
      if (val) {
        loadPlaylistData();
      }
    },
    { immediate: true }
  );

  // 监听visible变化
  watch(visible, (val) => {
    emit('update:modelValue', val);
  });

  // 加载播放主题数据
  const loadPlaylistData = async () => {
    loading.value = true;
    try {
      const params = {
        pageNo: pagination.page,
        pageSize: pagination.size,
        playName: searchForm.playName || undefined,
        playType: searchForm.playType,
        status: 1 // 只显示启用的播放主题
      };

      const result = await PlayMainApi.getPlayMainPage(params);
      playlistData.value = result.list || [];
      pagination.total = result.total || 0;
    } catch (error) {
      console.error('加载播放主题失败:', error);
      ElMessage.error('加载播放主题失败');
    } finally {
      loading.value = false;
    }
  };

  // 搜索
  const handleSearch = () => {
    pagination.page = 1;
    loadPlaylistData();
  };

  // 重置
  const handleReset = () => {
    searchForm.playName = '';
    searchForm.playType = undefined;
    pagination.page = 1;
    loadPlaylistData();
  };

  // 表格行选择
  const handleCurrentChange = (row: any) => {
    selectedPlaylist.value = row;
  };

  // 分页大小改变
  const handleSizeChange = (size: number) => {
    pagination.size = size;
    pagination.page = 1;
    loadPlaylistData();
  };

  // 页码改变
  const handlePageChange = (page: number) => {
    pagination.page = page;
    loadPlaylistData();
  };

  // 关闭对话框
  const handleClose = () => {
    visible.value = false;
    selectedPlaylist.value = null;
  };

  // 确认选择
  const handleConfirm = () => {
    if (!selectedPlaylist.value) {
      ElMessage.warning('请选择一个播放主题');
      return;
    }
    emit('confirm', selectedPlaylist.value);
  };
</script>

<style scoped>
  .search-section {
    margin-bottom: 20px;
    padding: 16px;
    background: var(--el-fill-color-lighter);
    border-radius: 6px;
  }

  .playlist-list {
    margin-bottom: 20px;
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  :deep(.el-table__row) {
    cursor: pointer;
  }

  :deep(.el-table__row:hover) {
    background-color: var(--el-table-row-hover-bg-color);
  }
</style>
