<template>
  <el-dialog
    v-model="visible"
    title="文件预览"
    width="800px"
    :before-close="handleClose"
  >
    <div class="file-preview-content" v-if="file">
      <div class="preview-area">
        <img
          :src="file.url"
          :alt="file.name"
          class="preview-image"
          @load="handleImageLoad"
          @error="handleImageError"
        />
      </div>
    </div>

    <div v-else class="empty-preview">
      <el-empty description="无预览内容" />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue';
  import { ElMessage } from 'element-plus';

  interface Props {
    modelValue: boolean;
    file?: any;
  }

  interface Emits {
    (e: 'update:modelValue', value: boolean): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  // 响应式数据
  const visible = ref(false);
  const imageLoading = ref(false);

  // 计算属性
  const file = computed(() => props.file);

  // 监听props变化
  watch(
    () => props.modelValue,
    (val) => {
      visible.value = val;
    },
    { immediate: true }
  );

  // 监听visible变化
  watch(visible, (val) => {
    emit('update:modelValue', val);
  });

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 图片加载完成
  const handleImageLoad = () => {
    imageLoading.value = false;
  };

  // 图片加载错误
  const handleImageError = () => {
    imageLoading.value = false;
    ElMessage.error('图片加载失败');
  };

  // 关闭对话框
  const handleClose = () => {
    visible.value = false;
  };
</script>

<style scoped>
  .file-preview-content {
    text-align: center;
  }

  .file-info {
    margin-bottom: 20px;
    padding: 16px;
    background: var(--el-fill-color-lighter);
    border-radius: 6px;
  }

  .file-info h4 {
    margin: 0 0 8px 0;
    color: var(--el-text-color-primary);
    font-size: 16px;
  }

  .file-details {
    display: flex;
    justify-content: center;
    gap: 20px;
    font-size: 14px;
    color: var(--el-text-color-regular);
  }

  .preview-area {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    background: var(--el-fill-color-lighter);
    border-radius: 6px;
    padding: 10px;
  }

  .preview-image {
    max-width: 100%;
    max-height: 500px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .empty-preview {
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  @media (max-width: 768px) {
    .file-details {
      flex-direction: column;
      gap: 8px;
    }

    .preview-area {
      min-height: 200px;
      padding: 10px;
    }
  }
</style>
