<template>
  <el-dialog
    v-model="visible"
    title="报修单详情"
    width="800px"
    :before-close="handleClose"
  >
    <div class="detail-container">
      <!-- 基本信息 -->
      <div class="detail-section">
        <div class="section-title">
          <el-icon><Document /></el-icon>
          <span>基本信息</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <label>工单编号：</label>
            <span>{{ repairData.repairNo }}</span>
          </div>
          <div class="info-item">
            <label>设备名称：</label>
            <span>{{ repairData.deviceName }}</span>
          </div>
          <div class="info-item">
            <label>优先级：</label>
            <el-tag :type="getPriorityType(repairData.priority)" size="small">
              {{ getPriorityText(repairData.priority) }}
            </el-tag>
          </div>
          <div class="info-item">
            <label>当前状态：</label>
            <el-tag :type="getStatusType(repairData.status)" size="small">
              {{ getStatusText(repairData.status) }}
            </el-tag>
          </div>
          <div class="info-item">
            <label>报修人：</label>
            <span>{{ repairData.reportUser }}</span>
          </div>
          <div class="info-item">
            <label>处理人：</label>
            <span>{{ repairData.assignedUser || '未分配' }}</span>
          </div>
          <div class="info-item">
            <label>报修时间：</label>
            <span>{{ repairData.reportTime }}</span>
          </div>
          <div class="info-item">
            <label>设备分类：</label>
            <span>{{ getCategoryText(repairData.category) }}</span>
          </div>
        </div>
      </div>

      <!-- 故障描述 -->
      <div class="detail-section">
        <div class="section-title">
          <el-icon><Warning /></el-icon>
          <span>故障描述</span>
        </div>
        <div class="fault-description">
          {{ repairData.faultDescription }}
        </div>
      </div>

      <!-- 处理记录 -->
      <div class="detail-section">
        <div class="section-title">
          <el-icon><Clock /></el-icon>
          <span>处理记录</span>
        </div>
        <div class="timeline-container">
          <el-timeline>
            <el-timeline-item
              v-for="record in processRecords"
              :key="record.id"
              :timestamp="record.time"
              :type="record.type"
            >
              <div class="timeline-content">
                <div class="timeline-title">{{ record.title }}</div>
                <div class="timeline-desc">{{ record.description }}</div>
                <div class="timeline-user">操作人：{{ record.operator }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>

      <!-- 附件信息 -->
      <div class="detail-section" v-if="attachments.length > 0">
        <div class="section-title">
          <el-icon><Paperclip /></el-icon>
          <span>相关附件</span>
        </div>
        <div class="attachments">
          <div
            v-for="file in attachments"
            :key="file.id"
            class="attachment-item"
          >
            <el-icon><Document /></el-icon>
            <span class="file-name">{{ file.name }}</span>
            <el-button type="primary" link size="small" @click="downloadFile(file)">
              下载
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="printDetail">
          <el-icon><Printer /></el-icon>
          打印
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Warning, Clock, Paperclip, Printer } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  repairData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = ref(false)

// 模拟处理记录数据
const processRecords = ref([
  {
    id: 1,
    title: '工单创建',
    description: '用户提交报修申请，工单已创建',
    operator: '张三',
    time: '2024-01-15 09:30:00',
    type: 'primary'
  },
  {
    id: 2,
    title: '工单分配',
    description: '工单已分配给李工程师处理',
    operator: '管理员',
    time: '2024-01-15 10:00:00',
    type: 'success'
  },
  {
    id: 3,
    title: '开始处理',
    description: '工程师已开始处理此报修单',
    operator: '李工程师',
    time: '2024-01-15 14:30:00',
    type: 'warning'
  }
])

// 模拟附件数据
const attachments = ref([
  {
    id: 1,
    name: '设备故障图片.jpg',
    size: '2.5MB',
    url: '/files/fault-image.jpg'
  },
  {
    id: 2,
    name: '设备规格说明.pdf',
    size: '1.2MB',
    url: '/files/device-spec.pdf'
  }
])

watch(() => props.modelValue, (val) => {
  visible.value = val
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

const handleClose = () => {
  visible.value = false
}

const downloadFile = (file) => {
  ElMessage.success(`开始下载：${file.name}`)
  // 这里实现文件下载逻辑
}

const printDetail = () => {
  ElMessage.success('打印功能开发中...')
  // 这里实现打印逻辑
}

// 工具方法
const getPriorityType = (priority) => {
  const types = {
    low: '',
    medium: 'warning',
    high: 'danger',
    urgent: 'danger'
  }
  return types[priority] || ''
}

const getPriorityText = (priority) => {
  const texts = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return texts[priority] || priority
}

const getStatusType = (status) => {
  const types = {
    pending: 'warning',
    processing: 'primary',
    completed: 'success',
    closed: 'info'
  }
  return types[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    closed: '已关闭'
  }
  return texts[status] || status
}

const getCategoryText = (category) => {
  const texts = {
    computer: '计算机设备',
    network: '网络设备',
    printer: '打印设备',
    other: '其他设备'
  }
  return texts[category] || category
}
</script>

<style scoped>
.detail-container {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e4e7ed;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.fault-description {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 6px;
  border-left: 4px solid #409eff;
  line-height: 1.6;
  color: #606266;
}

.timeline-container {
  padding-left: 8px;
}

.timeline-content {
  padding-bottom: 8px;
}

.timeline-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.timeline-desc {
  color: #606266;
  margin-bottom: 4px;
}

.timeline-user {
  font-size: 12px;
  color: #909399;
}

.attachments {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.file-name {
  flex: 1;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid #e4e7ed;
}

:deep(.el-timeline-item__timestamp) {
  color: #909399;
  font-size: 12px;
}

@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }
}
</style>
