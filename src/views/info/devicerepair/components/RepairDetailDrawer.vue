<template>
  <el-drawer
    v-model="visible"
    title="报修单详情"
    :size="600"
    :before-close="handleClose"
    direction="rtl"
  >
    <div class="drawer-content">
      <!-- 基本信息卡片 -->
      <div class="info-card">
        <div class="card-header">
          <div class="header-left">
            <el-icon class="header-icon"><Document /></el-icon>
            <span class="header-title">基本信息</span>
          </div>
          <div class="header-right">
            <el-tag :type="getStatusType(repairData.status)" size="large">
              {{ getStatusText(repairData.status) }}
            </el-tag>
          </div>
        </div>
        <div class="card-content">
          <div class="info-grid">
            <div class="info-item">
              <label>工单编号</label>
              <span class="value primary">{{ repairData.repairNo }}</span>
            </div>
            <div class="info-item">
              <label>设备名称</label>
              <span class="value">{{ repairData.deviceName }}</span>
            </div>
            <div class="info-item">
              <label>故障类型</label>
              <span class="value">{{ repairData.faultType }}</span>
            </div>
            <div class="info-item">
              <label>优先级</label>
              <el-tag :type="getPriorityType(repairData.priority)" size="small">
                {{ getPriorityText(repairData.priority) }}
              </el-tag>
            </div>
            <div class="info-item">
              <label>报修人</label>
              <span class="value">{{ repairData.reportUser }}</span>
            </div>
            <div class="info-item">
              <label>处理人</label>
              <span class="value">{{
                repairData.assignedUser || '未分配'
              }}</span>
            </div>
            <div class="info-item">
              <label>报修时间</label>
              <span class="value">{{ repairData.reportTime }}</span>
            </div>
            <div class="info-item">
              <label>预计完成</label>
              <span class="value">{{
                getEstimatedTime(repairData.priority)
              }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 故障描述卡片 -->
      <div class="info-card">
        <div class="card-header">
          <div class="header-left">
            <el-icon class="header-icon"><Warning /></el-icon>
            <span class="header-title">故障描述</span>
          </div>
        </div>
        <div class="card-content">
          <div class="fault-description">
            {{ repairData.faultDescription }}
          </div>
        </div>
      </div>

      <!-- 处理进度 -->
      <div class="info-card">
        <div class="card-header">
          <div class="header-left">
            <el-icon class="header-icon"><Clock /></el-icon>
            <span class="header-title">处理进度</span>
          </div>
        </div>
        <div class="card-content">
          <div class="progress-container">
            <el-steps
              :active="getProgressStep(repairData.status)"
              finish-status="success"
            >
              <el-step title="工单创建" description="用户提交报修申请" />
              <el-step title="分配处理" description="分配给相关工程师" />
              <el-step title="正在处理" description="工程师处理中" />
              <el-step title="处理完成" description="问题已解决" />
            </el-steps>
          </div>
        </div>
      </div>

      <!-- 处理记录 -->
      <div class="info-card">
        <div class="card-header">
          <div class="header-left">
            <el-icon class="header-icon"><List /></el-icon>
            <span class="header-title">处理记录</span>
          </div>
        </div>
        <div class="card-content">
          <div class="timeline-container">
            <el-timeline>
              <el-timeline-item
                v-for="record in processRecords"
                :key="record.id"
                :timestamp="record.time"
                :type="record.type"
                :icon="record.icon"
              >
                <div class="timeline-content">
                  <div class="timeline-title">{{ record.title }}</div>
                  <div class="timeline-desc">{{ record.description }}</div>
                  <div class="timeline-user">
                    <el-icon><User /></el-icon>
                    <span>{{ record.operator }}</span>
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </div>

      <!-- 相关附件 -->
      <div class="info-card" v-if="attachments.length > 0">
        <div class="card-header">
          <div class="header-left">
            <el-icon class="header-icon"><Paperclip /></el-icon>
            <span class="header-title">相关附件</span>
          </div>
        </div>
        <div class="card-content">
          <div class="attachments-grid">
            <div
              v-for="file in attachments"
              :key="file.id"
              class="attachment-item"
            >
              <div class="file-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="file-info">
                <div class="file-name">{{ file.name }}</div>
                <div class="file-size">{{ file.size }}</div>
              </div>
              <div class="file-actions">
                <el-button
                  type="primary"
                  link
                  size="small"
                  @click="downloadFile(file)"
                >
                  <el-icon><Download /></el-icon>
                  下载
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 抽屉底部操作栏 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="printDetail">
          <el-icon><Printer /></el-icon>
          打印详情
        </el-button>
        <el-button
          type="success"
          @click="editRepair"
          v-if="repairData.status !== 'completed'"
        >
          <el-icon><Edit /></el-icon>
          编辑工单
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, watch, computed } from 'vue';
  import { ElMessage } from 'element-plus';
  import {
    Document,
    Warning,
    Clock,
    List,
    User,
    Paperclip,
    Download,
    Printer,
    Edit
  } from '@element-plus/icons-vue';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false
    },
    repairData: {
      type: Object,
      default: () => ({})
    }
  });

  const emit = defineEmits(['update:modelValue', 'edit']);

  const visible = ref(false);

  // 模拟处理记录数据
  const processRecords = ref([
    {
      id: 1,
      title: '工单创建',
      description: '用户提交报修申请，工单已创建',
      operator: '张三',
      time: '2024-01-15 09:30:00',
      type: 'primary',
      icon: 'Plus'
    },
    {
      id: 2,
      title: '工单分配',
      description: '工单已分配给李工程师处理',
      operator: '系统管理员',
      time: '2024-01-15 10:00:00',
      type: 'success',
      icon: 'User'
    },
    {
      id: 3,
      title: '开始处理',
      description: '工程师已开始处理此报修单',
      operator: '李工程师',
      time: '2024-01-15 14:30:00',
      type: 'warning',
      icon: 'Tools'
    }
  ]);

  // 模拟附件数据
  const attachments = ref([
    {
      id: 1,
      name: '故障现象截图.jpg',
      size: '2.5MB',
      url: '/files/fault-screenshot.jpg'
    },
    {
      id: 2,
      name: '设备信息报告.pdf',
      size: '1.2MB',
      url: '/files/device-report.pdf'
    }
  ]);

  watch(
    () => props.modelValue,
    (val) => {
      visible.value = val;
    }
  );

  watch(visible, (val) => {
    emit('update:modelValue', val);
  });

  const handleClose = () => {
    visible.value = false;
  };

  const downloadFile = (file) => {
    ElMessage.success(`开始下载：${file.name}`);
  };

  const printDetail = () => {
    ElMessage.success('打印功能开发中...');
  };

  const editRepair = () => {
    emit('edit', props.repairData);
    handleClose();
  };

  // 工具方法
  const getPriorityType = (priority) => {
    const types = {
      low: '',
      medium: 'warning',
      high: 'danger',
      urgent: 'danger'
    };
    return types[priority] || '';
  };

  const getPriorityText = (priority) => {
    const texts = {
      low: '低',
      medium: '中',
      high: '高',
      urgent: '紧急'
    };
    return texts[priority] || priority;
  };

  const getStatusType = (status) => {
    const types = {
      pending: 'warning',
      processing: 'primary',
      completed: 'success',
      closed: 'info'
    };
    return types[status] || '';
  };

  const getStatusText = (status) => {
    const texts = {
      pending: '待处理',
      processing: '处理中',
      completed: '已完成',
      closed: '已关闭'
    };
    return texts[status] || status;
  };

  const getProgressStep = (status) => {
    const steps = {
      pending: 1,
      processing: 2,
      completed: 4,
      closed: 4
    };
    return steps[status] || 0;
  };

  const getEstimatedTime = (priority) => {
    const times = {
      urgent: '2小时内',
      high: '4小时内',
      medium: '1个工作日',
      low: '3个工作日'
    };
    return times[priority] || '待评估';
  };
</script>

<style scoped>
  .drawer-content {
    padding: 0;
    height: 100%;
    overflow-y: auto;
  }

  /* 信息卡片 */
  .info-card {
    background: white;
    border-radius: 8px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: #fafbfc;
    border-bottom: 1px solid #e4e7ed;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .header-icon {
    color: #409eff;
    font-size: 18px;
  }

  .header-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .card-content {
    padding: 20px;
  }

  /* 信息网格 */
  .info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .info-item label {
    font-size: 12px;
    color: #909399;
    font-weight: 500;
  }

  .info-item .value {
    font-size: 14px;
    color: #303133;
    font-weight: 500;
  }

  .info-item .value.primary {
    color: #409eff;
    font-weight: 600;
  }

  /* 故障描述 */
  .fault-description {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    border-left: 4px solid #409eff;
    line-height: 1.6;
    color: #606266;
    font-size: 14px;
  }

  /* 进度条容器 */
  .progress-container {
    padding: 16px 0;
  }

  /* 时间线 */
  .timeline-container {
    padding: 8px 0;
  }

  .timeline-content {
    padding-bottom: 8px;
  }

  .timeline-title {
    font-weight: 600;
    color: #303133;
    margin-bottom: 4px;
    font-size: 14px;
  }

  .timeline-desc {
    color: #606266;
    margin-bottom: 6px;
    font-size: 13px;
  }

  .timeline-user {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #909399;
  }

  /* 附件网格 */
  .attachments-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .attachment-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e4e7ed;
    transition: all 0.3s;
  }

  .attachment-item:hover {
    background: #ecf5ff;
    border-color: #b3d8ff;
  }

  .file-icon {
    width: 40px;
    height: 40px;
    background: #409eff;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
  }

  .file-info {
    flex: 1;
  }

  .file-name {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 2px;
  }

  .file-size {
    font-size: 12px;
    color: #909399;
  }

  /* 抽屉底部 */
  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 20px;
    border-top: 1px solid #e4e7ed;
    background: #fafbfc;
  }

  /* 响应式 */
  @media (max-width: 768px) {
    .info-grid {
      grid-template-columns: 1fr;
    }

    .drawer-footer {
      flex-direction: column;
    }

    .drawer-footer .el-button {
      width: 100%;
    }
  }

  /* 抽屉样式覆盖 */
  :deep(.el-drawer__header) {
    padding: 20px;
    border-bottom: 1px solid #e4e7ed;
    margin-bottom: 0;
  }

  :deep(.el-drawer__title) {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
  }

  :deep(.el-drawer__body) {
    padding: 16px;
    background: #f5f7fa;
  }

  :deep(.el-steps) {
    padding: 0 16px;
  }

  :deep(.el-timeline-item__timestamp) {
    color: #909399;
    font-size: 12px;
  }

  :deep(.el-tag--large) {
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
  }
</style>
