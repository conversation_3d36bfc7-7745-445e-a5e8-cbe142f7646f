<template>
  <div class="repair-management-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <div class="page-title">
          <el-icon class="title-icon"><Tools /></el-icon>
          <span>设备报修</span>
        </div>
        <div class="page-breadcrumb">
          <span>首页</span>
          <el-icon class="breadcrumb-separator"><ArrowRight /></el-icon>
          <span>设备管理</span>
          <el-icon class="breadcrumb-separator"><ArrowRight /></el-icon>
          <span class="current">设备报修</span>
        </div>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Plus" @click="openCreateDialog">
          新增报修单
        </el-button>
        <el-button
          :icon="Download"
          @click="exportData"
          :loading="exportLoading"
        >
          导出
        </el-button>
      </div>
    </div>

    <!-- 主体内容区域 -->
    <div class="main-content">
      <!-- 左侧树形导航 -->
      <div class="left-sidebar">
        <div class="sidebar-header">
          <span class="sidebar-title">报修分类</span>
          <el-button text size="small" @click="refreshTree">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>

        <div class="tree-container">
          <el-tree
            ref="treeRef"
            :data="treeData"
            :props="treeProps"
            node-key="id"
            :default-expand-all="true"
            :expand-on-click-node="false"
            :highlight-current="true"
            @node-click="handleTreeNodeClick"
            class="repair-tree"
          >
            <template #default="{ node, data }">
              <div class="tree-node">
                <div class="node-icon">
                  <el-icon v-if="data.type === 'category'"><Folder /></el-icon>
                  <el-icon v-else-if="data.type === 'device'"
                    ><Monitor
                  /></el-icon>
                  <el-icon v-else><Document /></el-icon>
                </div>
                <span class="node-label">{{ node.label }}</span>
                <span class="node-count" v-if="data.count !== undefined"
                  >({{ data.count }})</span
                >
              </div>
            </template>
          </el-tree>
        </div>

        <!-- 统计信息卡片 -->
        <div class="stats-cards">
          <div class="stat-card pending">
            <div class="stat-icon">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.pending }}</div>
              <div class="stat-label">待处理</div>
            </div>
          </div>

          <div class="stat-card processing">
            <div class="stat-icon">
              <el-icon><Setting /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.processing }}</div>
              <div class="stat-label">处理中</div>
            </div>
          </div>

          <div class="stat-card completed">
            <div class="stat-icon">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.completed }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧表格区域 -->
      <div class="right-content">
        <!-- 搜索和工具栏 -->
        <div class="content-header">
          <div class="search-bar">
            <el-input
              v-model="queryParams.repairNo"
              placeholder="请输入工单编号"
              clearable
              class="search-input"
              :prefix-icon="Search"
            />
            <el-input
              v-model="queryParams.deviceName"
              placeholder="请输入设备名称"
              clearable
              class="search-input"
              :prefix-icon="Search"
            />
            <el-select
              v-model="queryParams.status"
              placeholder="工单类型"
              clearable
              class="search-select"
            >
              <el-option label="全部" value="" />
              <el-option label="待处理" value="pending" />
              <el-option label="处理中" value="processing" />
              <el-option label="已完成" value="completed" />
            </el-select>
            <el-select
              v-model="queryParams.priority"
              placeholder="优先级"
              clearable
              class="search-select"
            >
              <el-option label="全部" value="" />
              <el-option label="低" value="low" />
              <el-option label="中" value="medium" />
              <el-option label="高" value="high" />
              <el-option label="紧急" value="urgent" />
            </el-select>
            <el-button type="primary" :icon="Search" @click="handleSearch">
              搜索
            </el-button>
            <el-button :icon="RefreshRight" @click="handleReset">
              重置
            </el-button>
          </div>

          <div class="toolbar-actions">
            <el-button
              :icon="Download"
              @click="exportData"
              :loading="exportLoading"
            >
              导出数据
            </el-button>
            <el-button type="primary" :icon="Plus" @click="openCreateDialog">
              新增报修单
            </el-button>
          </div>
        </div>

        <!-- 表格区域 -->
        <div class="table-container">
          <el-table
            :data="repairList"
            v-loading="loading"
            stripe
            border
            height="100%"
            @selection-change="handleSelectionChange"
            class="repair-table"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column
              prop="repairNo"
              label="工单编号"
              width="120"
              fixed="left"
            >
              <template #default="{ row }">
                <el-link type="primary" @click="viewDetail(row)">
                  {{ row.repairNo }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column
              prop="deviceName"
              label="设备名称"
              width="150"
              show-overflow-tooltip
            />
            <el-table-column
              prop="faultDescription"
              label="故障描述"
              min-width="200"
              show-overflow-tooltip
            />
            <el-table-column
              prop="priority"
              label="优先级"
              width="80"
              align="center"
            >
              <template #default="{ row }">
                <el-tag :type="getPriorityType(row.priority)" size="small">
                  {{ getPriorityText(row.priority) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="status"
              label="状态"
              width="100"
              align="center"
            >
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)" size="small">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="reportUser" label="报修人" width="100" />
            <el-table-column prop="assignedUser" label="处理人" width="100" />
            <el-table-column prop="reportTime" label="报修时间" width="160" />
            <el-table-column
              label="操作"
              width="180"
              fixed="right"
              align="center"
            >
              <template #default="{ row }">
                <el-button
                  type="primary"
                  link
                  size="small"
                  @click="viewDetail(row)"
                >
                  查看
                </el-button>
                <el-button
                  type="primary"
                  link
                  size="small"
                  @click="editRepair(row)"
                  v-if="row.status !== 'completed'"
                >
                  编辑
                </el-button>
                <el-button
                  type="success"
                  link
                  size="small"
                  @click="processRepair(row)"
                  v-if="row.status === 'pending'"
                >
                  处理
                </el-button>
                <el-button
                  type="warning"
                  link
                  size="small"
                  @click="completeRepair(row)"
                  v-if="row.status === 'processing'"
                >
                  完成
                </el-button>
                <el-button
                  type="danger"
                  link
                  size="small"
                  @click="deleteRepair(row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 新建/编辑对话框 -->
    <repair-form-dialog
      v-model="dialogVisible"
      :form-data="currentRepair"
      :is-edit="isEdit"
      @confirm="handleFormConfirm"
    />

    <!-- 详情对话框 -->
    <repair-detail-dialog
      v-model="detailDialogVisible"
      :repair-data="currentRepair"
    />
  </div>
</template>

<script setup>
  import { ref, reactive, computed, onMounted } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import {
    Tools,
    ArrowRight,
    Plus,
    Download,
    Refresh,
    Folder,
    Monitor,
    Document,
    Clock,
    Setting,
    CircleCheck,
    Search,
    RefreshRight
  } from '@element-plus/icons-vue';
  import RepairFormDialog from './components/RepairFormDialog.vue';
  import RepairDetailDialog from './components/RepairDetailDialog.vue';

  // 响应式数据
  const loading = ref(false);
  const exportLoading = ref(false);
  const dialogVisible = ref(false);
  const detailDialogVisible = ref(false);
  const isEdit = ref(false);
  const treeRef = ref();

  // 查询参数
  const queryParams = reactive({
    repairNo: '',
    deviceName: '',
    status: '',
    priority: '',
    categoryId: ''
  });

  // 分页参数
  const pagination = reactive({
    currentPage: 1,
    pageSize: 20,
    total: 0
  });

  // 当前选中的报修单
  const currentRepair = ref({});
  const selectedRows = ref([]);

  // 树形数据
  const treeData = ref([
    {
      id: 'all',
      label: '全部设备',
      type: 'category',
      count: 156,
      children: [
        {
          id: 'computer',
          label: '计算机设备',
          type: 'category',
          count: 45,
          children: [
            { id: 'desktop', label: '台式机', type: 'device', count: 23 },
            { id: 'laptop', label: '笔记本', type: 'device', count: 22 }
          ]
        },
        {
          id: 'network',
          label: '网络设备',
          type: 'category',
          count: 32,
          children: [
            { id: 'router', label: '路由器', type: 'device', count: 15 },
            { id: 'switch', label: '交换机', type: 'device', count: 17 }
          ]
        },
        {
          id: 'printer',
          label: '打印设备',
          type: 'category',
          count: 28,
          children: [
            { id: 'laser', label: '激光打印机', type: 'device', count: 18 },
            { id: 'inkjet', label: '喷墨打印机', type: 'device', count: 10 }
          ]
        },
        {
          id: 'other',
          label: '其他设备',
          type: 'category',
          count: 51
        }
      ]
    }
  ]);

  const treeProps = {
    children: 'children',
    label: 'label'
  };

  // 统计数据
  const statistics = reactive({
    total: 156,
    pending: 23,
    processing: 15,
    completed: 118,
    urgent: 5
  });

  // 报修单列表
  const repairList = ref([
    {
      id: 1,
      repairNo: 'R-2024-001',
      deviceName: '办公室台式机-001',
      faultDescription: '电脑无法开机，按电源键无反应',
      priority: 'high',
      status: 'pending',
      reportUser: '张三',
      assignedUser: '李工程师',
      reportTime: '2024-01-15 09:30:00',
      category: 'computer'
    },
    {
      id: 2,
      repairNo: 'R-2024-002',
      deviceName: '会议室投影仪',
      faultDescription: '投影仪显示模糊，需要清洁镜头',
      priority: 'medium',
      status: 'processing',
      reportUser: '王五',
      assignedUser: '赵工程师',
      reportTime: '2024-01-15 10:15:00',
      category: 'other'
    }
  ]);

  // 方法
  const refreshTree = () => {
    ElMessage.success('刷新成功');
  };

  const handleTreeNodeClick = (data) => {
    queryParams.categoryId = data.id === 'all' ? '' : data.id;
    handleSearch();
  };

  const handleSearch = () => {
    loading.value = true;
    // 模拟搜索
    setTimeout(() => {
      loading.value = false;
      ElMessage.success('搜索完成');
    }, 1000);
  };

  const handleReset = () => {
    Object.assign(queryParams, {
      repairNo: '',
      deviceName: '',
      status: '',
      priority: '',
      categoryId: ''
    });
    handleSearch();
  };

  const exportData = () => {
    exportLoading.value = true;
    setTimeout(() => {
      exportLoading.value = false;
      ElMessage.success('导出成功');
    }, 2000);
  };

  const openCreateDialog = () => {
    currentRepair.value = {};
    isEdit.value = false;
    dialogVisible.value = true;
  };

  const viewDetail = (row) => {
    currentRepair.value = { ...row };
    detailDialogVisible.value = true;
  };

  const editRepair = (row) => {
    currentRepair.value = { ...row };
    isEdit.value = true;
    dialogVisible.value = true;
  };

  const processRepair = (row) => {
    ElMessageBox.confirm('确认开始处理此报修单？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      ElMessage.success('已开始处理');
    });
  };

  const completeRepair = (row) => {
    ElMessageBox.confirm('确认完成此报修单？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      ElMessage.success('已完成处理');
    });
  };

  const deleteRepair = (row) => {
    ElMessageBox.confirm('确认删除此报修单？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      ElMessage.success('删除成功');
    });
  };

  const handleSelectionChange = (selection) => {
    selectedRows.value = selection;
  };

  const handleSizeChange = (size) => {
    pagination.pageSize = size;
    handleSearch();
  };

  const handleCurrentChange = (page) => {
    pagination.currentPage = page;
    handleSearch();
  };

  const handleFormConfirm = (formData) => {
    if (isEdit.value) {
      ElMessage.success('修改成功');
    } else {
      ElMessage.success('创建成功');
    }
    dialogVisible.value = false;
    handleSearch();
  };

  // 工具方法
  const getPriorityType = (priority) => {
    const types = {
      low: '',
      medium: 'warning',
      high: 'danger',
      urgent: 'danger'
    };
    return types[priority] || '';
  };

  const getPriorityText = (priority) => {
    const texts = {
      low: '低',
      medium: '中',
      high: '高',
      urgent: '紧急'
    };
    return texts[priority] || priority;
  };

  const getStatusType = (status) => {
    const types = {
      pending: 'warning',
      processing: 'primary',
      completed: 'success',
      closed: 'info'
    };
    return types[status] || '';
  };

  const getStatusText = (status) => {
    const texts = {
      pending: '待处理',
      processing: '处理中',
      completed: '已完成',
      closed: '已关闭'
    };
    return texts[status] || status;
  };

  // 生命周期
  onMounted(() => {
    handleSearch();
  });
</script>

<style scoped>
  .repair-management-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f5f7fa;
  }

  /* 页面头部 */
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: white;
    border-bottom: 1px solid #e4e7ed;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .header-left {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .page-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 20px;
    font-weight: 600;
    color: #303133;
  }

  .title-icon {
    color: #409eff;
  }

  .page-breadcrumb {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    color: #909399;
  }

  .breadcrumb-separator {
    font-size: 12px;
  }

  .current {
    color: #409eff;
  }

  .header-right {
    display: flex;
    gap: 12px;
  }

  /* 主体内容 */
  .main-content {
    flex: 1;
    display: flex;
    gap: 16px;
    padding: 16px 24px;
    overflow: hidden;
  }

  /* 左侧边栏 */
  .left-sidebar {
    width: 280px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e4e7ed;
    background: #fafbfc;
  }

  .sidebar-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .tree-container {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
  }

  .repair-tree {
    background: transparent;
  }

  .tree-node {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
  }

  .node-icon {
    color: #909399;
  }

  .node-label {
    flex: 1;
    font-size: 14px;
  }

  .node-count {
    font-size: 12px;
    color: #909399;
  }

  /* 统计卡片 */
  .stats-cards {
    padding: 16px;
    border-top: 1px solid #e4e7ed;
    background: #fafbfc;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .stat-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: white;
    border-radius: 6px;
    border-left: 4px solid #e4e7ed;
    transition: all 0.3s;
  }

  .stat-card.pending {
    border-left-color: #e6a23c;
  }

  .stat-card.processing {
    border-left-color: #409eff;
  }

  .stat-card.completed {
    border-left-color: #67c23a;
  }

  .stat-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f7fa;
  }

  .stat-card.pending .stat-icon {
    background: #fdf6ec;
    color: #e6a23c;
  }

  .stat-card.processing .stat-icon {
    background: #ecf5ff;
    color: #409eff;
  }

  .stat-card.completed .stat-icon {
    background: #f0f9ff;
    color: #67c23a;
  }

  .stat-info {
    flex: 1;
  }

  .stat-number {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    line-height: 1;
  }

  .stat-label {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }

  /* 右侧内容区域 */
  .right-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .content-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e4e7ed;
    background: #fafbfc;
  }

  .search-bar {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    flex-wrap: wrap;
  }

  .search-input {
    width: 200px;
  }

  .search-select {
    width: 120px;
  }

  .toolbar-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  /* 表格区域 */
  .table-container {
    flex: 1;
    padding: 0;
    overflow: hidden;
  }

  .repair-table {
    height: 100%;
  }

  .repair-table :deep(.el-table__body-wrapper) {
    overflow-y: auto;
  }

  /* 分页 */
  .pagination-container {
    padding: 16px 20px;
    border-top: 1px solid #e4e7ed;
    background: #fafbfc;
    display: flex;
    justify-content: flex-end;
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .left-sidebar {
      width: 240px;
    }

    .search-input {
      width: 160px;
    }
  }

  @media (max-width: 768px) {
    .main-content {
      flex-direction: column;
      gap: 12px;
    }

    .left-sidebar {
      width: 100%;
      height: 300px;
    }

    .search-bar {
      flex-direction: column;
      align-items: stretch;
    }

    .search-input,
    .search-select {
      width: 100%;
    }

    .toolbar-actions {
      justify-content: stretch;
    }

    .toolbar-actions .el-button {
      flex: 1;
    }
  }

  /* 树形组件样式优化 */
  .repair-tree :deep(.el-tree-node__content) {
    height: 36px;
    padding: 0 8px;
    border-radius: 4px;
    margin: 2px 0;
    transition: all 0.3s;
  }

  .repair-tree :deep(.el-tree-node__content:hover) {
    background-color: #f5f7fa;
  }

  .repair-tree :deep(.is-current > .el-tree-node__content) {
    background-color: #ecf5ff;
    color: #409eff;
  }

  .repair-tree :deep(.el-tree-node__expand-icon) {
    color: #c0c4cc;
  }

  .repair-tree :deep(.el-tree-node__expand-icon.expanded) {
    transform: rotate(90deg);
  }

  /* 表格样式优化 */
  .repair-table :deep(.el-table__header) {
    background-color: #fafbfc;
  }

  .repair-table :deep(.el-table__header th) {
    background-color: #fafbfc;
    color: #606266;
    font-weight: 600;
  }

  .repair-table :deep(.el-table__row:hover) {
    background-color: #f5f7fa;
  }

  .repair-table :deep(.el-link) {
    font-weight: 500;
  }

  /* 标签样式 */
  .el-tag {
    border: none;
    font-weight: 500;
  }
</style>
