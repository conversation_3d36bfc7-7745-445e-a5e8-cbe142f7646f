<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑报修' : '新增报修'"
    width="700px"
    :close-on-click-modal="false"
    class="repair-form-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      class="repair-form"
    >
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="设备名称" prop="deviceName">
            <el-input
              v-model="formData.deviceName"
              placeholder="请输入设备名称"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备位置" prop="deviceLocation">
            <el-input
              v-model="formData.deviceLocation"
              placeholder="请输入设备位置"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="报修人" prop="reporterName">
            <el-input
              v-model="formData.reporterName"
              placeholder="请输入报修人姓名"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="紧急程度" prop="priority">
            <el-select
              v-model="formData.priority"
              placeholder="请选择紧急程度"
              style="width: 100%"
            >
              <el-option label="低" value="low" />
              <el-option label="中" value="medium" />
              <el-option label="高" value="high" />
              <el-option label="紧急" value="urgent" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="故障描述" prop="faultDescription">
            <el-input
              v-model="formData.faultDescription"
              type="textarea"
              :rows="4"
              placeholder="请详细描述设备故障情况..."
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="isEdit">
          <el-form-item label="维修人员" prop="repairPerson">
            <el-input
              v-model="formData.repairPerson"
              placeholder="请输入维修人员"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="isEdit">
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="formData.status"
              placeholder="请选择状态"
              style="width: 100%"
            >
              <el-option label="待处理" value="pending" />
              <el-option label="维修中" value="processing" />
              <el-option label="已完成" value="completed" />
              <el-option label="已取消" value="cancelled" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
        >
          {{ isEdit ? '更新' : '提交' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
  import { ref, computed, watch, nextTick } from 'vue';
  import { ElMessage } from 'element-plus';
  import type { FormInstance, FormRules } from 'element-plus';

  /** 设备报修表单 */
  defineOptions({ name: 'DeviceRepairForm' });

  interface Props {
    modelValue: boolean;
    data?: any;
  }

  interface Emits {
    (e: 'update:modelValue', value: boolean): void;
    (e: 'success'): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  const formRef = ref<FormInstance>();
  const submitLoading = ref(false);

  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  });

  const isEdit = computed(() => !!props.data?.id);

  // 表单数据
  const formData = ref({
    deviceName: '',
    deviceLocation: '',
    reporterName: '',
    priority: 'medium',
    faultDescription: '',
    repairPerson: '',
    status: 'pending'
  });

  // 重置表单
  const resetForm = () => {
    formData.value = {
      deviceName: '',
      deviceLocation: '',
      reporterName: '',
      priority: 'medium',
      faultDescription: '',
      repairPerson: '',
      status: 'pending'
    };
    nextTick(() => {
      formRef.value?.clearValidate();
    });
  };

  // 表单验证规则
  const formRules: FormRules = {
    deviceName: [
      { required: true, message: '请输入设备名称', trigger: 'blur' }
    ],
    deviceLocation: [
      { required: true, message: '请输入设备位置', trigger: 'blur' }
    ],
    reporterName: [
      { required: true, message: '请输入报修人姓名', trigger: 'blur' }
    ],
    priority: [
      { required: true, message: '请选择紧急程度', trigger: 'change' }
    ],
    faultDescription: [
      { required: true, message: '请输入故障描述', trigger: 'blur' },
      { min: 10, message: '故障描述至少10个字符', trigger: 'blur' }
    ]
  };

  // 监听数据变化，初始化表单
  watch(
    () => props.data,
    (newData) => {
      if (newData) {
        formData.value = {
          deviceName: newData.deviceName || '',
          deviceLocation: newData.deviceLocation || '',
          reporterName: newData.reporterName || '',
          priority: newData.priority || 'medium',
          faultDescription: newData.faultDescription || '',
          repairPerson: newData.repairPerson || '',
          status: newData.status || 'pending'
        };
      } else {
        resetForm();
      }
    },
    { immediate: true }
  );

  // 关闭对话框
  const handleClose = () => {
    visible.value = false;
    resetForm();
  };

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return;

    try {
      await formRef.value.validate();
      submitLoading.value = true;

      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 1000));

      ElMessage.success(isEdit.value ? '更新成功' : '提交成功');
      emit('success');
      handleClose();
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      submitLoading.value = false;
    }
  };
</script>

<style lang="scss" scoped>
  .repair-form-dialog {
    :deep(.el-dialog__body) {
      padding: 24px;
    }
  }

  .repair-form {
    .el-form-item {
      margin-bottom: 20px;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
</style>
