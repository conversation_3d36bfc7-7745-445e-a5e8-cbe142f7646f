<template>
  <ele-page flex-table>
    <!-- 主体内容区域 -->
    <div class="main-content">
      <!-- 左侧树形导航 -->
      <div class="left-sidebar">
        <div class="sidebar-header">
          <span class="sidebar-title">故障分类</span>
          <el-button text size="small" @click="refreshTree">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>

        <div class="tree-container">
          <el-tree
            ref="treeRef"
            :data="treeData"
            :props="treeProps"
            node-key="id"
            :default-expand-all="true"
            :expand-on-click-node="false"
            :highlight-current="true"
            @node-click="handleTreeNodeClick"
            class="repair-tree"
          >
            <template #default="{ node, data }">
              <div class="tree-node">
                <div class="node-icon">
                  <el-icon v-if="data.type === 'category'"><Folder /></el-icon>
                  <el-icon v-else-if="data.type === 'device'"
                    ><Monitor
                  /></el-icon>
                  <el-icon v-else><Document /></el-icon>
                </div>
                <span class="node-label">{{ node.label }}</span>
                <span class="node-count" v-if="data.count !== undefined"
                  >({{ data.count }})</span
                >
              </div>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 右侧表格区域 -->
      <div class="right-content">
        <!-- 统计信息卡片 -->
        <div class="stats-overview">
          <div class="stat-card pending">
            <div class="stat-icon">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.pending }}</div>
              <div class="stat-label">待处理</div>
            </div>
          </div>

          <div class="stat-card processing">
            <div class="stat-icon">
              <el-icon><Setting /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.processing }}</div>
              <div class="stat-label">处理中</div>
            </div>
          </div>

          <div class="stat-card completed">
            <div class="stat-icon">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.completed }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>
        </div>

        <!-- 搜索和工具栏 -->
        <ele-card :body-style="{ paddingBottom: '2px' }">
          <div class="search-bar">
            <el-input
              v-model="queryParams.repairNo"
              placeholder="请输入工单编号"
              clearable
              class="search-input"
              :prefix-icon="Search"
            />
            <el-input
              v-model="queryParams.deviceName"
              placeholder="请输入设备名称"
              clearable
              class="search-input"
              :prefix-icon="Search"
            />
            <el-select
              v-model="queryParams.status"
              placeholder="工单类型"
              clearable
              class="search-select"
            >
              <el-option label="全部" value="" />
              <el-option label="待处理" value="pending" />
              <el-option label="处理中" value="processing" />
              <el-option label="已完成" value="completed" />
            </el-select>
            <el-select
              v-model="queryParams.priority"
              placeholder="优先级"
              clearable
              class="search-select"
            >
              <el-option label="全部" value="" />
              <el-option label="低" value="low" />
              <el-option label="中" value="medium" />
              <el-option label="高" value="high" />
              <el-option label="紧急" value="urgent" />
            </el-select>
            <el-button type="primary" :icon="Search" @click="handleSearch">
              搜索
            </el-button>
            <el-button :icon="RefreshRight" @click="handleReset">
              重置
            </el-button>
          </div>
        </ele-card>

        <!-- 表格区域 -->
        <ele-card flex-table :body-style="{ paddingTop: '8px' }">
          <ele-pro-table
            ref="tableRef"
            row-key="id"
            :columns="columns"
            :datasource="datasource"
            :show-overflow-tooltip="true"
            stripe
            @selection-change="handleSelectionChange"
            class="repair-table"
          >
            <template #toolbar>
              <el-button type="primary" :icon="Plus" @click="openCreateDialog">
                新增报修单
              </el-button>
              <el-button
                :icon="Download"
                @click="exportData"
                :loading="exportLoading"
              >
                导出数据
              </el-button>
            </template>

            <template #repairNo="{ row }">
              <el-link type="primary" @click="viewDetail(row)">
                {{ row.repairNo }}
              </el-link>
            </template>

            <template #faultType="{ row }">
              <el-tag size="small" effect="plain">
                {{ row.faultType }}
              </el-tag>
            </template>

            <template #priority="{ row }">
              <el-tag :type="getPriorityType(row.priority)" size="small">
                {{ getPriorityText(row.priority) }}
              </el-tag>
            </template>

            <template #status="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>

            <template #action="{ row }">
              <el-button
                type="primary"
                link
                size="small"
                @click="viewDetail(row)"
              >
                查看
              </el-button>
              <el-button
                type="primary"
                link
                size="small"
                @click="editRepair(row)"
                v-if="row.status !== 'completed'"
              >
                编辑
              </el-button>
              <el-button
                type="success"
                link
                size="small"
                @click="processRepair(row)"
                v-if="row.status === 'pending'"
              >
                处理
              </el-button>
              <el-button
                type="warning"
                link
                size="small"
                @click="completeRepair(row)"
                v-if="row.status === 'processing'"
              >
                完成
              </el-button>
              <el-button
                type="danger"
                link
                size="small"
                @click="deleteRepair(row)"
              >
                删除
              </el-button>
            </template>
          </ele-pro-table>
        </ele-card>
      </div>
    </div>

    <!-- 新建/编辑对话框 -->
    <repair-form-dialog
      v-model="dialogVisible"
      :form-data="currentRepair"
      :is-edit="isEdit"
      @confirm="handleFormConfirm"
    />

    <!-- 详情抽屉 -->
    <repair-detail-drawer
      v-model="detailDrawerVisible"
      :repair-data="currentRepair"
    />
  </ele-page>
</template>

<script setup>
  import { ref, reactive, computed, onMounted } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import {
    Tools,
    ArrowRight,
    Plus,
    Download,
    Refresh,
    Folder,
    Monitor,
    Document,
    Clock,
    Setting,
    CircleCheck,
    Search,
    RefreshRight
  } from '@element-plus/icons-vue';
  import RepairFormDialog from './components/RepairFormDialog.vue';
  import RepairDetailDrawer from './components/RepairDetailDrawer.vue';

  // 响应式数据
  const loading = ref(false);
  const exportLoading = ref(false);
  const dialogVisible = ref(false);
  const detailDrawerVisible = ref(false);
  const isEdit = ref(false);
  const treeRef = ref();
  const tableRef = ref();

  // 查询参数
  const queryParams = reactive({
    repairNo: '',
    deviceName: '',
    status: '',
    priority: '',
    categoryId: ''
  });

  // 分页参数
  const pagination = reactive({
    currentPage: 1,
    pageSize: 20,
    total: 0
  });

  // 当前选中的报修单
  const currentRepair = ref({});
  const selectedRows = ref([]);

  // 树形数据 - 按报修类型分类
  const treeData = ref([
    {
      id: 'all',
      label: '全部报修',
      type: 'root',
      count: 20,
      children: [
        {
          id: 'hardware',
          label: '硬件故障',
          type: 'category',
          count: 11,
          children: [
            { id: 'power', label: '电源故障', type: 'fault', count: 1 },
            { id: 'display', label: '显示故障', type: 'fault', count: 2 },
            { id: 'memory', label: '内存故障', type: 'fault', count: 1 },
            { id: 'disk', label: '硬盘故障', type: 'fault', count: 1 },
            { id: 'motherboard', label: '主板故障', type: 'fault', count: 1 },
            { id: 'peripheral', label: '外设故障', type: 'fault', count: 2 }
          ]
        },
        {
          id: 'software',
          label: '软件问题',
          type: 'category',
          count: 5,
          children: [
            { id: 'system', label: '系统故障', type: 'fault', count: 2 },
            { id: 'application', label: '应用程序', type: 'fault', count: 1 },
            { id: 'driver', label: '驱动问题', type: 'fault', count: 1 },
            { id: 'virus', label: '病毒感染', type: 'fault', count: 1 }
          ]
        },
        {
          id: 'network',
          label: '网络问题',
          type: 'category',
          count: 4,
          children: [
            { id: 'connection', label: '连接故障', type: 'fault', count: 2 },
            { id: 'speed', label: '网速异常', type: 'fault', count: 1 },
            { id: 'wifi', label: 'WiFi问题', type: 'fault', count: 1 }
          ]
        },
        {
          id: 'maintenance',
          label: '维护保养',
          type: 'category',
          count: 3,
          children: [
            { id: 'cleaning', label: '清洁保养', type: 'fault', count: 1 },
            { id: 'upgrade', label: '升级更新', type: 'fault', count: 1 },
            { id: 'inspection', label: '定期检查', type: 'fault', count: 1 }
          ]
        }
      ]
    }
  ]);

  const treeProps = {
    children: 'children',
    label: 'label'
  };

  // 表格列配置
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'repairNo',
      label: '工单编号',
      width: 120,
      fixed: 'left',
      slot: 'repairNo'
    },
    {
      prop: 'deviceName',
      label: '设备名称',
      width: 150,
      showOverflowTooltip: true
    },
    {
      prop: 'faultType',
      label: '故障类型',
      width: 100,
      align: 'center',
      slot: 'faultType'
    },
    {
      prop: 'faultDescription',
      label: '故障描述',
      minWidth: 180,
      showOverflowTooltip: true
    },
    {
      prop: 'priority',
      label: '优先级',
      width: 80,
      align: 'center',
      slot: 'priority'
    },
    {
      prop: 'status',
      label: '状态',
      width: 100,
      align: 'center',
      slot: 'status'
    },
    {
      prop: 'reportUser',
      label: '报修人',
      width: 100
    },
    {
      prop: 'assignedUser',
      label: '处理人',
      width: 100
    },
    {
      prop: 'reportTime',
      label: '报修时间',
      width: 160
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      slot: 'action',
      align: 'center',
      fixed: 'right'
    }
  ]);

  // 统计数据
  const statistics = reactive({
    total: 20,
    pending: 8,
    processing: 6,
    completed: 6,
    urgent: 2
  });

  // 报修单列表
  const repairList = ref([
    {
      id: 1,
      repairNo: 'I-371443',
      deviceName: '办公室台式机-001',
      faultDescription: '电脑无法开机，按电源键无反应，疑似电源故障',
      priority: 'high',
      status: 'pending',
      reportUser: '张三',
      assignedUser: '李工程师',
      reportTime: '2024-01-15 09:30:00',
      category: 'power',
      faultType: '硬件故障'
    },
    {
      id: 2,
      repairNo: 'I-370867',
      deviceName: '会议室投影仪',
      faultDescription: '系统蓝屏，无法正常启动操作系统',
      priority: 'medium',
      status: 'processing',
      reportUser: '王五',
      assignedUser: '赵工程师',
      reportTime: '2024-01-15 10:15:00',
      category: 'system',
      faultType: '软件问题'
    },
    {
      id: 3,
      repairNo: 'I-370866',
      deviceName: '财务部打印机',
      faultDescription: '网络连接不稳定，经常断线重连',
      priority: 'medium',
      status: 'pending',
      reportUser: '李四',
      assignedUser: '张工程师',
      reportTime: '2024-01-15 11:20:00',
      category: 'connection',
      faultType: '网络问题'
    },
    {
      id: 4,
      repairNo: 'I-370851',
      deviceName: '前台接待电脑',
      faultDescription: '显示器花屏，颜色显示异常',
      priority: 'medium',
      status: 'processing',
      reportUser: '陈六',
      assignedUser: '王工程师',
      reportTime: '2024-01-15 14:30:00',
      category: 'display',
      faultType: '硬件故障'
    },
    {
      id: 5,
      repairNo: 'I-332674',
      deviceName: '人事部笔记本',
      faultDescription: '应用程序频繁崩溃，无法正常使用',
      priority: 'medium',
      status: 'completed',
      reportUser: '刘七',
      assignedUser: '李工程师',
      reportTime: '2024-01-14 16:45:00',
      category: 'application',
      faultType: '软件问题'
    },
    {
      id: 6,
      repairNo: 'I-367773',
      deviceName: '服务器机房主机',
      faultDescription: '硬盘读写速度异常缓慢，系统响应迟钝',
      priority: 'high',
      status: 'pending',
      reportUser: '周八',
      assignedUser: '赵工程师',
      reportTime: '2024-01-15 08:20:00',
      category: 'disk',
      faultType: '硬件故障'
    },
    {
      id: 7,
      repairNo: 'I-363267',
      deviceName: '销售部台式机-003',
      faultDescription: '内存条松动，开机后频繁重启',
      priority: 'high',
      status: 'processing',
      reportUser: '孙九',
      assignedUser: '李工程师',
      reportTime: '2024-01-15 13:45:00',
      category: 'memory',
      faultType: '硬件故障'
    },
    {
      id: 8,
      repairNo: 'I-362984',
      deviceName: '研发部工作站',
      faultDescription: '主板电容鼓包，系统不稳定',
      priority: 'urgent',
      status: 'pending',
      reportUser: '吴十',
      assignedUser: '张工程师',
      reportTime: '2024-01-15 15:20:00',
      category: 'motherboard',
      faultType: '硬件故障'
    },
    {
      id: 9,
      repairNo: 'I-361847',
      deviceName: '客服部耳机',
      faultDescription: '耳机左声道无声音，右声道正常',
      priority: 'low',
      status: 'completed',
      reportUser: '郑十一',
      assignedUser: '王工程师',
      reportTime: '2024-01-14 11:30:00',
      category: 'peripheral',
      faultType: '硬件故障'
    },
    {
      id: 10,
      repairNo: 'I-360592',
      deviceName: '市场部笔记本',
      faultDescription: '驱动程序冲突，蓝屏错误代码0x0000007E',
      priority: 'medium',
      status: 'processing',
      reportUser: '钱十二',
      assignedUser: '赵工程师',
      reportTime: '2024-01-15 09:15:00',
      category: 'driver',
      faultType: '软件问题'
    },
    {
      id: 11,
      repairNo: 'I-359438',
      deviceName: '行政部扫描仪',
      faultDescription: '检测到病毒感染，系统运行缓慢',
      priority: 'high',
      status: 'completed',
      reportUser: '赵十三',
      assignedUser: '李工程师',
      reportTime: '2024-01-14 14:20:00',
      category: 'virus',
      faultType: '软件问题'
    },
    {
      id: 12,
      repairNo: 'I-358274',
      deviceName: '总经理办公室电脑',
      faultDescription: 'WiFi信号弱，连接不稳定',
      priority: 'medium',
      status: 'pending',
      reportUser: '冯十四',
      assignedUser: '张工程师',
      reportTime: '2024-01-15 16:10:00',
      category: 'wifi',
      faultType: '网络问题'
    },
    {
      id: 13,
      repairNo: 'I-357163',
      deviceName: '技术部服务器',
      faultDescription: '网络传输速度异常，下载速度只有10KB/s',
      priority: 'high',
      status: 'processing',
      reportUser: '陈十五',
      assignedUser: '王工程师',
      reportTime: '2024-01-15 12:40:00',
      category: 'speed',
      faultType: '网络问题'
    },
    {
      id: 14,
      repairNo: 'I-356029',
      deviceName: '库房管理电脑',
      faultDescription: '定期清洁保养，键盘按键粘滞',
      priority: 'low',
      status: 'completed',
      reportUser: '周十六',
      assignedUser: '赵工程师',
      reportTime: '2024-01-14 10:00:00',
      category: 'cleaning',
      faultType: '维护保养'
    },
    {
      id: 15,
      repairNo: 'I-354875',
      deviceName: '培训室投影仪',
      faultDescription: '系统升级到Windows 11，需要更新驱动',
      priority: 'medium',
      status: 'pending',
      reportUser: '吴十七',
      assignedUser: '李工程师',
      reportTime: '2024-01-15 17:30:00',
      category: 'upgrade',
      faultType: '维护保养'
    },
    {
      id: 16,
      repairNo: 'I-353641',
      deviceName: '安保部监控主机',
      faultDescription: '年度安全检查，检测硬件状态',
      priority: 'low',
      status: 'processing',
      reportUser: '郑十八',
      assignedUser: '张工程师',
      reportTime: '2024-01-15 08:00:00',
      category: 'inspection',
      faultType: '维护保养'
    },
    {
      id: 17,
      repairNo: 'I-352487',
      deviceName: '设计部图形工作站',
      faultDescription: '显卡风扇异响，温度过高导致性能下降',
      priority: 'high',
      status: 'pending',
      reportUser: '王十九',
      assignedUser: '王工程师',
      reportTime: '2024-01-15 14:15:00',
      category: 'display',
      faultType: '硬件故障'
    },
    {
      id: 18,
      repairNo: 'I-351293',
      deviceName: '法务部打印机',
      faultDescription: '打印质量下降，墨盒需要更换',
      priority: 'low',
      status: 'completed',
      reportUser: '李二十',
      assignedUser: '赵工程师',
      reportTime: '2024-01-14 15:45:00',
      category: 'peripheral',
      faultType: '硬件故障'
    },
    {
      id: 19,
      repairNo: 'I-350139',
      deviceName: '采购部笔记本',
      faultDescription: '系统文件损坏，无法正常启动到桌面',
      priority: 'urgent',
      status: 'processing',
      reportUser: '张二一',
      assignedUser: '李工程师',
      reportTime: '2024-01-15 11:50:00',
      category: 'system',
      faultType: '软件问题'
    },
    {
      id: 20,
      repairNo: 'I-348975',
      deviceName: '质检部测试设备',
      faultDescription: '网络端口损坏，无法连接到局域网',
      priority: 'medium',
      status: 'pending',
      reportUser: '赵二二',
      assignedUser: '张工程师',
      reportTime: '2024-01-15 16:25:00',
      category: 'connection',
      faultType: '网络问题'
    }
  ]);
  const getTableList = async () => {
    return repairList.value;
  };
  // 表格数据源
  const datasource = ({ pages, where, filters }) => {
    return getTableList();
  };

  // 方法
  const refreshTree = () => {
    ElMessage.success('刷新成功');
  };

  const handleTreeNodeClick = (data) => {
    queryParams.categoryId = data.id === 'all' ? '' : data.id;
    handleSearch();
  };

  const handleSearch = () => {
    reload();
  };

  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };

  const handleReset = () => {
    Object.assign(queryParams, {
      repairNo: '',
      deviceName: '',
      status: '',
      priority: '',
      categoryId: ''
    });
    handleSearch();
  };

  const exportData = () => {
    exportLoading.value = true;
    setTimeout(() => {
      exportLoading.value = false;
      ElMessage.success('导出成功');
    }, 2000);
  };

  const openCreateDialog = () => {
    currentRepair.value = {};
    isEdit.value = false;
    dialogVisible.value = true;
  };

  const viewDetail = (row) => {
    currentRepair.value = { ...row };
    detailDrawerVisible.value = true;
  };

  const editRepair = (row) => {
    currentRepair.value = { ...row };
    isEdit.value = true;
    dialogVisible.value = true;
  };

  const processRepair = (row) => {
    ElMessageBox.confirm('确认开始处理此报修单？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      ElMessage.success('已开始处理');
    });
  };

  const completeRepair = (row) => {
    ElMessageBox.confirm('确认完成此报修单？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      ElMessage.success('已完成处理');
    });
  };

  const deleteRepair = (row) => {
    ElMessageBox.confirm('确认删除此报修单？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      ElMessage.success('删除成功');
    });
  };

  const handleSelectionChange = (selection) => {
    selectedRows.value = selection;
  };

  const handleFormConfirm = (formData) => {
    if (isEdit.value) {
      ElMessage.success('修改成功');
    } else {
      ElMessage.success('创建成功');
    }
    dialogVisible.value = false;
    reload();
  };

  // 工具方法
  const getPriorityType = (priority) => {
    const types = {
      low: '',
      medium: 'warning',
      high: 'danger',
      urgent: 'danger'
    };
    return types[priority] || '';
  };

  const getPriorityText = (priority) => {
    const texts = {
      low: '低',
      medium: '中',
      high: '高',
      urgent: '紧急'
    };
    return texts[priority] || priority;
  };

  const getStatusType = (status) => {
    const types = {
      pending: 'warning',
      processing: 'primary',
      completed: 'success',
      closed: 'info'
    };
    return types[status] || '';
  };

  const getStatusText = (status) => {
    const texts = {
      pending: '待处理',
      processing: '处理中',
      completed: '已完成',
      closed: '已关闭'
    };
    return texts[status] || status;
  };

  // 生命周期
  onMounted(() => {
    handleSearch();
  });
</script>

<style scoped>
  .repair-management-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f5f7fa;
  }

  /* 页面头部 */
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: white;
    border-bottom: 1px solid #e4e7ed;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .header-left {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .page-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 20px;
    font-weight: 600;
    color: #303133;
  }

  .title-icon {
    color: #409eff;
  }

  .page-breadcrumb {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    color: #909399;
  }

  .breadcrumb-separator {
    font-size: 12px;
  }

  .current {
    color: #409eff;
  }

  .header-right {
    display: flex;
    gap: 12px;
  }

  /* 主体内容 */
  .main-content {
    flex: 1;
    display: flex;
    overflow: hidden;
    gap: 8px;
  }

  /* 左侧边栏 */
  .left-sidebar {
    width: 280px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e4e7ed;
    background: #fafbfc;
  }

  .sidebar-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .tree-container {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
  }

  .repair-tree {
    background: transparent;
  }

  .tree-node {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
  }

  .node-icon {
    color: #909399;
  }

  .node-label {
    flex: 1;
    font-size: 14px;
  }

  .node-count {
    font-size: 12px;
    color: #909399;
  }

  /* 统计卡片 */
  .stats-cards {
    padding: 16px;
    border-top: 1px solid #e4e7ed;
    background: #fafbfc;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .stat-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: white;
    border-radius: 6px;
    border-left: 4px solid #e4e7ed;
    transition: all 0.3s;
  }

  .stat-card.pending {
    border-left-color: #e6a23c;
  }

  .stat-card.processing {
    border-left-color: #409eff;
  }

  .stat-card.completed {
    border-left-color: #67c23a;
  }

  .stat-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f7fa;
  }

  .stat-card.pending .stat-icon {
    background: #fdf6ec;
    color: #e6a23c;
  }

  .stat-card.processing .stat-icon {
    background: #ecf5ff;
    color: #409eff;
  }

  .stat-card.completed .stat-icon {
    background: #f0f9ff;
    color: #67c23a;
  }

  .stat-info {
    flex: 1;
  }

  .stat-number {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    line-height: 1;
  }

  .stat-label {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }

  /* 右侧内容区域 */
  .right-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  /* 统计概览 */
  .stats-overview {
    display: flex;
    gap: 16px;
    padding: 16px 20px;
    background: #fafbfc;
    border-bottom: 1px solid #e4e7ed;
  }

  .stats-overview .stat-card {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: white;
    border-radius: 8px;
    border-left: 4px solid #e4e7ed;
    transition: all 0.3s;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .stats-overview .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .stats-overview .stat-card.pending {
    border-left-color: #e6a23c;
  }

  .stats-overview .stat-card.processing {
    border-left-color: #409eff;
  }

  .stats-overview .stat-card.completed {
    border-left-color: #67c23a;
  }

  .stats-overview .stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
  }

  .stats-overview .stat-card.pending .stat-icon {
    background: #fdf6ec;
    color: #e6a23c;
  }

  .stats-overview .stat-card.processing .stat-icon {
    background: #ecf5ff;
    color: #409eff;
  }

  .stats-overview .stat-card.completed .stat-icon {
    background: #f0f9ff;
    color: #67c23a;
  }

  .stats-overview .stat-info {
    flex: 1;
  }

  .stats-overview .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #303133;
    line-height: 1;
    margin-bottom: 4px;
  }

  .stats-overview .stat-label {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
  }

  .content-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e4e7ed;
    background: #fafbfc;
  }

  .search-bar {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    flex-wrap: wrap;
  }

  .search-input {
    width: 200px;
  }

  .search-select {
    width: 120px;
  }

  .toolbar-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  /* 表格区域 */
  .table-container {
    flex: 1;
    padding: 0;
    overflow: hidden;
  }

  .repair-table {
    height: 100%;
  }

  .repair-table :deep(.el-table__body-wrapper) {
    overflow-y: auto;
  }

  /* 分页 */
  .pagination-container {
    padding: 16px 20px;
    border-top: 1px solid #e4e7ed;
    background: #fafbfc;
    display: flex;
    justify-content: flex-end;
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .left-sidebar {
      width: 240px;
    }

    .search-input {
      width: 160px;
    }
  }

  @media (max-width: 768px) {
    .main-content {
      flex-direction: column;
      gap: 12px;
    }

    .left-sidebar {
      width: 100%;
      height: 300px;
    }

    .search-bar {
      flex-direction: column;
      align-items: stretch;
    }

    .search-input,
    .search-select {
      width: 100%;
    }

    .toolbar-actions {
      justify-content: stretch;
    }

    .toolbar-actions .el-button {
      flex: 1;
    }
  }

  /* 树形组件样式优化 */
  .repair-tree :deep(.el-tree-node__content) {
    height: 36px;
    padding: 0 8px;
    border-radius: 4px;
    margin: 2px 0;
    transition: all 0.3s;
  }

  .repair-tree :deep(.el-tree-node__content:hover) {
    background-color: #f5f7fa;
  }

  .repair-tree :deep(.is-current > .el-tree-node__content) {
    background-color: #ecf5ff;
    color: #409eff;
  }

  .repair-tree :deep(.el-tree-node__expand-icon) {
    color: #c0c4cc;
  }

  .repair-tree :deep(.el-tree-node__expand-icon.expanded) {
    transform: rotate(90deg);
  }

  /* 表格样式优化 */
  .repair-table :deep(.el-table__header) {
    background-color: #fafbfc;
  }

  .repair-table :deep(.el-table__header th) {
    background-color: #fafbfc;
    color: #606266;
    font-weight: 600;
  }

  .repair-table :deep(.el-table__row:hover) {
    background-color: #f5f7fa;
  }

  .repair-table :deep(.el-link) {
    font-weight: 500;
  }

  /* 标签样式 */
  .el-tag {
    border: none;
    font-weight: 500;
  }
</style>
