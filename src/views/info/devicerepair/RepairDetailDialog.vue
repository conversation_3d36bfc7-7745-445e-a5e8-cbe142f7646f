<template>
  <el-dialog
    v-model="visible"
    title="报修详情"
    width="800px"
    :close-on-click-modal="false"
    class="repair-detail-dialog"
  >
    <div class="detail-content" v-if="data">
      <div class="detail-header">
        <div class="repair-no">
          <span class="label">报修单号：</span>
          <span class="value">{{ data.repairNo }}</span>
        </div>
        <el-tag :type="getStatusType(data.status)" size="large">
          {{ getStatusText(data.status) }}
        </el-tag>
      </div>

      <el-row :gutter="24" class="detail-info">
        <el-col :span="12">
          <div class="info-item">
            <span class="label">设备名称：</span>
            <span class="value">{{ data.deviceName }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="label">设备位置：</span>
            <span class="value">{{ data.deviceLocation }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="label">报修人：</span>
            <span class="value">{{ data.reporterName }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="label">报修时间：</span>
            <span class="value">{{ formatDate(data.reportTime) }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="label">紧急程度：</span>
            <el-tag :type="getPriorityType(data.priority)" size="small">
              {{ getPriorityText(data.priority) }}
            </el-tag>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="label">维修人员：</span>
            <span class="value">{{ data.repairPerson || '未分配' }}</span>
          </div>
        </el-col>
      </el-row>

      <div class="fault-description">
        <div class="section-title">故障描述</div>
        <div class="description-content">
          {{ data.faultDescription }}
        </div>
      </div>

      <div class="repair-progress" v-if="data.status !== 'pending'">
        <div class="section-title">维修进度</div>
        <el-timeline>
          <el-timeline-item
            timestamp="2025-01-15 09:30:00"
            type="primary"
          >
            用户提交报修申请
          </el-timeline-item>
          <el-timeline-item
            v-if="data.status === 'processing' || data.status === 'completed'"
            timestamp="2025-01-15 10:00:00"
            type="success"
          >
            维修人员开始处理
          </el-timeline-item>
          <el-timeline-item
            v-if="data.status === 'completed'"
            timestamp="2025-01-15 15:30:00"
            type="success"
          >
            维修完成
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button 
          type="primary" 
          @click="handleEdit"
          v-if="data && data.status !== 'completed'"
        >
          编辑
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue';
  import { formatDate } from '@/utils/formatTime';

  interface Props {
    modelValue: boolean;
    data: any;
  }

  interface Emits {
    (e: 'update:modelValue', value: boolean): void;
    (e: 'edit', data: any): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  });

  const handleClose = () => {
    visible.value = false;
  };

  const handleEdit = () => {
    emit('edit', props.data);
    handleClose();
  };

  // 状态和优先级的显示处理
  const getStatusType = (status: string) => {
    const statusMap: Record<string, string> = {
      pending: 'warning',
      processing: 'primary',
      completed: 'success',
      cancelled: 'info'
    };
    return statusMap[status] || 'info';
  };

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      pending: '待处理',
      processing: '维修中',
      completed: '已完成',
      cancelled: '已取消'
    };
    return statusMap[status] || '未知';
  };

  const getPriorityType = (priority: string) => {
    const priorityMap: Record<string, string> = {
      low: 'info',
      medium: 'warning',
      high: 'danger',
      urgent: 'danger'
    };
    return priorityMap[priority] || 'info';
  };

  const getPriorityText = (priority: string) => {
    const priorityMap: Record<string, string> = {
      low: '低',
      medium: '中',
      high: '高',
      urgent: '紧急'
    };
    return priorityMap[priority] || '未知';
  };
</script>

<style lang="scss" scoped>
  .repair-detail-dialog {
    :deep(.el-dialog__body) {
      padding: 24px;
    }
  }

  .detail-content {
    .detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #e9ecef;

      .repair-no {
        .label {
          font-size: 16px;
          color: #6c757d;
        }
        .value {
          font-size: 18px;
          font-weight: 600;
          color: #2c3e50;
          margin-left: 8px;
        }
      }
    }

    .detail-info {
      margin-bottom: 24px;

      .info-item {
        margin-bottom: 16px;
        display: flex;
        align-items: center;

        .label {
          font-weight: 500;
          color: #6c757d;
          min-width: 80px;
        }

        .value {
          color: #2c3e50;
          margin-left: 8px;
        }
      }
    }

    .fault-description,
    .repair-progress {
      margin-bottom: 24px;

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 2px solid #e9ecef;
      }

      .description-content {
        background: #f8f9fa;
        padding: 16px;
        border-radius: 8px;
        color: #495057;
        line-height: 1.6;
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
</style>
