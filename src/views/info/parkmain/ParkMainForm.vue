<template>
  <ele-modal
    form
    :width="900"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      v-loading="loading"
    >
      <div class="form-grid">
        <el-form-item label="园区编码" prop="parkCode">
          <el-input disabled v-model="form.parkCode" placeholder="自动生成" />
        </el-form-item>
        <el-form-item label="园区名称" prop="parkName">
          <el-input v-model="form.parkName" placeholder="请输入园区名称" />
        </el-form-item>
        <el-form-item label="区域" prop="townId">
          <CountryAreaSelect v-model="form.townId" placeholder="请选择地区" />
        </el-form-item>
        <el-form-item label="详细地址" prop="addressDetail">
          <el-input v-model="form.addressDetail" placeholder="请输入详细地址" />
        </el-form-item>
        <el-form-item label="园区面积" prop="parkArea">
          <el-input-number
            v-model="form.parkArea"
            placeholder="请输入园区面积"
            :precision="2"
            :min="0"
            controls-position="right"
            class="ele-fluid"
          />
        </el-form-item>
        <el-form-item label="建筑面积" prop="buildingArea">
          <el-input-number
            v-model="form.buildingArea"
            placeholder="请输入建筑面积"
            :precision="2"
            :min="0"
            controls-position="right"
            class="ele-fluid"
          />
        </el-form-item>
        <el-form-item label="产证面积" prop="certificateArea">
          <el-input-number
            v-model="form.certificateArea"
            placeholder="请输入产证面积"
            :precision="2"
            :min="0"
            controls-position="right"
            class="ele-fluid"
          />
        </el-form-item>
        <el-form-item label="租赁面积" prop="leaseArea">
          <el-input-number
            v-model="form.leaseArea"
            placeholder="请输入租赁面积"
            :precision="2"
            :min="0"
            controls-position="right"
            class="ele-fluid"
          />
        </el-form-item>
        <el-form-item label="销售面积" prop="saleArea">
          <el-input-number
            v-model="form.saleArea"
            placeholder="请输入销售面积"
            :precision="2"
            :min="0"
            controls-position="right"
            class="ele-fluid"
          />
        </el-form-item>
        <el-form-item label="车位个数" prop="parkingLot">
          <el-input-number
            v-model="form.parkingLot"
            placeholder="请输入车位个数"
            :min="0"
            :precision="0"
            controls-position="right"
            class="ele-fluid"
          />
        </el-form-item>
        <el-form-item label="绿化率" prop="greeningRate">
          <el-input-number
            v-model="form.greeningRate"
            placeholder="请输入绿化率"
            :precision="2"
            :min="0"
            :max="100"
            controls-position="right"
            class="ele-fluid"
          />
        </el-form-item>
        <el-form-item label="监控覆盖率" prop="monitoringCoverage">
          <el-input-number
            v-model="form.monitoringCoverage"
            placeholder="请输入监控覆盖率"
            :precision="2"
            :min="0"
            :max="100"
            controls-position="right"
            class="ele-fluid"
          />
        </el-form-item>
        <el-form-item label="开发商" prop="developers">
          <el-input v-model="form.developers" placeholder="请输入开发商" />
        </el-form-item>
        <el-form-item label="承建商" prop="contractor">
          <el-input v-model="form.contractor" placeholder="请输入承建商" />
        </el-form-item>
        <el-form-item label="园区状态" prop="status">
          <el-select v-model="form.status" clearable placeholder="请选择状态">
            <el-option
              v-for="dict in getIntDictOptions(
                DICT_TYPE.INFO_COUNTRY_AREA_STATUS
              )"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { useMessage } from '@/hooks/web/useMessage';
  import * as ParkMainApi from '@/api/info/parkmain';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import CountryAreaSelect from '@/views/info/countryarea/countryarea-select-enable.vue';
  /** 园区信息 表单 */
  defineOptions({ name: 'ParkMainForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    parkCode: undefined,
    parkName: undefined,
    sourceCode: undefined,
    deptId: undefined,
    provincesId: undefined,
    provincesCode: undefined,
    provincesName: undefined,
    cityId: undefined,
    cityCode: undefined,
    cityName: undefined,
    districtId: undefined,
    districtCode: undefined,
    districtName: undefined,
    townId: undefined,
    townCode: undefined,
    townName: undefined,
    proviceCityArea: undefined,
    addressDetail: undefined,
    parkArea: undefined,
    buildingArea: undefined,
    certificateArea: undefined,
    leaseArea: undefined,
    saleArea: undefined,
    parkingLot: undefined,
    greeningRate: undefined,
    monitoringCoverage: undefined,
    developers: undefined,
    contractor: undefined,
    status: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined
  });
  /** 表单验证规则 */
  const rules = reactive({
    status: [
      {
        required: true,
        message: '园区状态 1启用，0禁用不能为空',
        trigger: 'blur'
      }
    ],
    parkName: [
      {
        required: true,
        message: '园区名称不能为空',
        trigger: 'blur'
      }
    ],
    townId: [
      {
        required: true,
        message: '区域不能为空',
        trigger: 'change'
      }
    ]
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef.value) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        await ParkMainApi.createParkMain(form);
        message.success(t('common.createSuccess'));
      } else {
        await ParkMainApi.updateParkMain(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await ParkMainApi.getParkMain(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>

<style scoped>
  .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
  }

  .form-grid .el-form-item {
    margin-bottom: 18px;
  }
</style>
