<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="园区编码" prop="parkCode">
              <el-input
                v-model.trim="queryParams.parkCode"
                placeholder="请输入园区编码"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="园区名称" prop="parkName">
              <el-input
                v-model.trim="queryParams.parkName"
                placeholder="请输入园区名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="6" :sm="12" :xs="24">
            <el-form-item label="状态">
              <dict-data
                :code="DICT_TYPE.INFO_COUNTRY_AREA_STATUS"
                type="select"
                v-model="queryParams.status"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            v-permission="['info:park-main:create']"
            :icon="Plus"
            @click="openEdit(null)"
          >
            新增
          </el-button>
          <el-button
            class="ele-btn-icon"
            v-permission="['info:park-main:export']"
            :icon="DownloadOutlined"
            @click="exportData"
            :loading="exportLoading"
          >
            导出
          </el-button>
        </template>
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.INFO_COUNTRY_AREA_STATUS"
            type="tag"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openEdit(row)"
            v-permission="['info:park-main:update']"
          >
            编辑
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['info:park-main:delete']"
          />
          <el-link
            :underline="false"
            type="primary"
            @click="removeBatch(row)"
            v-permission="['info:park-main:delete']"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <ParkMainForm v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup lang="ts">
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { DownloadOutlined } from '@/components/icons';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import download from '@/utils/download';
  import * as ParkMainApi from '@/api/info/parkmain';
  import ParkMainForm from './ParkMainForm.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE } from '@/utils/dict';
  /** 园区信息 列表 */
  defineOptions({ name: 'ParkMainIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    parkCode: undefined,
    parkName: undefined,
    sourceCode: undefined,
    deptId: undefined,
    provincesId: undefined,
    provincesCode: undefined,
    provincesName: undefined,
    cityId: undefined,
    cityCode: undefined,
    cityName: undefined,
    districtId: undefined,
    districtCode: undefined,
    districtName: undefined,
    townId: undefined,
    townCode: undefined,
    townName: undefined,
    proviceCityArea: undefined,
    addressDetail: undefined,
    parkArea: undefined,
    buildingArea: undefined,
    certificateArea: undefined,
    leaseArea: undefined,
    saleArea: undefined,
    parkingLot: undefined,
    greeningRate: undefined,
    monitoringCoverage: undefined,
    developers: undefined,
    contractor: undefined,
    status: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'parkCode',
      label: '园区编码',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'parkName',
      label: '园区名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'proviceCityArea',
      label: '省市区',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'addressDetail',
      label: '详细地址',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'parkArea',
      label: '园区面积',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'buildingArea',
      label: '建筑面积',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'certificateArea',
      label: '产证面积',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'leaseArea',
      label: '租赁面积',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'saleArea',
      label: '销售面积',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'parkingLot',
      label: '车位个数',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'greeningRate',
      label: '绿化率',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'monitoringCoverage',
      label: '监控覆盖率',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'developers',
      label: '开发商',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'contractor',
      label: '承建商',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'status',
      label: '园区状态',
      align: 'center',
      minWidth: 110,
      slot: 'status'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return ParkMainApi.getParkMainPage({ ...where, ...filters, ...pages });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await ParkMainApi.deleteParkMain(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data = await ParkMainApi.exportParkMain(queryParams);
      download.excel(data, '园区信息.xls');
    } catch {
    } finally {
      exportLoading.value = false;
    }
  };
</script>
