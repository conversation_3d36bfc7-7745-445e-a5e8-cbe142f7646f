<template>
  <ele-modal
    form
    :width="680"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      v-loading="loading"
    >
      <el-form-item label="播放编码" prop="playCode">
        <el-input disabled v-model="form.playCode" placeholder="自动生成" />
      </el-form-item>
      <el-form-item label="播放名称" prop="playName">
        <el-input v-model="form.playName" placeholder="请输入播放名称" />
      </el-form-item>
      <el-form-item label="播放类型" prop="playType">
        <el-select v-model="form.playType" placeholder="请选择播放类型">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.INFO_PLAY_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="显示顺序" prop="sort">
        <el-input v-model="form.sort" placeholder="请输入显示顺序" />
      </el-form-item>
      <el-form-item label="播放状态" prop="status">
        <el-select v-model="form.status" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(
              DICT_TYPE.INFO_COUNTRY_AREA_STATUS
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import * as PlayMainApi from '@/api/info/playmain';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';

  /** 播放信息 表单 */
  defineOptions({ name: 'PlayMainForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    playCode: undefined,
    playName: undefined,
    playType: undefined,
    sourceCode: undefined,
    deptId: undefined,
    sort: undefined,
    status: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined
  });
  /** 表单验证规则 */
  const rules = reactive({
    playName: [
      {
        required: true,
        message: '播放名称不能为空',
        trigger: 'blur'
      }
    ],
    sort: [
      {
        required: true,
        message: '显示顺序不能为空',
        trigger: 'blur'
      }
    ],
    status: [
      {
        required: true,
        message: '播放状态 1启用，0禁用不能为空',
        trigger: 'blur'
      }
    ]
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        await PlayMainApi.createPlayMain(form);
        message.success(t('common.createSuccess'));
      } else {
        await PlayMainApi.updatePlayMain(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await PlayMainApi.getPlayMain(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
