<template>
  <ele-modal
    form
    :width="680"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      v-loading="loading"
    >
      <el-form-item label="地区编码" prop="areaCode">
        <el-input v-model="form.areaCode" placeholder="请输入地区编码" />
      </el-form-item>
      <el-form-item label="地区名称" prop="areaName">
        <el-input v-model="form.areaName" placeholder="请输入地区名称" />
      </el-form-item>
      <el-form-item label="来源，默认-MANUAL-手动创建" prop="sourceCode">
        <el-input
          v-model="form.sourceCode"
          placeholder="请输入来源，默认-MANUAL-手动创建"
        />
      </el-form-item>
      <el-form-item label="上级地区id，国家层级为空" prop="parentId">
        <el-input
          v-model="form.parentId"
          placeholder="请输入上级地区id，国家层级为空"
        />
      </el-form-item>
      <el-form-item
        label="级别 0-国家，1-省，2-市，3-区，4-街道&镇"
        prop="level"
      >
        <el-input
          v-model="form.level"
          placeholder="请输入级别 0-国家，1-省，2-市，3-区，4-街道&镇"
        />
      </el-form-item>
      <el-form-item label="级别id路径 以|分割" prop="levelIdPath">
        <el-input
          v-model="form.levelIdPath"
          placeholder="请输入级别id路径 以|分割"
        />
      </el-form-item>
      <el-form-item label="级别code路径 以|分割" prop="levelCodePath">
        <el-input
          v-model="form.levelCodePath"
          placeholder="请输入级别code路径 以|分割"
        />
      </el-form-item>
      <el-form-item label="部门ID" prop="deptId">
        <el-input v-model="form.deptId" placeholder="请输入部门ID" />
      </el-form-item>
      <el-form-item label="状态 1启用，0禁用" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备用字段1" prop="atributeVarchar1">
        <el-input
          v-model="form.atributeVarchar1"
          placeholder="请输入备用字段1"
        />
      </el-form-item>
      <el-form-item label="备用字段2" prop="atributeVarchar2">
        <el-input
          v-model="form.atributeVarchar2"
          placeholder="请输入备用字段2"
        />
      </el-form-item>
      <el-form-item label="备用字段3" prop="atributeVarchar3">
        <el-input
          v-model="form.atributeVarchar3"
          placeholder="请输入备用字段3"
        />
      </el-form-item>
      <el-form-item label="备用字段4" prop="atributeVarchar4">
        <el-input
          v-model="form.atributeVarchar4"
          placeholder="请输入备用字段4"
        />
      </el-form-item>
      <el-form-item label="备用字段5" prop="atributeVarchar5">
        <el-input
          v-model="form.atributeVarchar5"
          placeholder="请输入备用字段5"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import * as CountryAreaApi from '@/api/info/countryarea';
  import TinymceEditor from '@/components/TinymceEditor/index.vue';
  import { useFormData } from '@/utils/use-form-data';

  /** 国家地区信息 表单 */
  defineOptions({ name: 'CountryAreaForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    areaCode: undefined,
    areaName: undefined,
    sourceCode: undefined,
    parentId: undefined,
    level: undefined,
    levelIdPath: undefined,
    levelCodePath: undefined,
    deptId: undefined,
    status: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined
  });
  /** 表单验证规则 */
  const rules = reactive({
    status: [
      { required: true, message: '状态 1启用，0禁用不能为空', trigger: 'blur' }
    ]
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        await CountryAreaApi.createCountryArea(form);
        message.success(t('common.createSuccess'));
      } else {
        await CountryAreaApi.updateCountryArea(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await CountryAreaApi.getCountryArea(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
