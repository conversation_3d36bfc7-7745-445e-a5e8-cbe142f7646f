<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ paddingBottom: '2px' }">
    <el-form label-width="72px" @keyup.enter="search" @submit.prevent="">
      <el-row :gutter="6">
        <el-col :lg="6" :md="6" :sm="12" :xs="24">
          <el-form-item label="地区编码">
            <el-input
              clearable
              v-model.trim="form.areaCode"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="6" :sm="12" :xs="24">
          <el-form-item label="地区名称">
            <el-input
              clearable
              v-model.trim="form.areaName"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="6" :sm="12" :xs="24">
          <el-form-item label="状态">
            <dict-data
              :code="DICT_TYPE.INFO_COUNTRY_AREA_STATUS"
              type="select"
              v-model="form.status"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="6" :sm="24" :xs="24">
          <el-form-item label-width="16px" style="text-align: right">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE } from '@/utils/dict';

  const emit = defineEmits(['search']);

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    areaCode: '',
    areaName: '',
    status: void 0
  });

  /** 搜索 */
  const search = () => {
    emit('search', { ...form });
  };

  /** 重置 */
  const reset = () => {
    resetFields();
    search();
  };
</script>
