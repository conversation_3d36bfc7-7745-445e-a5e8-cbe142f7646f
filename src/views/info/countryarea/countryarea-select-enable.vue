<!-- 部门选择下拉框 -->
<template>
  <el-tree-select
    clearable
    :data="data"
    node-key="id"
    :props="{ label: 'areaName' }"
    v-model="selectedValue"
    placeholder="请选择"
    class="ele-fluid"
    :model-value="modelValue"
    @update:modelValue="updateValue"
  />
</template>

<script setup>
  import { ref } from 'vue';
  import { toTree } from 'sirius-platform-pro/es';
  import { getCountryAreaSimpleList } from '@/api/info/countryarea';

  const emit = defineEmits(['update:modelValue']);

  defineProps({
    /** 选中的地区 */
    modelValue: [Number, String],
    /** 提示信息 */
    placeholder: {
      type: String,
      default: '请选择上级地区'
    }
  });

  /** 地区数据 */
  const data = ref([]);

  /** 选中值 */
  const selectedValue = ref([]);

  /** 更新选中数据 */
  const updateValue = (value) => {
    emit('update:modelValue', value);
  };
  onMounted(() => {
    getCountryAreaSimpleList().then((list) => {
      data.value = toTree({
        data: list,
        idField: 'id',
        parentIdField: 'parentId'
      });
    });
  });
</script>
