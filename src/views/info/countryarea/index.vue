<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <CountryAreaSearch @search="reload" />
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        sticky
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        highlight-current-row
        :default-expand-all="true"
        :pagination="false"
        cache-key="infoCountryAreaTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            class="ele-btn-icon"
            :icon="ColumnHeightOutlined"
            @click="expandAll"
          >
            展开全部
          </el-button>
          <el-button
            class="ele-btn-icon"
            :icon="VerticalAlignMiddleOutlined"
            @click="foldAll"
          >
            折叠全部
          </el-button>
        </template>
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.INFO_COUNTRY_AREA_STATUS"
            type="tag"
            :model-value="row.status"
          />
        </template>
        <template #level="{ row }">
          <dict-data
            :code="DICT_TYPE.INFO_COUNTRY_AREA_LEVEL"
            type="tag"
            :model-value="row.level"
          />
        </template>
        <template #action="{ row }">
          <div style="display: inline-flex; align-items: center">
            <el-link
              type="primary"
              :underline="false"
              @click="openEdit(null, row.id)"
            >
              添加
            </el-link>
            <el-divider direction="vertical" style="margin: 0 8px" />
            <el-link type="primary" :underline="false" @click="openEdit(row)">
              修改
            </el-link>
            <el-divider direction="vertical" style="margin: 0 8px" />
            <template v-if="row.status === 0">
              <el-link
                type="primary"
                :underline="false"
                @click="enableArea(row)"
              >
                启用
              </el-link>
              <el-divider direction="vertical" style="margin: 0 8px" />
              <el-link
                type="primary"
                :underline="false"
                @click="enableAreaAll(row)"
              >
                全部启用
              </el-link>
              <el-divider direction="vertical" style="margin: 0 8px" />
            </template>
            <template v-else-if="row.status === 1">
              <el-link
                type="primary"
                :underline="false"
                @click="disableArea(row)"
              >
                禁用
              </el-link>
              <el-divider direction="vertical" style="margin: 0 8px" />
            </template>
            <el-link type="danger" :underline="false" @click="remove(row)">
              删除
            </el-link>
          </div>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <CountryAreaEdit
      v-model="showEdit"
      :data="current"
      :parent-id="parentId"
      @done="reload"
    />
  </ele-page>
</template>

<script setup>
  import { ref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage, toTree } from 'sirius-platform-pro/es';
  import {
    PlusOutlined,
    ColumnHeightOutlined,
    VerticalAlignMiddleOutlined
  } from '@/components/icons';
  import CountryAreaSearch from '@/views/info/countryarea/countryarea-search.vue';
  import CountryAreaEdit from '@/views/info/countryarea/countryarea-edit.vue';
  import {
    deleteCountryArea,
    getCountryAreaList,
    enabledCountryArea,
    disabledCountryArea
  } from '@/api/info/countryarea';
  import { DICT_TYPE } from '@/utils/dict';
  import { dateFormatter } from '@/utils/formatTime';

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'areaCode',
      label: '地区编码',
      minWidth: 100
    },
    {
      prop: 'areaName',
      label: '地区名称',
      minWidth: 160
    },
    {
      prop: 'level',
      label: '级别',
      minWidth: 50,
      slot: 'level'
    },
    {
      prop: 'status',
      label: '状态',
      align: 'center',
      slot: 'status',
      minWidth: 50
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 110,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 250,
      align: 'center',
      slot: 'action',
      hideInPrint: true
    }
  ]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 上级菜单id */
  const parentId = ref();

  /** 表格数据源 */
  const datasource = async ({ where }) => {
    const data = await getCountryAreaList({ ...where });
    return toTree({
      data,
      idField: 'id',
      parentIdField: 'parentId'
    });
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ where });
  };

  /** 打开编辑弹窗 */
  const openEdit = (row, id) => {
    current.value = row ?? null;
    parentId.value = id;
    showEdit.value = true;
  };

  /** 删除单个 */
  const remove = (row) => {
    ElMessageBox.confirm(
      `是否确认删除名称为“${row.areaName}”的数据项？`,
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        deleteCountryArea(row.id)
          .then(() => {
            loading.close();
            EleMessage.success('删除成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 展开全部 */
  const expandAll = () => {
    tableRef.value?.toggleRowExpansionAll?.(true);
  };

  /** 折叠全部 */
  const foldAll = () => {
    tableRef.value?.toggleRowExpansionAll?.(false);
  };

  /** 启用地区 */
  const enableArea = (row) => {
    ElMessageBox.confirm(
      `是否确认启用名称为“${row.areaName}”的数据项？`,
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        enabledCountryArea({ id: row.id, operaMethod: 'ONE' })
          .then(() => {
            loading.close();
            EleMessage.success('启用成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 禁用地区 */
  const disableArea = (row) => {
    ElMessageBox.confirm(
      `是否确认禁用名称为“${row.areaName}”的数据项？该禁用会连带禁用子级地区`,
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        disabledCountryArea({ id: row.id })
          .then(() => {
            loading.close();
            EleMessage.success('禁用成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 启用全部地区 */
  const enableAreaAll = (row) => {
    ElMessageBox.confirm(
      `是否确认启用名称为“${row.areaName}”的数据项？`,
      '系统提示，该操作会连带启用子级地区 以及子级地区的子级地区',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        enabledCountryArea({ id: row.id, operaMethod: 'ALL' })
          .then(() => {
            loading.close();
            EleMessage.success('启用成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
</script>

<script>
  export default {
    name: 'SystemDept'
  };
</script>
