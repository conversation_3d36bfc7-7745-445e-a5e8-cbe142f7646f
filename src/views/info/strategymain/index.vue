<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="策略编码" prop="strategyCode">
              <el-input
                v-model.trim="queryParams.strategyCode"
                placeholder="请输入策略编码"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="策略名称" prop="strategyName">
              <el-input
                v-model.trim="queryParams.strategyName"
                placeholder="请输入策略名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="来源" prop="sourceCode">
              <el-input
                v-model.trim="queryParams.sourceCode"
                placeholder="请输入来源"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="部门ID" prop="deptId">
              <el-input
                v-model.trim="queryParams.deptId"
                placeholder="请输入部门ID"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="显示顺序" prop="sort">
              <el-input
                v-model.trim="queryParams.sort"
                placeholder="请输入显示顺序"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="状态">
              <dict-data
                :code="DICT_TYPE.INFO_COUNTRY_AREA_STATUS"
                type="select"
                v-model="queryParams.status"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            v-permission="['info:strategy-main:create']"
            :icon="Plus"
            @click="openEdit(null)"
          >
            新增
          </el-button>
          <el-button
            class="ele-btn-icon"
            v-permission="['info:strategy-main:export']"
            :icon="DownloadOutlined"
            @click="exportData"
            :loading="exportLoading"
          >
            导出
          </el-button>
        </template>
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.INFO_COUNTRY_AREA_STATUS"
            type="tag"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link :underline="false" type="primary" @click="openEdit(row)">
            编辑
          </el-link>
          <el-divider direction="vertical" />
          <el-link :underline="false" type="primary" @click="removeBatch(row)">
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <StrategyMainForm v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { DownloadOutlined } from '@/components/icons';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import download from '@/utils/download';
  import {
    getStrategyMainPage,
    exportStrategyMain,
    deleteStrategyMain
  } from '@/api/info/strategymain';
  import StrategyMainForm from './StrategyMainForm.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE } from '@/utils/dict';

  /** 策略信息 列表 */
  defineOptions({ name: 'StrategyMainIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    strategyCode: undefined,
    strategyName: undefined,
    sourceCode: undefined,
    deptId: undefined,
    sort: undefined,
    status: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'strategyCode',
      label: '策略编码',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'strategyName',
      label: '策略名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'sort',
      label: '显示顺序',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'status',
      label: '状态',
      align: 'center',
      minWidth: 110,
      slot: 'status'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 180,
      formatter: dateFormatter
    },
    {
      prop: 'action',
      label: '操作',
      align: 'center',
      width: 120,
      fixed: 'right',
      slot: 'action'
    }
  ]);

  /** 数据源 */
  const datasource = async ({ page, limit }) => {
    const params = {
      ...queryParams,
      pageNo: page,
      pageSize: limit
    };
    return await getStrategyMainPage(params);
  };

  /** 刷新 */
  const reload = () => {
    tableRef.value?.reload();
  };

  /** 导出加载状态 */
  const exportLoading = ref(false);

  /** 导出数据 */
  const exportData = async () => {
    exportLoading.value = true;
    try {
      const data = await exportStrategyMain(queryParams);
      download.excel(data, '策略信息.xls');
      message.success('导出成功');
    } finally {
      exportLoading.value = false;
    }
  };

  /** 编辑弹窗显示状态 */
  const showEdit = ref(false);

  /** 当前编辑的数据 */
  const current = ref(null);

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row;
    showEdit.value = true;
  };

  /** 批量删除 */
  const removeBatch = async (row) => {
    try {
      await message.delConfirm();
      await deleteStrategyMain(row.id);
      message.success('删除成功');
      reload();
    } catch {}
  };
</script>
