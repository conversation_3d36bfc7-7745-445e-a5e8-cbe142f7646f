<template>
  <ele-modal
    form
    :width="'90vw'"
    :height="'85vh'"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      v-loading="loading"
    >
      <div class="form-grid">
        <el-form-item label="策略编码" prop="strategyCode">
          <el-input
            disabled
            v-model="form.strategyCode"
            placeholder="自动生成"
          />
        </el-form-item>
        <el-form-item label="策略名称" prop="strategyName">
          <el-input v-model="form.strategyName" placeholder="请输入策略名称" />
        </el-form-item>
        <el-form-item label="显示顺序" prop="sort">
          <el-input v-model="form.sort" placeholder="请输入显示顺序" />
        </el-form-item>
        <el-form-item label="策略状态" prop="status">
          <el-select v-model="form.status" clearable placeholder="请选择状态">
            <el-option
              v-for="dict in getIntDictOptions(
                DICT_TYPE.INFO_COUNTRY_AREA_STATUS
              )"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </div>
    </el-form>
    <!-- 子表的表单 -->
    <div class="sub-form-container">
      <el-tabs v-model="subTabsName" class="sub-tabs">
        <el-tab-pane label="策略行" name="strategyPlay">
          <StrategyPlayForm ref="strategyPlayFormRef" :strategy-id="form.id" />
        </el-tab-pane>
      </el-tabs>
    </div>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { useMessage } from '@/hooks/web/useMessage';
  import {
    getStrategyMain,
    createStrategyMain,
    updateStrategyMain
  } from '@/api/info/strategymain';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import StrategyPlayForm from './components/StrategyPlayForm.vue';

  /** 策略信息 表单 */
  defineOptions({ name: 'StrategyMainForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    strategyCode: undefined,
    strategyName: undefined,
    sourceCode: undefined,
    deptId: undefined,
    sort: undefined,
    status: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined
  });

  /** 表单验证规则 */
  const rules = reactive({
    status: [
      {
        required: true,
        message: '策略状态 1启用，0禁用不能为空',
        trigger: 'blur'
      }
    ]
  });

  /** 子表的表单 */
  const subTabsName = ref('strategyPlay');
  const strategyPlayFormRef = ref();

  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef.value) return;
    const valid = await formRef.value.validate();
    if (!valid) return;

    // 校验子表单
    try {
      await strategyPlayFormRef.value.validate();
    } catch (e) {
      subTabsName.value = 'strategyPlay';
      return;
    }

    // 提交请求
    loading.value = true;
    try {
      const data = { ...form, sourceCode: 'MANUAL' };
      // 拼接子表的数据
      data.strategyPlays = strategyPlayFormRef.value.getData();

      // 转换开始时间和结束时间字段为时间戳
      if (data.strategyPlays && data.strategyPlays.length > 0) {
        data.strategyPlays = data.strategyPlays.map(play => {
          return {
            ...play,
            startTime: new Date(play.startTime).getTime(),
            endTime: new Date(play.endTime).getTime()
          };
        });
      }

      if (!isUpdate.value) {
        await createStrategyMain(data);
        message.success('新建成功');
      } else {
        await updateStrategyMain(data);
        message.success('修改成功');
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await getStrategyMain(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? '修改' : '新建';
    } finally {
      loading.value = false;
    }
  };
</script>

<style scoped>
  .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
  }

  .form-grid .el-form-item {
    margin-bottom: 18px;
  }

  .sub-form-container {
    margin-top: 20px;
    height: calc(85vh - 400px);
    overflow: hidden;
  }

  .sub-tabs {
    height: 100%;
  }

  .sub-tabs :deep(.el-tabs__content) {
    height: calc(100% - 40px);
    overflow: auto;
  }

  .sub-tabs :deep(.el-tab-pane) {
    height: 100%;
  }

  /* 确保表格不会超出容器 */
  .sub-tabs :deep(.el-table) {
    max-height: calc(100% - 60px);
  }
</style>
