<template>
  <div class="strategy-play-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      v-loading="formLoading"
      label-width="0px"
      :inline-message="true"
    >
      <el-table 
        :data="formData" 
        class="strategy-table"
        :key="tableKey"
        row-key="id"
      >
        <el-table-column label="序号" type="index" width="60" align="center" />
        <el-table-column label="优先级" min-width="100">
          <template #default="{ row, $index }">
            <el-form-item
              :prop="`${$index}.sort`"
              :rules="formRules.sort"
              class="mb-0px!"
            >
              <el-input-number
                v-model="row.sort"
                placeholder="请输入优先级"
                :min="0"
                :precision="0"
                controls-position="right"
                class="ele-fluid"
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="播放列表" min-width="120">
          <template #default="{ row, $index }">
            <el-select v-model="row.playId" placeholder="请选择">
              <el-option
                v-for="item in playList"
                :key="item.id"
                :label="item.playName"
                :value="item.id"
              />
            </el-select>            
          </template>
        </el-table-column>
        <el-table-column label="策略类型" min-width="120">
          <template #default="{ row, $index }">
            <el-form-item
              :prop="`${$index}.strategyType`"
              :rules="formRules.strategyType"
              class="mb-0px!"
            >
              <el-select
                v-model="row.strategyType"
                placeholder="请选择策略类型"
                clearable
              >
                <el-option label="定时" :value="0" />
                <el-option label="循环" :value="1" />
                <el-option label="长期" :value="3" />
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="开始时间" min-width="120">
          <template #default="{ row, $index }">
            <el-form-item
              :prop="`${$index}.startTime`"
              :rules="formRules.startTime"
              class="mb-0px!"
            >
              <el-date-picker
                v-model="row.startTime"
                type="datetime"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="选择开始时间"
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="结束时间" min-width="120">
          <template #default="{ row, $index }">
            <el-form-item
              :prop="`${$index}.endTime`"
              :rules="formRules.endTime"
              class="mb-0px!"
            >
              <el-date-picker
                v-model="row.endTime"
                type="datetime"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="选择结束时间"
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="播放间隔(秒)" min-width="120">
          <template #default="{ row, $index }">
            <el-form-item
              :prop="`${$index}.intervalTime`"
              :rules="formRules.intervalTime"
              class="mb-0px!"
            >
              <el-input-number
                v-model="row.intervalTime"
                placeholder="请输入播放间隔"
                :min="0"
                :precision="0"
                controls-position="right"
                class="ele-fluid"
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="状态" min-width="100">
          <template #default="{ row, $index }">
            <el-form-item
              :prop="`${$index}.status`"
              :rules="formRules.status"
              class="mb-0px!"
            >
              <el-select
                v-model="row.status"
                clearable
                placeholder="请选择状态"
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.INFO_COUNTRY_AREA_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column align="center" fixed="right" label="操作" width="80">
          <template #default="{ $index }">
            <el-button @click="handleDelete($index)" link type="danger">
              <el-icon><Delete /></el-icon>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <div class="add-button-container">
      <el-button @click="handleAdd" type="primary" :icon="Plus" round>
        添加策略行
      </el-button>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { ref, reactive, watch, nextTick } from 'vue';
  import { Plus, Delete } from '@element-plus/icons-vue';
  import { getStrategyPlayListByStrategyId } from '@/api/info/strategymain';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import * as PlayMainApi from '@/api/info/playmain';

  const props = defineProps<{
    strategyId: number; // 策略id（主表的关联字段）
  }>();

  const formLoading = ref(false); // 表单的加载中
  const formData = ref<any[]>([]);
  const formRules = reactive({
    playName: [
      { required: true, message: '播放名称不能为空', trigger: 'blur' }
    ],
    strategyType: [
      { required: true, message: '策略类型不能为空', trigger: 'change' }
    ],
    startTime: [
      { required: true, message: '开始时间不能为空', trigger: 'change' }
    ],
    endTime: [
      { required: true, message: '结束时间不能为空', trigger: 'change' }
    ],
    intervalTime: [
      { required: true, message: '播放间隔不能为空', trigger: 'blur' }
    ],
    sort: [{ required: true, message: '优先级不能为空', trigger: 'blur' }],
    status: [
      {
        required: true,
        message: '状态不能为空',
        trigger: 'change'
      }
    ]
  });
  const formRef = ref(); // 表单 Ref

  // 播放列表
  const playList = ref([]);

  // 表格key，用于强制重新渲染
  const tableKey = ref(0);

  /** 监听主表的关联字段的变化，加载对应的子表数据 */
  watch(
    () => props.strategyId,
    async (val) => {
      // 1. 重置表单
      formData.value = [];
      // 2. val 非空，则加载数据
      if (!val) {
        // 加载播放列表
        playList.value = await PlayMainApi.getPlayMainSimpleList({});
        return;
      }
      try {
        formLoading.value = true;
        const res = await getStrategyPlayListByStrategyId(val);
        // 格式化 startTime 和 endTime 为 YYYY-MM-DD HH24:MI:SS
        formData.value = res.map(play => ({
          ...play,
          startTime: play.startTime
            ? new Date(play.startTime).toISOString().replace('T', ' ').substring(0, 19)
            : play.startTime,
          endTime: play.endTime
            ? new Date(play.endTime).toISOString().replace('T', ' ').substring(0, 19)
            : play.endTime
        }));
        // 加载播放列表
        playList.value = await PlayMainApi.getPlayMainSimpleList({});
      } finally {
        formLoading.value = false;
      }
    },
    { immediate: true }
  );

  /** 新增按钮操作 */
  const handleAdd = () => {
    const row = {
      id: Date.now() + Math.random(), // 生成唯一ID
      strategyId: undefined,
      playId: undefined,
      playCode: undefined,
      playName: undefined,
      strategyType: undefined,
      startTime: undefined,
      endTime: undefined,
      intervalTime: undefined,
      sort: undefined,
      status: 1, // 默认启用
      atributeVarchar1: undefined,
      atributeVarchar2: undefined,
      atributeVarchar3: undefined,
      atributeVarchar4: undefined,
      atributeVarchar5: undefined
    };
    row.strategyId = props.strategyId as any;
    formData.value.push(row);
    
    // 强制重新渲染表格
    tableKey.value++;
    
    // 使用nextTick确保DOM更新后再滚动到底部
    nextTick(() => {
      const tableEl = document.querySelector('.strategy-table');
      if (tableEl) {
        tableEl.scrollTop = tableEl.scrollHeight;
      }
    });
  };

  /** 删除按钮操作 */
  const handleDelete = (index) => {
    formData.value.splice(index, 1);
  };

  /** 表单校验 */
  const validate = () => {
    return formRef.value.validate();
  };

  /** 表单值 */
  const getData = () => {
    return formData.value;
  };

  defineExpose({ validate, getData });
</script>

<style scoped>
  .strategy-play-form {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .strategy-table {
    flex: 1;
    overflow: auto;
    min-height: 200px;
  }

  .strategy-table :deep(.el-table__body-wrapper) {
    overflow-y: auto;
    max-height: none;
  }

  .strategy-table :deep(.el-table__body) {
    min-height: 100px;
  }

  .add-button-container {
    padding: 16px 0;
    text-align: center;
    border-top: 1px solid #ebeef5;
    background: #fafafa;
    flex-shrink: 0;
  }
</style>
