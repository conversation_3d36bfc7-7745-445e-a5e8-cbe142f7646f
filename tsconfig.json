{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "bundler", "strict": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "strictFunctionTypes": false, "jsx": "preserve", "baseUrl": "./", "allowJs": true, "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "noUnusedLocals": true, "noUnusedParameters": true, "experimentalDecorators": true, "lib": ["esnext", "dom"], "types": ["vite/client"], "typeRoots": ["./node_modules/@types/"], "noImplicitAny": false, "skipLibCheck": true, "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "components.d.ts", "vite.config.ts", "auto-imports.d.ts"], "exclude": ["node_modules", "dist", "**/*.js"]}